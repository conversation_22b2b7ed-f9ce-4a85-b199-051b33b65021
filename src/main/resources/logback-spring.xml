<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/comReaderLogs/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 完全禁用UCanAccess相关的错误日志 -->
    <logger name="net.ucanaccess" level="OFF" additivity="false"/>
    <logger name="net.ucanaccess.converters" level="OFF" additivity="false"/>
    <logger name="net.ucanaccess.converters.LoadJet" level="OFF" additivity="false"/>
    <logger name="net.ucanaccess.converters.Functions" level="OFF" additivity="false"/>
    <logger name="net.ucanaccess.converters.FunctionsAggregate" level="OFF" additivity="false"/>
    <logger name="net.ucanaccess.jdbc" level="OFF" additivity="false"/>

    <!-- 禁用HSQLDB相关的错误日志 -->
    <logger name="org.hsqldb" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.jdbc" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.error" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.Routine" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.ParserRoutine" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.ParserDDL" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.ParserCommand" level="OFF" additivity="false"/>
    <logger name="org.hsqldb.Session" level="OFF" additivity="false"/>

    <!-- 禁用Jackcess相关的警告 -->
    <logger name="com.healthmarketscience.jackcess" level="OFF" additivity="false"/>
    <logger name="com.healthmarketscience.jackcess.Index" level="OFF" additivity="false"/>

    <!-- 应用程序日志级别 -->
    <logger name="org.bj" level="INFO"/>

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
