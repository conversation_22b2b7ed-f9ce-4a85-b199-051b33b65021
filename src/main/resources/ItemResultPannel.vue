<template>
  <div>
    <div style="height: 80vh; display: flex; justify-content: center; align-items: center" v-if="!currentReg?.id">
      <a-empty description="项目结果录入面板" />
    </div>
    <template v-else>
      <a-alert :message="criticalTip" type="error" show-icon style="margin-bottom: 4px" v-if="criticalItems.length > 0" />

      <a-alert
        :message="'数据修改申请状态：' + changeApplyList[0].status"
        style="margin-bottom: 4px"
        :type="changeApplyList[0]?.status != '已同意' ? 'warning' : 'success'"
        v-if="changeApplyList.length > 0"
      >
        <template #action>
          <a-button size="small" type="text" @click="openItemResultChangeModal">查看</a-button>
        </template>
      </a-alert>
      <a-card size="small" style="padding: 0">
        <a-row :span="0">
          <a-col :span="22">
            <a-descriptions size="small" :column="4">
              <a-descriptions-item label="">
                <a-typography-text strong
                  >{{ currentReg.name ? currentReg.name : '' }}
                  {{ currentReg.examNo || '' }}
                  {{ currentReg.gender ? currentReg.gender : '' }}
                  {{ currentReg.age ? currentReg.age + currentReg.ageUnit : '' }}
                </a-typography-text>
              </a-descriptions-item>
              <a-descriptions-item label="体检类别">
                <a-typography-text strong>
                  {{ currentReg.examCategory ? currentReg.examCategory : '' }}
                </a-typography-text>
              </a-descriptions-item>
              <a-descriptions-item label="总检状态">
                <a-typography-text strong>{{ currentReg.summaryStatus || '' }}</a-typography-text>
              </a-descriptions-item>
              <a-descriptions-item label="危害因素" span="2">
                {{ currentReg.riskFactor ? currentReg.riskFactor_dictText : '' }}
              </a-descriptions-item>
              <a-descriptions-item label="工种">
                {{ currentReg.workType_dictText ? currentReg.workType_dictText : '' }}
              </a-descriptions-item>
              <a-descriptions-item label="车间">
                {{ currentReg.workShop ? currentReg.workShop : '' }}
              </a-descriptions-item>
              <!--                      <a-descriptions-item label="预约">{{ currentReg.companyRegName ? currentReg.companyRegName : '' }}</a-descriptions-item>
                <a-descriptions-item label="分组">{{ currentReg.teamName ? currentReg.teamName : '' }}</a-descriptions-item>-->
              <a-descriptions-item label="单位" :span="2">
                {{ currentReg.companyName ? currentReg.companyName : '' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="2">
            <a-flex justify="end">
              <div style="border: 1px solid #bfbfbf; padding: 2px">
                <a-image
                  :height="60"
                  :src="currentReg.customerAvatar ? getFileAccessHttpUrl(currentReg.customerAvatar) : defaultAvatar"
                  :fallback="defaultAvatar"
                />
              </div>
            </a-flex>
          </a-col>
        </a-row>
      </a-card>

      <a-card size="small" style="padding: 0; margin-top: 10px">
        <template #title>
          <ApartmentOutlined style="margin-right: 5px" />
          <a-typography-text>{{ currentDepart.departName }}</a-typography-text>
          <a-divider type="vertical" />
          <a-typography-text :type="currentDepart.autoSummary == '1' ? 'success' : 'secondary'"
            >{{ currentDepart.autoSummary == '1' ? '自动小结' : '手动小结' }}
          </a-typography-text>
        </template>
        <template #extra>
          <a-space>
            <a-button size="middle" type="dashed" @click="openInquiry">
              <SolutionOutlined />
              职业病问诊
            </a-button>
            <a-button size="middle" type="dashed" @click="openDataModal">
              <StockOutlined />
              历史数据对比
            </a-button>
            <a-spin :spinning="btnLoading">
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleMenuClick">
                    <a-menu-item key="fixLisData"> 更新检验数据</a-menu-item>
                    <a-menu-item key="fixCheckData"> 更新检查数据</a-menu-item>
                  </a-menu>
                </template>

                <a-button size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-spin>
            <a-button :loading="loading" :disabled="loading" size="middle" type="primary" @click="saveResult">
              <SaveOutlined />
              暂存
            </a-button>
          </a-space>
        </template>
        <a-spin :spinning="groupLoading">
          <div style="max-height: 50vh; overflow-y: scroll; padding: 0">
            <a-collapse v-model:activeKey="activeKey" collapsible="icon" size="small" style="padding: 0; margin: 4px">
              <a-collapse-panel key="1" :show-arrow="false" v-for="group in groupList" :data-group-id="group.itemGroupId">
                <template #header>
                  <a-tag color="red" v-if="group.payStatus != '已支付'">
                    {{ group.payStatus }}
                  </a-tag>
                  <span style="font-weight: bold">{{ group.departmentName }}</span>
                  <a-divider type="vertical" />
                  <span>{{ group.itemGroupName }}</span>
                  <span style="padding-left: 10px; color: red">{{ group.priceAfterDis }}元</span>
                  <a-divider type="vertical" />
                  <a-tag :color="group.checkStatusColor">{{ group.checkStatus }}</a-tag>
                  <a-tag color="red" v-if="group.abandonFlag == 1">已放弃</a-tag>
                  <a-typography-text
                    :type="group.comEquipmentStatus ? (group.comEquipmentStatus == 'success' ? 'success' : 'danger') : 'warning'"
                    v-if="group.comEquipmentTip"
                    >{{ group.comEquipmentTip }}
                  </a-typography-text>
                </template>
                <template #extra>
                  <template v-if="currentDepart?.autoSummary != '1' && (group.payStatus == '已支付' || autoCharge == '1')">
                    <a-space>
                      <a-tooltip title="查看图像" v-if="group.reportPics && group.reportPics.length > 0">
                        <EyeOutlined
                          :style="{ color: token.colorPrimary, fontSize: '16px', cursor: 'pointer' }"
                          @click="priewPic(group.reportPics)"
                        />
                      </a-tooltip>
                      <a-tooltip title="图像管理">
                        <FileAddOutlined
                          :style="{ color: token.colorPrimary, fontSize: '16px', cursor: 'pointer' }"
                          @click="openPicModal4Group(group)"
                        />
                      </a-tooltip>
                      <a-tooltip title="清除结果">
                        <ClearOutlined
                          @click="confirmClearGroupResult(group)"
                          :style="{ color: token.colorPrimary, fontSize: '20px', cursor: 'pointer' }"
                        />
                      </a-tooltip>
                      <a-tooltip title="放弃组合" v-if="group.abandonFlag == 0">
                        <DeleteOutlined
                          :style="{ color: token.colorError, fontSize: '20px', cursor: 'pointer' }"
                          @click="handelAbandonGroup(group)"
                        />
                      </a-tooltip>
                      <a-tooltip title="恢复组合数据" v-if="group.abandonFlag == 1">
                        <RollbackOutlined :style="{ color: token.colorPrimary, fontSize: '20px', cursor: 'pointer' }" @click="recoverGroup(group)" />
                      </a-tooltip>
                      <a-tooltip title="保存组合数据">
                        <SaveOutlined
                          @click="saveGroupItemResult(group)"
                          :style="{ color: token.colorPrimary, fontSize: '20px', cursor: 'pointer' }"
                        />
                      </a-tooltip>
                      <a-tooltip title="开始测试" v-if="shouldShowStartTestButton(group)">
                        <a-button
                          type="primary"
                          size="small"
                          @click="startDeviceTest(group)"
                          :loading="group.testingInProgress"
                          :style="{ marginLeft: '8px' }"
                        >
                          <template #icon>
                            <PlayCircleOutlined />
                          </template>
                          开始测试
                        </a-button>
                      </a-tooltip>
                      <a-tooltip title="查询结果" v-if="shouldShowQueryResultsButton(group)">
                        <a-button
                          type="default"
                          size="small"
                          @click="queryDeviceResults(group)"
                          :loading="group.queryingInProgress"
                          :style="{ marginLeft: '8px' }"
                        >
                          <template #icon>
                            <SearchOutlined />
                          </template>
                          查询结果
                        </a-button>
                      </a-tooltip>
                    </a-space>
                  </template>
                </template>
                <template v-if="group.payStatus == '已支付' || autoCharge == '1'">
                  <template v-for="item in group.itemList">
                    <a-row :gutter="[4, 4]" style="margin-bottom: 3px" justify="space-around" align="middle" :data-item-id="item.id">
                      <a-col :span="4">
                        <a-flex align="middle">
                          <span :class="{ abandon: item.itemResult.abandonFlag == 1 }">{{ item.name }}</span>
                          <span
                            :style="{ color: token.colorError }"
                            style="font-weight: bold; font-size: 16px; margin-left: 5px; margin-right: 5px"
                            v-if="item.requiredFlag"
                            >*</span
                          >
                          <a-typography-text type="danger" v-if="item.itemResult.abandonFlag == 1"> 已放弃 </a-typography-text>
                          <template v-if="item.itemResult?.criticalFlag">
                            <a-tag color="red">危急值{{ item.itemResult.criticalDegree ? ':' + item.itemResult.criticalDegree : '' }} </a-tag>
                          </template>
                        </a-flex>
                      </a-col>
                      <a-col :span="11">
                        <!-- 普通数值型项目 -->
                        <a-flex
                          v-if="
                            item.itemType == '数值型' ||
                            item.itemType == '计算型' ||
                            item.itemResult.valueType == '数值型' ||
                            item.itemResult.valueType == '计算型'
                          "
                        >
                          <a-input
                            style="width: 100%"
                            v-model:value="item.itemResult.value"
                            :disabled="item.itemResult.abandonFlag == 1 || currentDepart?.autoSummary == '1'"
                            allow-clear
                          >
                            <template #addonBefore>
                              <span v-if="item.itemType || item.itemResult.valueType">{{
                                item.itemType ? item.itemType : item.itemResult.valueType
                              }}</span>
                            </template>
                            <template #addonAfter>
                              <span v-if="item.unit || item.itemResult.unit">{{ item.unit ? item.unit : item.itemResult.unit }}</span>
                            </template>
                          </a-input>
                        </a-flex>
                        <a-flex v-else-if="item.itemType == '说明型' || item.itemResult.valueType == '说明型'">
                          <template v-if="item.itemResult?.abandonFlag == 1">
                            <a-textarea style="width: 100%" v-model:value="item.itemResult.value" disabled />
                          </template>
                          <template v-else>
                            <a-popover trigger="click" placement="top" @open-change="handleItemDictPop(item)">
                              <template #title>
                                <a-row>
                                  <a-col :span="20">
                                    <a-typography-text strong>项目字典</a-typography-text>
                                  </a-col>
                                  <a-col :span="4">
                                    <a-button size="small" @click="handelAddItemDict">添加字典 </a-button>
                                  </a-col>
                                </a-row>
                              </template>

                              <template #content>
                                <a-input-search placeholder="请输入关键字检索" @change="filterItemDict" enter-button />
                                <a-table
                                  :columns="itemDictColumns"
                                  :data-source="filtedItemDictList"
                                  size="small"
                                  :show-header="false"
                                  :pagination="false"
                                  :scroll="{ y: 200, x: 380 }"
                                >
                                  <template #bodyCell="{ column, text, record, index }">
                                    <template v-if="'operation' == column.dataIndex">
                                      <a-button type="primary" size="small" @click="click2UseItemDict(record, 'use')">使用 </a-button>
                                      <a-divider type="vertical" />
                                      <a-button size="small" @click="click2UseItemDict(record, 'append')">追加 </a-button>
                                    </template>
                                  </template>
                                </a-table>
                              </template>
                              <a-textarea
                                style="width: 100%"
                                v-model:value="item.itemResult.value"
                                :disabled="item.itemResult.abandonFlag == 1 || currentDepart?.autoSummary == '1'"
                                :auto-size="{ minRows: 1, maxRows: 3 }"
                              />
                            </a-popover>
                          </template>
                        </a-flex>
                        <a-flex v-else>
                          <a-input style="width: 100%" v-model:value="item.itemResult.value" readonly>
                            <template #addonBefore v-if="item.itemType">
                              <span>{{ item.itemType }}</span>
                            </template>
                            <template #addonAfter v-if="item.unit">
                              <span>{{ item.unit }}</span>
                            </template>
                          </a-input>
                        </a-flex>
                      </a-col>
                      <a-col :span="8">
                        <a-space>
                          <a-select
                            v-model:value="item.itemResult.abnormalFlag"
                            style="width: 100px; margin-left: 5px"
                            placeholder="异常标志"
                            :allow-clear="true"
                            :disabled="currentDepart?.autoSummary == '1'"
                          >
                            <a-select-option value="" key="2">重置</a-select-option>
                            <a-select-option value="0" key="0">正常</a-select-option>
                            <a-select-option value="1" key="1">异常</a-select-option>
                          </a-select>
                          <a-input
                            :disabled="currentDepart?.autoSummary == '1'"
                            v-model:value="item.itemResult.abnormalFlagDesc"
                            placeholder="异常描述"
                            :allow-clear="true"
                            style="width: 80px"
                          />
                          <a-input
                            :disabled="currentDepart?.autoSummary == '1'"
                            v-model:value="item.itemResult.abnormalSymbol"
                            placeholder="符号"
                            :allow-clear="true"
                            style="width: 60px"
                          />
                          <a-tooltip :title="item.normalRef || '无参考范围'">
                            <ColumnWidthOutlined />
                          </a-tooltip>
                          <a-space>
                            <a-tooltip title="放弃项目" v-if="!item.itemResult.abandonFlag || item.itemResult.abandonFlag == 0">
                              <DeleteOutlined
                                :style="{ color: token.colorError, fontSize: '16px', cursor: 'pointer' }"
                                @click="abandonItem(item, group)"
                              />
                            </a-tooltip>
                            <a-tooltip title="恢复项目" v-else>
                              <RollbackOutlined
                                :style="{ color: token.colorPrimary, fontSize: '16px', cursor: 'pointer' }"
                                @click="recover(item, group)"
                              />
                            </a-tooltip>
                          </a-space>
                        </a-space>
                      </a-col>
                    </a-row>
                  </template>
                </template>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-spin>
      </a-card>
      <a-card size="small" title="科室小结" style="padding: 0; margin-top: 10px">
        <a-form-item name="departSummary">
          <a-textarea v-model:value="characterSummary" placeholder="请输入科室小结" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
        <template #extra>
          <a-space>
            <a-button
              :loading="loading"
              :disabled="loading"
              size="middle"
              type="primary"
              @click="saveSummary"
              v-if="currentDepart?.autoSummary != '1'"
              >保存并生成小结
            </a-button>
            <a-button :loading="loading" :disabled="loading" size="middle" type="primary" @click="handleUpdateSummary">手动修改小结 </a-button>
            <a-button size="middle" type="dashed" danger @click="handleAudit" v-if="hasPermission('station:customer_reg_depart_summary:audit')"
              >审核
            </a-button>
            <a-popconfirm
              v-if="hasPermission('station:customer_reg_depart_summary:audit') && departSummary?.auditStatus == '通过'"
              title="确定要反审核吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleUnAudit"
            >
              <a-button size="middle" type="dashed" danger>反审核</a-button>
            </a-popconfirm>

            <a-popconfirm
              v-if="departSummary?.auditStatus != '通过'"
              title="确定要清除科室小结吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleRemoveSummary"
            >
              <a-button size="middle" type="dashed" danger>清除小结</a-button>
            </a-popconfirm>
            <a-button
              size="middle"
              type="dashed"
              danger
              @click="openItemResultChangeModal"
              v-if="!currentReg.summaryStatus || currentReg?.summaryStatus != '未总检'"
              >申请修改
            </a-button>
          </a-space>
        </template>
      </a-card>
      <a-float-button
        shape="square"
        description="提醒"
        type="primary"
        :style="{
          right: '24px',
        }"
        :badge="{ count: departmentTipCount, showZero: false }"
        @click="openDepartTipDrawer"
      >
        <template #icon>
          <AlertOutlined />
        </template>
      </a-float-button>
    </template>

    <upload-manage-modal :preview-file-list="fileList" title="图片管理" ref="registerUploadModal" @ok="handlePicChange" />
    <customer-reg-depart-tip-drawer
      ref="departTipRef"
      :regId="currentReg?.id"
      :departmentId="currentDepart.value?.id"
      @loaded="handleDepartTipDrawerLoaded"
    />
    <zy-inquiry-modal ref="inquiryModal" />
    <jimu-report-modal ref="jimuReportModal" title="历史结果" width="80%" :report-id="historyResultReportId" />
    <item-dict-modal ref="itemDictModal" @success="reloadItemDictList" />
    <item-result-change-apply-list ref="itemResultChangeApplyListRef" />
    <ItemResultChangeApplyModal ref="changeApplyModal" @success="reloadReg" />

    <!-- 依赖验证弹窗 -->
    <DependencyValidationModal
      ref="dependencyModal"
      @navigate="handleDependencyNavigation"
      @confirm="handleDependencyConfirm"
      @cancel="handleDependencyCancel"
    />
  </div>
</template>
<script lang="ts" setup>
  import { computed, onMounted, onUnmounted, provide, ref, unref, watch } from 'vue';
  import { CustomerRegCriticalItem, CustomerRegItemGroup, Depart, ICustomerReg, ICustomerRegItemResult, ItemDict, ItemInfo } from '#/types';
  import {
    abandon,
    abandonGroup,
    auditDepartSummary,
    clearGroupResult,
    fetchCheckData,
    fetchLisData,
    generateSummary,
    getCriticalItem,
    getSummary,
    listGroupByRegId,
    removeSummary,
    saveItemResult,
    saveReportPics,
    unAbandon,
    unAbandonGroup,
    unAuditDepartSummary,
    updateSummary,
  } from '@/views/station/Station.api';
  import { increaseUseCount, listByItemId } from '@/views/basicinfo/ItemDict.api';
  import { getDependenciesByGroupIds, getDependentItemResultsBatch } from '@/views/reg/CustomerReg.api';
  import { queryById as queryItemInfoById } from '@/views/basicinfo/ItemInfo.api';
  import { queryById as queryItemGroupById } from '@/views/basicinfo/ItemGroup.api';
  import DependencyChecker from '@/utils/DependencyChecker.js';
  import DependencyValidationModal from '@/components/DependencyValidationModal.vue';
  import { message, theme } from 'ant-design-vue';
  import UploadManageModal from '@/components/Upload/src/UploadManageModal.vue';
  import { preview } from 'vue3-image-preview';
  import { ModalOptionsPartial, useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import defaultAvatar from '/@/assets/images/defaultAvatar.png';
  import CustomerRegDepartTipDrawer from '@/views/station/CustomerRegDepartTipDrawer.vue';
  import {
    AlertOutlined,
    ApartmentOutlined,
    ClearOutlined,
    ColumnWidthOutlined,
    DeleteOutlined,
    DownOutlined,
    EyeOutlined,
    FileAddOutlined,
    PlayCircleOutlined,
    RollbackOutlined,
    SaveOutlined,
    SearchOutlined,
    SolutionOutlined,
    StockOutlined,
  } from '@ant-design/icons-vue';
  import { calculateExpression } from '@/utils/formulaUtils';
  import ZyInquiryModal from '@/views/occu/components/ZyInquiryModal.vue';
  import JimuReportModal from '@/components/Report/JimuReportModal.vue';
  import ItemDictModal from '@/views/basicinfo/components/ItemDictModal.vue';
  import { match } from 'pinyin-pro';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { usePermission } from '@/hooks/web/usePermission';
  import WebSocketService from '@/utils/websocketService';
  import { getSettingFromLocalStorage } from '@/utils/localStorageUtils.ts';
  import ItemResultChangeApplyList from '@/views/station/ItemResultChangeApplyList.vue';
  import ItemResultChangeApplyModal from '@/views/station/components/ItemResultChangeApplyModal.vue';
  import { getLatestChangeApplyList } from '@/views/station/ItemResultChangeApply.api';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';

  const emits = defineEmits(['success']);
  const { hasPermission } = usePermission();
  const { createConfirm, createErrorModal } = useMessage();
  const { useToken } = theme;
  const { token } = useToken();
  const loading = ref(false);
  const btnLoading = ref(false);
  /**体检人员列表部分*/
  const userStore = useUserStore();
  const user = userStore.getUserInfo;
  const currentDepart = ref({});
  const currentReg = ref<ICustomerReg>(null);
  const autoCharge = ref('0');
  const historyResultReportId = ref();

  function reloadReg() {
    emits('success');
  }

  /**图片*/
  const currentGroup = ref<CustomerRegItemGroup>({});

  const registerUploadModal = ref(null);
  const fileList = ref<string[]>([]);

  function openPicModal4Group(group: CustomerRegItemGroup) {
    currentGroup.value = group;
    fileList.value = group.reportPics || [];
    registerUploadModal.value?.open();
  }

  const priewPic = (urls) => {
    preview({
      images: urls.map((item) => getFileAccessHttpUrl(item)),
    });
  };

  function handlePicChange(urls: string[]) {
    //找出新增或删除的图片
    let addList = urls.filter((item) => !fileList.value.includes(item));
    let delList = fileList.value.filter((item) => !urls.includes(item));
    currentGroup.value.reportPics = urls;
    if (addList.length > 0 || delList.length > 0) {
      //更新对应项目的图片
      saveReportPics({ id: currentGroup.value.id, reportPics: urls });
    }
  }

  /**项目组合部分*/
  const departGroupList = ref([]);

  /**职业病问诊*/
  const inquiryModal = ref(null);

  function openInquiry() {
    inquiryModal.value?.open(currentReg.value, false);
  }

  const jimuReportModal = ref();

  function openDataModal() {
    jimuReportModal.value?.open({ archivesNum: currentReg.value.archivesNum });
  }

  function handleMenuClick(menu) {
    //console.log('handleMenuClick', menu);
    if (menu.key === 'fixLisData') {
      fixLisData();
    } else if (menu.key === 'fixCheckData') {
      fixCheckData();
    }
  }

  function fixLisData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchLisData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检验数据更新成功！');
          emits('success');
        } else {
          message.error('检验数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  function fixCheckData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchCheckData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检查数据数据更新成功！');
          emits('success');
        } else {
          message.error('检查数据数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  /**自动完成部分*/
  const mainItem4ItemDictId = ref<any>();
  provide('mainItem4ItemDictId', mainItem4ItemDictId);
  const itemDictModal = ref();
  const currentItem4AutoComplete = ref();
  const itemDictList = ref([]);
  const filtedItemDictList = ref([]);
  const itemDictColumns = [
    {
      title: '字典值',
      dataIndex: 'dictText',
      key: 'dictText',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 140,
    },
  ];

  function handleItemDictPop(item: ItemInfo) {
    currentItem4AutoComplete.value = item;
    listByItemId({ itemId: item.id }).then((res) => {
      itemDictList.value = res;
      filtedItemDictList.value = res;
    });
  }

  function reloadItemDictList() {
    if (currentItem4AutoComplete.value?.id) {
      listByItemId({ itemId: currentItem4AutoComplete.value.id }).then((res) => {
        itemDictList.value = res;
        filtedItemDictList.value = res;
      });
    }
  }

  function filterItemDict(e) {
    let value = e.target.value;
    //如果itemDictList不为空，且value不为空，过滤itemDictList
    if (!value) {
      filtedItemDictList.value = itemDictList.value;
      return;
    }
    if (itemDictList.value.length > 0 && value) {
      filtedItemDictList.value = itemDictList.value.filter((item) => {
        let wordMatched = item.dictText.toLowerCase().includes(value.toLowerCase());
        if (wordMatched) {
          return true;
        }
        let pinyinMatched = match(item.dictText, value, { continuous: true }) || [];
        return pinyinMatched.length > 0;
      });
    }
  }

  function click2UseItemDict(row: ItemDict, type) {
    if (currentItem4AutoComplete.value?.itemResult) {
      if (type == 'append') {
        let arr = currentItem4AutoComplete.value.itemResult.value ? currentItem4AutoComplete.value.itemResult.value.split('，') : [];
        arr.push(row.dictText);
        currentItem4AutoComplete.value.itemResult.value = Array.from(new Set(arr)).join('，');
      } else {
        currentItem4AutoComplete.value.itemResult.value = row.dictText;
      }
    }
    increaseUseCount({ dictId: row.id, count: 1 });
  }

  function handleItemResultAbnormalFlag(value: string, itemResult: ICustomerRegItemResult) {
    console.log('====vale====', value);
    //itemResult.abnormalFlag = value;
    //itemResult.abnormalFlagDesc = value == '0' ? '正常' : '异常';
    //itemResult.abnormalFlagDescManual = value == '0' ? '正常' : '异常';
    itemResult.abnormalFlagManual = '1';
  }

  function handelAddItemDict() {
    //打开字典维护界面
    mainItem4ItemDictId.value = currentItem4AutoComplete.value.id;
    itemDictModal.value?.add(currentItem4AutoComplete.value.itemResult.value || '');
  }

  /**科室提醒抽屉部分*/
  const departmentTipCount = ref(0);
  const departTipRef = ref(null);

  function openDepartTipDrawer() {
    departTipRef.value?.open();
  }

  function handleDepartTipDrawerLoaded(count: number) {
    departmentTipCount.value = count;
  }

  /**危急值提示部分*/
  const criticalItems = ref<CustomerRegCriticalItem[]>([]);

  function getCriticalItemByRegId(showTip: boolean = false) {
    getCriticalItem({ regId: currentReg.value.id }).then((res) => {
      if (showTip) {
        showCriticalItems(res, false);
      }
      criticalItems.value = res;
    });
  }

  const criticalTip = computed(() => {
    let tip = '';

    if (criticalItems.value.length == 0) {
      return tip;
    }
    const categoryA = criticalItems.value.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.value.filter((item) => item.severityDegree === 'B类');

    const messageA = categoryA.map((item, index) => `${index + 1}. ${item.itemName}`).join('；');
    const messageB = categoryB.map((item, index) => `${index + 1}. ${item.itemName}`).join('；');

    if (categoryA.length > 0) {
      tip += `发现${categoryA.length}项A类危急值：${messageA}；`;
    }
    if (categoryB.length > 0) {
      tip += `发现${categoryB.length}项B类危急值：${messageB}；`;
    }

    return tip;
  });

  /**科室小结部分*/
  const departSummary = ref(null);
  const characterSummary = ref('');

  /**串口设备问题*/
  const comEquipmentList = ref([]);
  const websocketMap = new Map<string, WebSocketService>();

  /**设备用户映射关系管理*/
  const deviceUserMapping = new Map<string, string>(); // deviceId -> userId

  function createCmdFunction(cmdCode: string): Function {
    return new Function('customerReg', cmdCode);
  }

  function createDataFunction(dataCode: string): Function {
    return new Function('group', 'comData', dataCode);
  }

  /**
   * 设备用户映射管理函数
   */

  /**
   * 注册设备到当前用户
   * @param deviceId 设备ID
   */
  function registerDeviceToCurrentUser(deviceId: string) {
    if (deviceId && user.id) {
      deviceUserMapping.set(deviceId, user.id);
      console.log(`设备 ${deviceId} 已注册到用户 ${user.id}`);
    }
  }

  /**
   * 验证设备是否属于当前用户
   * @param deviceId 设备ID
   * @returns boolean
   */
  function validateDeviceOwnership(deviceId: string): boolean {
    if (!deviceId || !user.id) {
      return false;
    }

    const mappedUserId = deviceUserMapping.get(deviceId);
    if (!mappedUserId) {
      // 如果没有映射关系，说明设备还未被任何用户占用，可以使用
      return true;
    }

    // 检查设备是否属于当前用户
    return mappedUserId === user.id;
  }

  /**
   * 释放设备映射关系
   * @param deviceId 设备ID
   */
  function releaseDeviceMapping(deviceId: string) {
    if (deviceId) {
      deviceUserMapping.delete(deviceId);
      console.log(`设备 ${deviceId} 的映射关系已释放`);
    }
  }

  /**
   * 清理当前用户的所有设备映射
   */
  function clearCurrentUserDeviceMappings() {
    const currentUserId = user.id;
    if (!currentUserId) return;

    for (const [deviceId, userId] of deviceUserMapping.entries()) {
      if (userId === currentUserId) {
        deviceUserMapping.delete(deviceId);
        console.log(`已清理用户 ${currentUserId} 的设备 ${deviceId} 映射关系`);
      }
    }
  }

  /**
   * 安全解析 extraConfig 字符串
   * @param configStr 配置字符串
   * @returns 解析后的配置对象
   */
  function parseExtraConfig(configStr: string): object {
    if (!configStr || configStr.trim() === '') {
      return {};
    }

    try {
      // 清理字符串
      let cleanedStr = configStr.trim();

      // 检查是否是双重编码
      if (cleanedStr.startsWith('"') && cleanedStr.endsWith('"')) {
        console.log('检测到双重编码，进行第一次解析');
        cleanedStr = JSON.parse(cleanedStr);
        console.log('第一次解析结果:', cleanedStr);
      }

      // 修复常见的 JSON 格式问题
      cleanedStr = fixCommonJsonIssues(cleanedStr);

      // 最终解析
      const parsed = JSON.parse(cleanedStr);

      // 确保返回对象类型
      if (!parsed || typeof parsed !== 'object') {
        console.warn('解析结果不是对象，使用默认空对象');
        return {};
      }

      return parsed;
    } catch (e) {
      console.error('❌ 解析 extraConfig 失败:', e);
      console.error('失败的字符串:', configStr);
      console.error(
        '字符详情:',
        [...configStr].map((char, i) => `${i}: '${char}' (${char.charCodeAt(0)})`)
      );
      return {};
    }
  }

  /**
   * 修复常见的 JSON 格式问题
   * @param jsonStr JSON 字符串
   * @returns 修复后的 JSON 字符串
   */
  function fixCommonJsonIssues(jsonStr: string): string {
    let fixed = jsonStr;

    // 1. 移除对象或数组最后的多余逗号
    // 匹配 },} 或 ,] 或 ,} 的情况
    fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

    // 2. 移除注释（// 或 /* */)
    fixed = fixed.replace(/\/\/.*$/gm, '');
    fixed = fixed.replace(/\/\*[\s\S]*?\*\//g, '');

    // 3. 修复单引号为双引号（仅针对属性名）
    fixed = fixed.replace(/'([^']+)'(\s*:)/g, '"$1"$2');

    console.log('JSON 修复:', {
      original: jsonStr,
      fixed: fixed,
      changed: jsonStr !== fixed,
    });

    return fixed;
  }

  function initComEquipment(groupList) {
    try {
      /*let comEquipments = [];
      groupList.value.forEach((group) => {
        if (group.checkStatus == '未检') {
          let comEquipment = comEquipmentList.value.find((item) => item.groupId == group.itemGroupId);
          if (comEquipment) {
            group.comEquipmentTip = '正在初始化串口设备...';
            group.comEquipmentStatus = '';
            comEquipments.push(comEquipment);
          }
        }
      });*/

      let comEquipments = [];
      groupList.value.forEach((group) => {
        if (group.checkStatus == '未检') {
          // 改为 filter() 查找所有匹配的设备
          let matchedEquipments = comEquipmentList.value.filter((item) => item.groupId == group.itemGroupId);
          if (matchedEquipments.length > 0) {
            group.comEquipmentTip = '正在初始化设备...';
            group.comEquipmentStatus = '';
            // 将所有匹配的设备都加入列表
            comEquipments.push(...matchedEquipments);
          }
        }
      });

      const groupedComEquipments = groupComEquipmentsByWebSocketUrl(comEquipments);
      //console.log('groupedComEquipments', groupedComEquipments);
      for (let [websocketUrl, comEquipments] of Object.entries(groupedComEquipments)) {
        const ws = websocketMap.get(websocketUrl) || new WebSocketService(websocketUrl);
        const eventHandlers = {
          connect: () => {
            console.log('成功连接到 WebSocket server');
            comEquipments.forEach((comEquipment) => {
              // 使用统一的解析函数
              let extraConfig = parseExtraConfig(comEquipment.extraConfig || '{}');

              // 调试信息
              console.log('🔍 设备配置调试:', {
                deviceName: comEquipment.name,
                portDescriptor: comEquipment.portDescriptor,
                originalConfig: comEquipment.extraConfig,
                parsedConfig: extraConfig,
                configKeys: Object.keys(extraConfig),
              });

              // 构建完整的 PhysicalExamConnectRequest 数据结构
              let serialPortDesc = buildPhysicalExamConnectRequest(comEquipment, extraConfig);

              ws?.sendMessage('connectPort', serialPortDesc);
            });
          },
          connect_error: (error) => {
            console.error('连接WebSocket服务器错误:', error);
            comEquipments.forEach((comEquipment) => {
              let group = groupList.value.find((group) => group.itemGroupId == comEquipment.groupId);
              if (group) {
                group.comEquipmentTip = '连接设备失败';
                group.comEquipmentStatus = 'error';
              }
            });
          },
          disconnect: () => {
            console.log('从WebSocket服务器断开连接');
            comEquipments.forEach((comEquipment) => {
              let group = groupList.value.find((group) => group.itemGroupId == comEquipment.groupId);
              if (group) {
                group.comEquipmentTip = '连接断开';
                group.comEquipmentStatus = 'error';
              }
            });
          },
          testResult: (resultData: any) => {
            console.log('接收到统一测试结果事件:', resultData);
            handleUnifiedTestResult(resultData, comEquipments);
          },
          queryResults: (response: any) => {
            console.log('接收到查询结果响应:', response);
            handleQueryResultsResponse(response, comEquipments);
          },
          queryResultsResponse: (response: any) => {
            console.log('接收到查询结果响应事件:', response);
            handleQueryResultsResponse(response, comEquipments);
          },
          deviceStatus: (status: any) => {
            console.log('设备状态:', status);

            let comEquipment = comEquipments.find((item) => item.deviceModel == status.deviceModel);

            if (!comEquipment) {
              console.warn('未找到对应的设备配置:', status.deviceId || status.port || status.connectionIdentifier);
              return;
            }

            let group = groupList.value.find((group) => group.itemGroupId == comEquipment.groupId);
            if (!group) {
              console.warn('未找到对应的项目组:', comEquipment.groupId);
              return;
            }

            // 处理统一设备状态
            if (status.isConnected) {
              // 设备连接成功，注册设备到当前用户
              if (status.deviceId) {
                registerDeviceToCurrentUser(status.deviceId);
              }

              group.comEquipmentStatus = 'success';
              group.comEquipmentTip = status.statusMessage || '设备连接成功';

              let cmdFunction = comEquipment.cmdFunction ? createCmdFunction(comEquipment.cmdFunction) : null;
              if (cmdFunction) {
                let cmdMessage = cmdFunction(currentReg.value);
                console.log('向设备发送指令：' + JSON.stringify(cmdMessage));
                if (cmdMessage) {
                  ws?.sendMessage('sendPortCmd', cmdMessage);
                }
              }
            } else {
              // 设备连接失败，释放设备映射关系
              if (status.deviceId) {
                releaseDeviceMapping(status.deviceId);
              }

              const errorMessage = status.statusMessage || status.errorMessage || '设备通信失败！';
              message.error(errorMessage);
              group.comEquipmentTip = errorMessage;
              group.comEquipmentStatus = 'error';
            }

            // 处理查询相关状态
            if (status.statusCode) {
              switch (status.statusCode) {
                case 'QUERY_STARTED':
                  group.comEquipmentTip = '开始查询测量结果...';
                  break;
                case 'QUERY_COMPLETED':
                  group.queryingInProgress = false;
                  if (status.hasResults) {
                    group.comEquipmentTip = '查询完成，找到测量结果';
                  } else {
                    group.comEquipmentTip = '查询完成，未找到测量结果';
                  }
                  break;
                case 'QUERY':
                  group.comEquipmentTip = status.statusMessage || '正在查询...';
                  break;
              }
            }
          },
          // 保持向后兼容性
          portStatus: (status: any) => {
            console.log('收到portStatus事件（已废弃），转发到deviceStatus处理');
            // 转换为统一格式并调用deviceStatus处理器
            const unifiedStatus = {
              deviceId: status.deviceId,
              port: status.port,
              connectionIdentifier: status.port,
              isConnected: status.open,
              statusMessage: status.message,
              errorMessage: status.error,
              open: status.open // 保持兼容性
            };
            eventHandlers.deviceStatus(unifiedStatus);
          },
          portCmdStatus: (status: any) => {
            console.log('Port command status:', status);
          },
          // 通用设备事件处理
          deviceConnectionError: (errorData: any) => {
            console.log('接收到设备连接错误事件:', errorData);
            handleDeviceConnectionError(errorData, comEquipments);
          },
          deviceConnected: (connectionData: any) => {
            console.log('接收到设备连接成功事件:', connectionData);
            handleDeviceConnected(connectionData, comEquipments);
          },
          measurementStarted: (measurementData: any) => {
            console.log('接收到测量开始事件:', measurementData);
            handleMeasurementStarted(measurementData, comEquipments);
          },
          deviceReset: (resetData: any) => {
            console.log('接收到设备重置事件:', resetData);
            handleDeviceReset(resetData, comEquipments);
          },
        };

        ws.connect(user.id, eventHandlers);
      }
    } catch (e) {
      console.log('初始化设备异常', e);
    }
  }

  function groupComEquipmentsByWebSocketUrl(comEquipments) {
    return comEquipments.reduce((acc, comEquipment) => {
      const { websocketUrl } = comEquipment;
      if (!acc[websocketUrl]) {
        acc[websocketUrl] = [];
      }
      acc[websocketUrl].push(comEquipment);
      return acc;
    }, {});
  }

  /**
   * 构建完整的 PhysicalExamConnectRequest 数据结构
   * @param comEquipment 设备配置
   * @param extraConfig 扩展配置
   * @returns PhysicalExamConnectRequest 对象
   */
  function buildPhysicalExamConnectRequest(comEquipment, extraConfig) {
    // 获取当前时间戳
    const currentTimestamp = Date.now();
    // 构建 ConnectRequest 数据结构
    return {
      priority: comEquipment.priority || 1,
      requestTimestamp: currentTimestamp,
      timeoutMs: extraConfig.timeoutMs || 30000,
      retryCount: extraConfig.retryCount || 3,
      retryIntervalMs: extraConfig.retryIntervalMs || 1000,

      // =================== 第二层：嵌套对象 ===================

      // 第二层对象1：PatientInfo（患者信息对象）
      patientInfo: buildPatientInfo(),

      // 第二层对象2：ExamItemInfo（体检项目信息对象）
      examItemInfo: buildExamItemInfo(comEquipment),

      // 第二层对象3：OperatorInfo（操作员信息对象）
      operatorInfo: buildOperatorInfo(),

      // 第二层对象4：DeviceProperties（设备属性对象）
      deviceProperties: buildDeviceProperties(comEquipment, extraConfig),
    };
  }

  /**
   * 构建患者信息对象
   * @returns PatientInfo 对象
   */
  function buildPatientInfo() {
    const reg = currentReg.value;
    if (!reg) {
      return {};
    }

    return {
      customerId: reg.customerId || '',
      customerRegId: reg.id || '',
      // 基本信息（必填）
      examNo: reg.examNo || '',
      name: reg.name || '',
      gender: reg.gender || '',
      age: reg.age || 0,
      birthDate: reg.birthday ? new Date(reg.birthday).toISOString().split('T')[0] : '',
      idCard: reg.idCard || '',
      phone: reg.phone || '',
    };
  }

  /**
   * 构建体检项目信息对象
   * @param comEquipment 设备配置
   * @returns ExamItemInfo 对象
   */
  function buildExamItemInfo(comEquipment) {
    const depart = currentDepart.value;

    // 查找与设备关联的项目组
    const relatedGroup = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);

    // 构建基本项目信息
    const examItemInfo = {
      // 基本信息（必填）
      customerRegId: currentReg.value.id || '',
      customerRegItemGroupId: relatedGroup?.id || '',
      itemId: comEquipment.groupId || '',
      itemName: relatedGroup?.itemGroupName || '',
      hisName: relatedGroup?.hisName || '',
      hisCode: relatedGroup?.hisCode || '',
      departmentId: depart?.id || '',
      departmentName: depart?.departName || '',
      value: '',
      unit: '',
      abnormalFlag: '',
      valueRefRange: '',
      dependentItems: [],
    };

    // 构建依赖项目信息（从预构建的缓存中获取）
    if (relatedGroup && relatedGroup.itemGroupId) {
      console.log(`为设备 ${comEquipment.name} 构建依赖项目，项目组ID: ${relatedGroup.itemGroupId}`);
      examItemInfo.dependentItems = buildDependentItems(relatedGroup.itemGroupId);
      console.log(`设备 ${comEquipment.name} 的完整ExamItemInfo:`, examItemInfo);
    } else {
      console.log(`设备 ${comEquipment.name} 没有找到关联的项目组`);
    }

    return examItemInfo;
  }

  /**
   * 构建依赖项目信息（优化版本）
   * @param groupId 主项目组ID
   * @returns 依赖项目列表
   */
  function buildDependentItems(groupId) {
    const startTime = performance.now();
    performanceStats.value.totalQueries++;

    // 1. 优先从集成查询结果中获取
    const group = groupList.value.find((g) => g.itemGroupId === groupId);
    if (group && group.dependentResults && group.dependentResults.length > 0) {
      performanceStats.value.cacheHits++;
      performanceStats.value.integratedQueryUsed = true;

      const endTime = performance.now();
      console.log(
        `从集成查询结果获取项目 ${groupId} 的依赖项目，数量: ${group.dependentResults.length}，耗时: ${(endTime - startTime).toFixed(2)}ms`
      );

      updatePerformanceStats();
      return group.dependentResults;
    }

    // 2. 降级到缓存方式
    const dependentItems = [];
    const dependencies = dependencyMap.value.get(groupId);
    if (!dependencies || dependencies.length === 0) {
      console.log(`项目 ${groupId} 没有依赖关系`);
      return dependentItems;
    }

    console.log(`从缓存构建项目 ${groupId} 的依赖项目，依赖数量: ${dependencies.length}`);

    // 3. 使用优化的查找策略
    const dependentItemsMap = buildDependentItemsMap();
    let cacheHitCount = 0;

    dependencies.forEach((dep) => {
      let dependentItem = null;

      if (dep.relationItemType === 'GROUP') {
        // 优先从缓存获取，然后从映射获取
        dependentItem = dependentItemsCache.value.get(dep.relationGroupId);
        if (dependentItem) {
          cacheHitCount++;
        } else {
          dependentItem = dependentItemsMap.groups.get(dep.relationGroupId);
        }
      } else if (dep.relationItemType === 'ITEM') {
        dependentItem = dependentItemsCache.value.get(dep.relationItemId);
        if (dependentItem) {
          cacheHitCount++;
        } else {
          dependentItem = dependentItemsMap.items.get(dep.relationItemId);
        }
      }

      if (dependentItem) {
        dependentItems.push(dependentItem);
      } else {
        console.warn(`无法找到依赖项目: ${dep.relationGroupName || dep.relationItemName}`);
      }
    });

    performanceStats.value.cacheHits += cacheHitCount;

    const endTime = performance.now();
    console.log(
      `项目 ${groupId} 构建完成的依赖项目: ${dependentItems.length}，缓存命中: ${cacheHitCount}/${dependencies.length}，耗时: ${(endTime - startTime).toFixed(2)}ms`
    );

    updatePerformanceStats();
    return dependentItems;
  }

  /**
   * 构建依赖项目映射（优化版本，一次性构建所有映射）
   * @returns 依赖项目映射对象
   */
  function buildDependentItemsMap() {
    // 使用缓存避免重复构建
    if (buildDependentItemsMap._cache) {
      return buildDependentItemsMap._cache;
    }

    const groupsMap = new Map();
    const itemsMap = new Map();

    // 一次遍历构建所有映射
    groupList.value.forEach((group) => {
      // 构建大项映射
      if (group.itemGroupId) {
        groupsMap.set(group.itemGroupId, {
          itemType: 'GROUP',
          itemId: group.itemGroupId,
          itemName: group.itemGroupName,
          value: getDependentGroupValue(group),
          unit: '',
          abnormalFlag: getDependentGroupAbnormalFlag(group),
          valueRefRange: '',
        });
      }

      // 构建小项映射
      if (group.itemList) {
        group.itemList.forEach((item) => {
          if (item.id) {
            itemsMap.set(item.id, {
              itemType: 'ITEM',
              customerRegItemGroupId: group.id,
              itemId: item.id,
              itemName: item.name,
              value: getDependentItemValue(item),
              unit: item.unit || '',
              abnormalFlag: getDependentItemAbnormalFlag(item),
              valueRefRange: item.normalRef || '',
            });
          }
        });
      }
    });

    const result = { groups: groupsMap, items: itemsMap };

    // 缓存结果，避免重复构建
    buildDependentItemsMap._cache = result;

    console.log(`构建依赖项目映射完成，大项: ${groupsMap.size}，小项: ${itemsMap.size}`);
    return result;
  }

  /**
   * 清除依赖项目映射缓存
   */
  function clearDependentItemsMapCache() {
    buildDependentItemsMap._cache = null;
  }

  /**
   * 更新性能统计
   */
  function updatePerformanceStats() {
    if (performanceStats.value.totalQueries > 0) {
      performanceStats.value.cacheHitRate = ((performanceStats.value.cacheHits / performanceStats.value.totalQueries) * 100).toFixed(2);
    }
  }

  /**
   * 重置性能统计
   */
  function resetPerformanceStats() {
    performanceStats.value = {
      lastLoadTime: 0,
      cacheHitRate: 0,
      totalQueries: 0,
      cacheHits: 0,
      integratedQueryUsed: false,
    };
  }

  /**
   * 获取性能统计报告
   */
  function getPerformanceReport() {
    return {
      ...performanceStats.value,
      dependencyMapSize: dependencyMap.value.size,
      cacheSize: dependentItemsCache.value.size,
      groupCount: groupList.value.length,
    };
  }

  /**
   * 从当前数据构建依赖项目信息（兼容旧版本）
   * @param dependency 依赖关系
   * @param type 类型：GROUP 或 ITEM
   * @returns 依赖项目信息
   */
  function buildDependentItemFromCurrentData(dependency, type) {
    const dependentItemsMap = buildDependentItemsMap();

    if (type === 'GROUP') {
      return dependentItemsMap.groups.get(dependency.relationGroupId) || null;
    } else if (type === 'ITEM') {
      return dependentItemsMap.items.get(dependency.relationItemId) || null;
    }

    console.warn(`无法从当前数据构建依赖项目: ${dependency.relationGroupName || dependency.relationItemName}`);
    return null;
  }

  /**
   * 判断是否应该显示"开始测试"按钮
   * @param group 项目组
   * @returns boolean
   */
  function shouldShowStartTestButton(group) {
    // 检查该项目组是否配置了串口设备
    const comEquipment = comEquipmentList.value.find((item) => item.groupId === group.itemGroupId);
    if (!comEquipment) {
      return false;
    }

    // 检查autoStartMeasurement是否为false
    if (comEquipment.autoStartMeasurement === 'Y' || comEquipment.autoStartMeasurement === true) {
      return false;
    }

    // 检查项目组状态是否为"未检"
    if (group.checkStatus !== '未检') {
      return false;
    }

    // 检查设备连接状态
    if (group.comEquipmentStatus !== 'success') {
      return false;
    }

    return true;
  }

  /**
   * 处理统一测试结果事件
   * @param resultData 统一测试结果数据
   * @param comEquipments 设备配置列表
   */
  function handleUnifiedTestResult(resultData: any, comEquipments: any[]) {
    try {
      console.log('处理统一测试结果:', resultData);

      // 验证必要字段 - 适配新的数据结构
      if (!resultData.deviceModel || !resultData.sessionId || !resultData.data) {
        console.error('结果数据不完整:', resultData);
        message.error('接收到的结果数据不完整');
        return;
      }

      // 验证患者信息是否匹配
      const patientId = resultData.examItemInfo?.customerRegId;
      if (patientId && patientId !== currentReg.value?.id ) {
        console.warn('患者信息不匹配:', {
          received: patientId,
          currentId: currentReg.value?.id,
          currentExamNo: currentReg.value?.examNo,
        });
        message.warning(`接收到的测试结果患者信息不匹配`);
        return;
      }

      // 验证设备用户映射关系（如果有deviceId）
      if (resultData.deviceId) {
        if (!validateDeviceOwnership(resultData.deviceId)) {
          const mappedUserId = deviceUserMapping.get(resultData.deviceId);
          console.warn(`设备 ${resultData.deviceId} 已被用户 ${mappedUserId} 占用，当前用户 ${user.id} 无法接收测试结果`);
          message.warning(`接收到其他用户设备的测试结果，已忽略`);
          return;
        }
      }

      // 根据数据存储类型处理
      const dataStorageType = resultData.dataStorageType || 'transfer';

      if (dataStorageType === 'transfer') {
        // 直接传输模式：使用现有的数据处理逻辑
        handleDirectTransferResult(resultData, comEquipments);
      } else if (dataStorageType === 'db') {
        // 数据库存储模式：显示提示并刷新数据
        handleDatabaseStorageResult(resultData, comEquipments);
      } else {
        console.warn('未知的数据存储类型:', dataStorageType);
        // 默认使用直接传输模式
        handleDirectTransferResult(resultData, comEquipments);
      }

    } catch (error) {
      console.error('处理统一测试结果异常:', error);
      message.error('处理测试结果异常：' + (error.message || JSON.stringify(error)));
    }
  }

  /**
   * 处理直接传输模式的测试结果
   * @param resultData 测试结果数据
   * @param comEquipments 设备配置列表
   */
  function handleDirectTransferResult(resultData: any, comEquipments: any[]) {
    try {
      console.log('处理直接传输模式测试结果:', resultData);

      // 根据deviceModel查找对应的设备配置
      const comEquipment = comEquipments.find((item) => item.deviceModel === resultData.deviceModel);

      if (!comEquipment) {
        console.error('未找到对应的设备配置:', resultData.deviceModel);
        message.error(`未找到设备型号为 ${resultData.deviceModel} 的配置`);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.error('未找到对应的项目组:', comEquipment.groupId);
        message.error('未找到对应的项目组配置');
        return;
      }

      // 检查体检状态
      if (group.checkStatus !== '未检') {
        console.warn('当前体检状态不是未检，无法处理测试结果:', group.checkStatus);
        message.error('当前体检状态不是未检，无法处理设备测试结果！');
        return;
      }

      // 获取数据解析函数
      const dataFunction = comEquipment.dataFunction ? createDataFunction(comEquipment.dataFunction) : null;
      if (!dataFunction) {
        console.error('未配置设备数据解析代码');
        message.error('未配置设备数据解析代码，无法处理测试结果！');
        return;
      }

      // 更新设备状态
      group.comEquipmentTip = '正在处理设备测试结果...';
      group.comEquipmentStatus = 'processing';

      // 调用数据解析函数处理测试结果
      dataFunction(group, resultData.data);

      // 更新状态
      const duration = resultData.duration ? Math.round(resultData.duration / 1000) : 0;
      group.comEquipmentTip = `测试结果处理完成${duration > 0 ? ` (耗时: ${duration}秒)` : ''}`;
      group.comEquipmentStatus = 'success';

      // 清除测试状态
      if (group.testingInProgress) {
        group.testingInProgress = false;
      }

      const patientName = resultData.patientInfo?.name || resultData.examItemInfo?.customerRegId || '患者';
      message.success(`成功接收并处理结果：${patientName}`);
      console.log('直接传输模式测试结果处理完成:', {
        patient: patientName,
        duration: resultData.duration,
        timestamp: new Date(resultData.timestamp).toLocaleString(),
      });

    } catch (error) {
      console.error('处理直接传输模式测试结果异常:', error);
      throw error; // 重新抛出异常，由上层处理
    }
  }

  /**
   * 处理数据库存储模式的测试结果
   * @param resultData 测试结果数据
   * @param comEquipments 设备配置列表
   */
  function handleDatabaseStorageResult(resultData: any, comEquipments: any[]) {
    try {
      console.log('处理数据库存储模式测试结果:', resultData);

      // 根据deviceModel查找对应的设备配置
      const comEquipment = comEquipments.find((item) => item.deviceModel === resultData.deviceModel);

      if (!comEquipment) {
        console.error('未找到对应的设备配置:', resultData.deviceModel);
        message.error(`未找到设备型号为 ${resultData.deviceModel} 的配置`);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.error('未找到对应的项目组:', comEquipment.groupId);
        message.error('未找到对应的项目组配置');
        return;
      }

      // 更新设备状态
      group.comEquipmentTip = '测试结果已保存到数据库，正在查询最新数据...';
      group.comEquipmentStatus = 'processing';

      // 清除测试状态
      if (group.testingInProgress) {
        group.testingInProgress = false;
      }

      const patientName = resultData.patientInfo?.name || resultData.examItemInfo?.customerRegId || '检客';

      // 显示成功提示
      message.success(`测试结果已保存到数据库：${patientName}`);

      // 单独查询当前大项的最新结果，避免影响其他未保存的大项
      refreshSingleGroupResult(group).then(() => {
        // 查询成功后更新状态
        group.comEquipmentTip = '测试结果已保存并更新完成';
        group.comEquipmentStatus = 'success';

        console.log('数据库存储模式测试结果处理完成:', {
          patient: patientName,
          groupId: group.itemGroupId,
          groupName: group.itemGroupName,
          duration: resultData.duration,
          timestamp: new Date(resultData.timestamp).toLocaleString(),
        });
      }).catch((error) => {
        console.error('查询大项结果失败:', error);
        group.comEquipmentTip = '测试结果已保存，但查询最新数据失败';
        group.comEquipmentStatus = 'warning';
        message.warning('测试结果已保存到数据库，但查询最新数据失败，请手动刷新');
      });

    } catch (error) {
      console.error('处理数据库存储模式测试结果异常:', error);
      throw error; // 重新抛出异常，由上层处理
    }
  }

  /**
   * 刷新单个项目组的结果数据
   * @param group 项目组
   * @returns Promise
   */
  async function refreshSingleGroupResult(group: any) {
    try {
      console.log('开始刷新单个项目组结果:', group.itemGroupName);

      // 使用现有的API查询当前体检记录的所有项目组，然后筛选出目标项目组
      const reqParam = {
        regId: currentReg.value.id,
        includeDependencies: false, // 不需要依赖信息，只查询结果数据
      };

      // 调用现有的API
      const result = await listGroupByRegId(reqParam);

      if (result && Array.isArray(result)) {
        // 查找目标项目组
        const updatedGroup = result.find(g => g.id === group.id || g.itemGroupId === group.itemGroupId);

        if (updatedGroup) {
          // 更新项目列表中的数据
          if (updatedGroup.itemList && Array.isArray(updatedGroup.itemList)) {
            updatedGroup.itemList.forEach(updatedItem => {
              const existingItem = group.itemList.find(item => item.id === updatedItem.id);
              if (existingItem && updatedItem.itemResult) {
                // 更新项目结果数据
                Object.assign(existingItem.itemResult, updatedItem.itemResult);
                console.log(`更新项目结果: ${existingItem.name}`, updatedItem.itemResult);
              }
            });
          }

          // 更新项目组状态
          if (updatedGroup.checkStatus) {
            group.checkStatus = updatedGroup.checkStatus;
          }

          // 更新其他可能的字段
          if (updatedGroup.abnormalFlag !== undefined) {
            group.abnormalFlag = updatedGroup.abnormalFlag;
          }
          if (updatedGroup.checkConclusion) {
            group.checkConclusion = updatedGroup.checkConclusion;
          }

          console.log('单个项目组结果刷新完成:', group.itemGroupName);
        } else {
          throw new Error(`未找到项目组: ${group.itemGroupName}`);
        }
      } else {
        throw new Error('查询项目组结果返回数据格式错误');
      }

    } catch (error) {
      console.error('刷新单个项目组结果失败:', error);
      throw error;
    }
  }

  /**
   * 处理设备测试结果事件（保持向后兼容）
   * @param resultData 测试结果数据
   * @param comEquipments 设备配置列表
   */
  function handleTestResult(resultData: any, comEquipments: any[]) {
    console.log('收到旧版testResult事件（向后兼容），数据:', resultData);

    // 检查是否为新格式的数据
    if (resultData.sessionId && resultData.examItemInfo) {
      // 新格式数据，转发到统一处理函数
      console.log('检测到新格式数据，转发到统一处理函数');
      handleUnifiedTestResult(resultData, comEquipments);
      return;
    }

    // 旧格式数据，保持原有处理逻辑
    try {
      console.log('处理旧格式设备测试结果:', resultData);

      // 验证必要字段
      if (!resultData.deviceModel || !resultData.patientId || !resultData.resultType || !resultData.data) {
        console.error('测试结果数据不完整:', resultData);
        message.error('接收到的测试结果数据不完整');
        return;
      }

      // 验证患者信息是否匹配
      if (resultData.patientId !== currentReg.value?.examNo) {
        console.warn('患者检查号不匹配:', {
          received: resultData.patientId,
          current: currentReg.value?.examNo,
        });
        message.warning(`接收到的测试结果患者信息不匹配：${resultData.patientName}(${resultData.patientId})`);
        return;
      }

      // 验证设备用户映射关系（如果有deviceId）
      if (resultData.deviceId) {
        if (!validateDeviceOwnership(resultData.deviceId)) {
          const mappedUserId = deviceUserMapping.get(resultData.deviceId);
          console.warn(`设备 ${resultData.deviceId} 已被用户 ${mappedUserId} 占用，当前用户 ${user.id} 无法接收测试结果`);
          message.warning(`接收到其他用户设备的测试结果，已忽略`);
          return;
        }
      }

      // 根据deviceModel查找对应的设备配置
      const comEquipment = comEquipments.find((item) => item.deviceModel === resultData.deviceModel);

      if (!comEquipment) {
        console.error('未找到对应的设备配置:', resultData.deviceModel);
        message.error(`未找到设备型号为 ${resultData.deviceModel} 的配置`);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.error('未找到对应的项目组:', comEquipment.groupId);
        message.error('未找到对应的项目组配置');
        return;
      }

      // 检查体检状态
      if (group.checkStatus !== '未检') {
        console.warn('当前体检状态不是未检，无法处理测试结果:', group.checkStatus);
        message.error('当前体检状态不是未检，无法处理设备测试结果！');
        return;
      }

      // 获取数据解析函数
      const dataFunction = comEquipment.dataFunction ? createDataFunction(comEquipment.dataFunction) : null;
      if (!dataFunction) {
        console.error('未配置设备数据解析代码');
        message.error('未配置设备数据解析代码，无法处理测试结果！');
        return;
      }

      // 更新设备状态
      group.comEquipmentTip = '正在处理设备测试结果...';
      group.comEquipmentStatus = 'processing';

      // 调用数据解析函数处理测试结果
      dataFunction(group, resultData.data);

      // 更新状态
      const duration = resultData.testDuration ? Math.round(resultData.testDuration / 1000) : 0;
      group.comEquipmentTip = `测试结果处理完成${duration > 0 ? ` (耗时: ${duration}秒)` : ''}`;
      group.comEquipmentStatus = 'success';

      // 清除测试状态
      if (group.testingInProgress) {
        group.testingInProgress = false;
      }

      const patientName = resultData.patientName || '患者';
      message.success(`成功接收并处理测试结果：${patientName}`);
      console.log('旧格式测试结果处理完成:', {
        patient: patientName,
        duration: resultData.testDuration,
        timestamp: resultData.timestamp ? new Date(resultData.timestamp).toLocaleString() : 'N/A',
      });
    } catch (error) {
      console.error('处理设备测试结果异常:', error);

      // 尝试更新对应组的状态
      const comEquipment = comEquipments.find((item) => item.deviceModel === resultData?.deviceModel);
      if (comEquipment) {
        const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
        if (group) {
          group.comEquipmentTip = '测试结果处理异常';
          group.comEquipmentStatus = 'error';
          if (group.testingInProgress) {
            group.testingInProgress = false;
          }
        }
      }

      message.error('处理设备测试结果异常：' + (error.message || JSON.stringify(error)));
    }
  }

  /**
   * 处理设备连接错误事件
   * @param errorData 错误数据
   * @param comEquipments 设备配置列表
   */
  function handleDeviceConnectionError(errorData: any, comEquipments: any[]) {
    try {
      console.log('处理设备连接错误:', errorData);

      // 查找对应的设备配置
      const comEquipment = findComEquipmentByDeviceModel(errorData.deviceModel, comEquipments);
      if (!comEquipment) {
        console.warn('未找到对应的设备配置:', errorData.deviceModel);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.warn('未找到对应的项目组:', comEquipment.groupId);
        return;
      }

      // 更新设备状态显示
      group.comEquipmentStatus = 'error';
      group.comEquipmentTip = errorData.error || `${errorData.deviceModel || '设备'}连接失败`;

      // 显示错误消息
      const deviceName = errorData.deviceModel || '设备';
      message.error(`${deviceName}连接失败: ${errorData.error || '未知错误'}`);

      console.log(`设备连接错误处理完成: ${group.itemGroupName} (${deviceName})`);
    } catch (error) {
      console.error('处理设备连接错误失败:', error);
    }
  }

  /**
   * 处理设备连接成功事件
   * @param connectionData 连接数据
   * @param comEquipments 设备配置列表
   */
  function handleDeviceConnected(connectionData: any, comEquipments: any[]) {
    try {
      console.log('处理设备连接成功:', connectionData);

      // 查找对应的设备配置
      const comEquipment = findComEquipmentByDeviceModel(connectionData.deviceModel, comEquipments);
      if (!comEquipment) {
        console.warn('未找到对应的设备配置:', connectionData.deviceModel);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.warn('未找到对应的项目组:', comEquipment.groupId);
        return;
      }

      // 更新设备状态显示
      group.comEquipmentStatus = 'success';
      const deviceName = connectionData.deviceModel || '设备';
      group.comEquipmentTip = `${deviceName}连接成功，可以开始测试`;

      // 显示成功消息
      message.success(`${deviceName}连接成功`);

      console.log(`设备连接成功处理完成: ${group.itemGroupName} (${deviceName})`);
    } catch (error) {
      console.error('处理设备连接成功失败:', error);
    }
  }

  /**
   * 开始设备测试
   * @param group 项目组
   */
  function startDeviceTest(group) {
    try {
      // 查找对应的串口设备配置
      const comEquipment = comEquipmentList.value.find((item) => item.groupId === group.itemGroupId);
      if (!comEquipment) {
        message.error('未找到对应的设备配置');
        return;
      }

      // 检查WebSocket连接
      const ws = websocketMap.get(comEquipment.websocketUrl);
      if (!ws || !ws.isConnected()) {
        message.error('设备连接已断开，请重新连接');
        return;
      }

      // 设置测试状态
      group.testingInProgress = true;
      group.comEquipmentTip = '正在发送测试指令...';

      // 构建并发送测试指令
      const cmdFunction = comEquipment.cmdFunction ? createCmdFunction(comEquipment.cmdFunction) : null;
      if (cmdFunction) {
        const cmdMessage = cmdFunction(currentReg.value);
        console.log('手动发送测试指令：', JSON.stringify(cmdMessage));

        if (cmdMessage) {
          ws.sendMessage('sendPortCmd', cmdMessage);
          group.comEquipmentTip = '测试指令已发送，等待设备响应...';
          message.success('测试指令已发送');

          // 设置超时，避免按钮一直处于loading状态
          setTimeout(() => {
            if (group.testingInProgress) {
              group.testingInProgress = false;
              group.comEquipmentTip = '等待设备响应超时';
            }
          }, 30000); // 30秒超时
        } else {
          group.testingInProgress = false;
          group.comEquipmentTip = '测试指令构建失败';
          message.error('测试指令构建失败');
        }
      } else {
        group.testingInProgress = false;
        group.comEquipmentTip = '未配置测试指令';
        message.error('设备未配置测试指令');
      }
    } catch (error) {
      group.testingInProgress = false;
      group.comEquipmentTip = '发送测试指令异常';
      console.error('发送测试指令异常:', error);
      message.error('发送测试指令异常：' + error.message);
    }
  }

  /**
   * 处理测量开始事件
   * @param measurementData 测量数据
   * @param comEquipments 设备配置列表
   */
  function handleMeasurementStarted(measurementData: any, comEquipments: any[]) {
    try {
      console.log('处理测量开始事件:', measurementData);

      // 查找对应的设备配置
      const comEquipment = findComEquipmentByDeviceModel(measurementData.deviceModel, comEquipments);
      if (!comEquipment) {
        console.warn('未找到对应的设备配置:', measurementData.deviceModel);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.warn('未找到对应的项目组:', comEquipment.groupId);
        return;
      }

      // 更新设备状态显示
      group.comEquipmentStatus = 'success';
      const deviceName = measurementData.deviceModel || '设备';
      group.comEquipmentTip = measurementData.message || `${deviceName}测量已开始，请在设备上进行检测`;
      group.testingInProgress = true;

      // 显示提示消息
      const patientName = measurementData.patientName || '当前患者';
      message.info(`${deviceName}测量已开始: ${patientName}`);

      console.log(`测量开始处理完成: ${group.itemGroupName} (${deviceName})`);
    } catch (error) {
      console.error('处理测量开始事件失败:', error);
    }
  }

  /**
   * 处理设备重置事件
   * @param resetData 重置数据
   * @param comEquipments 设备配置列表
   */
  function handleDeviceReset(resetData: any, comEquipments: any[]) {
    try {
      console.log('处理设备重置事件:', resetData);

      // 查找对应的设备配置
      const comEquipment = findComEquipmentByDeviceModel(resetData.deviceModel, comEquipments);
      if (!comEquipment) {
        console.warn('未找到对应的设备配置:', resetData.deviceModel);
        return;
      }

      // 查找对应的项目组
      const group = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);
      if (!group) {
        console.warn('未找到对应的项目组:', comEquipment.groupId);
        return;
      }

      // 更新设备状态显示
      group.comEquipmentStatus = 'success';
      const deviceName = resetData.deviceModel || '设备';
      group.comEquipmentTip = `${deviceName}重置完成，可以重新开始测试`;
      group.testingInProgress = false;

      // 显示成功消息
      message.success(`${deviceName}重置完成`);

      console.log(`设备重置处理完成: ${group.itemGroupName} (${deviceName})`);
    } catch (error) {
      console.error('处理设备重置事件失败:', error);
    }
  }

  /**
   * 根据设备型号查找对应的设备配置
   * @param deviceModel 设备型号
   * @param comEquipments 设备配置列表
   * @returns 设备配置对象或null
   */
  function findComEquipmentByDeviceModel(deviceModel: string, comEquipments: any[]) {
    if (!deviceModel || !comEquipments || comEquipments.length === 0) {
      return null;
    }

    console.log('查找设备配置:', { deviceModel, comEquipmentsCount: comEquipments.length });

    // 通过设备型号匹配
    const comEquipment = comEquipments.find((item) => {
      // 检查扩展配置中的设备型号
      try {
       return item.deviceModel === deviceModel;
      } catch (e) {
        console.warn('解析设备扩展配置失败:', item.name, e);
      }

      return false;
    });

    if (comEquipment) {
      console.log('设备匹配成功:', comEquipment.name);
    } else {
      console.warn('未找到匹配的设备配置:', deviceModel);
    }

    return comEquipment || null;
  }

  /**
   * 全局依赖验证
   * @returns Promise<boolean> 验证是否通过
   */
  async function validateDependencies() {
    if (!dependencyChecker.value) {
      console.log('依赖检查器未初始化，跳过依赖验证');
      return true;
    }

    // 获取所有有结果的项目组
    const groupsWithResults = groupList.value.filter((group) => {
      return (
        group.itemList &&
        group.itemList.some((item) => {
          return item.itemResult && item.itemResult.value !== null && item.itemResult.value !== undefined && item.itemResult.value !== '';
        })
      );
    });

    if (groupsWithResults.length === 0) {
      console.log('没有项目组有结果，跳过依赖验证');
      return true;
    }

    // 检查每个项目组的依赖关系
    const allMissing = [];
    for (const group of groupsWithResults) {
      const validation = dependencyChecker.value.checkDependencies(group.itemGroupId);
      if (!validation.valid) {
        allMissing.push({ group, missing: validation.missing });
      }
    }

    if (allMissing.length === 0) {
      console.log('所有项目组依赖验证通过');
      return true;
    }

    // 显示依赖验证弹窗
    return new Promise((resolve) => {
      // 合并所有缺失的依赖项目
      const flatMissing = allMissing.flatMap((item) => item.missing);

      // 显示弹窗
      dependencyModal.value?.open(
        allMissing[0].group, // 使用第一个有问题的项目组
        flatMissing,
        'warning'
      );

      // 设置临时的解析函数
      window._dependencyValidationResolve = resolve;
    });
  }

  /**
   * 验证单个项目组的依赖关系
   * @param group 项目组
   * @returns Promise<boolean> 验证是否通过
   */
  async function validateGroupDependencies(group) {
    if (!dependencyChecker.value) {
      console.log('依赖检查器未初始化，跳过依赖验证');
      return true;
    }

    // 检查该项目组是否有结果
    const hasResults =
      group.itemList &&
      group.itemList.some((item) => {
        return item.itemResult && item.itemResult.value !== null && item.itemResult.value !== undefined && item.itemResult.value !== '';
      });

    if (!hasResults) {
      console.log(`项目组 ${group.itemGroupName} 没有结果，跳过依赖验证`);
      return true;
    }

    // 检查该项目组的依赖关系
    const validation = dependencyChecker.value.checkDependencies(group.itemGroupId);
    if (validation.valid) {
      console.log(`项目组 ${group.itemGroupName} 依赖验证通过`);
      return true;
    }

    // 显示依赖验证弹窗
    return new Promise((resolve) => {
      // 显示弹窗
      dependencyModal.value?.open(group, validation.missing, 'warning');

      // 设置临时的解析函数
      window._dependencyValidationResolve = resolve;
    });
  }

  /**
   * 处理依赖验证弹窗的导航事件
   * @param missingItem 缺失的依赖项目
   */
  function handleDependencyNavigation(missingItem) {
    console.log('导航到依赖项目:', missingItem);

    if (missingItem.type === 'GROUP') {
      // 导航到大项
      navigateToGroup(missingItem.id);
    } else {
      // 导航到小项
      navigateToItem(missingItem.groupId, missingItem.itemId);
    }
  }

  /**
   * 处理依赖验证弹窗的确认事件
   * @param result 确认结果
   */
  function handleDependencyConfirm(result) {
    console.log('依赖验证确认:', result);

    // 解析Promise，允许继续保存
    if (window._dependencyValidationResolve) {
      window._dependencyValidationResolve(true);
      delete window._dependencyValidationResolve;
    }
  }

  /**
   * 处理依赖验证弹窗的取消事件
   * @param result 取消结果
   */
  function handleDependencyCancel(result) {
    console.log('依赖验证取消:', result);

    // 解析Promise，中断保存
    if (window._dependencyValidationResolve) {
      window._dependencyValidationResolve(false);
      delete window._dependencyValidationResolve;
    }
  }

  /**
   * 导航到指定项目组
   * @param groupId 项目组ID
   */
  function navigateToGroup(groupId) {
    const targetGroup = groupList.value.find((g) => g.itemGroupId === groupId);
    if (targetGroup) {
      // 滚动到目标项目组
      const element = document.querySelector(`[data-group-id="${groupId}"]`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 高亮显示
        highlightElement(element);
      } else {
        message.info(`请查看 ${targetGroup.itemGroupName} 项目组`);
      }
    }
  }

  /**
   * 导航到指定小项
   * @param groupId 项目组ID
   * @param itemId 小项ID
   */
  function navigateToItem(groupId, itemId) {
    // 先导航到项目组
    navigateToGroup(groupId);

    // 延迟高亮小项
    setTimeout(() => {
      const element = document.querySelector(`[data-item-id="${itemId}"]`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        highlightElement(element);
      }
    }, 500);
  }

  /**
   * 高亮显示元素
   * @param element DOM元素
   */
  function highlightElement(element) {
    if (!element) return;

    // 添加高亮样式
    element.style.backgroundColor = '#fff7e6';
    element.style.border = '2px solid #ffd591';
    element.style.borderRadius = '4px';

    // 2秒后移除高亮
    setTimeout(() => {
      element.style.backgroundColor = '';
      element.style.border = '';
      element.style.borderRadius = '';
    }, 2000);
  }

  /**
   * 获取依赖大项的值（通常是完成状态）
   * @param group 大项信息
   * @returns 大项状态值
   */
  function getDependentGroupValue(group) {
    // 兼容字符串和数字类型的checkStatus
    const status = String(group.checkStatus);
    if (status === '1' || status === '已检') {
      return '已完成';
    } else if (status === '0' || status === '进行中') {
      return '进行中';
    } else {
      return '未开始';
    }
  }

  /**
   * 获取依赖大项的异常标志
   * @param group 大项信息
   * @returns 异常标志
   */
  function getDependentGroupAbnormalFlag(group) {
    // 根据大项的完成状态判断
    const status = String(group.checkStatus);
    return status === '1' || status === '已检' ? 'N' : 'Y';
  }

  /**
   * 获取依赖小项的值
   * @param item 小项信息
   * @returns 小项结果值
   */
  function getDependentItemValue(item) {
    if (item.itemResult && item.itemResult.value !== null && item.itemResult.value !== undefined) {
      return String(item.itemResult.value);
    }
    return '';
  }

  /**
   * 获取依赖小项的异常标志
   * @param item 小项信息
   * @returns 异常标志
   */
  function getDependentItemAbnormalFlag(item) {
    if (item.itemResult && item.itemResult.abnormalFlag) {
      return item.itemResult.abnormalFlag;
    }
    // 如果没有结果，默认为正常
    return 'N';
  }

  /**
   * 构建操作员信息对象
   * @returns OperatorInfo 对象
   */
  function buildOperatorInfo() {
    const depart = currentDepart.value;

    return {
      // 基本信息（必填）
      id: user.id || '',
      name: user.realname || user.username || '',
      departmentId: depart?.id || '',
      departmentName: depart?.departName || '',
    };
  }

  /**
   * 构建设备属性对象
   * @param comEquipment 设备配置
   * @param extraConfig 解析后的扩展配置
   * @returns DeviceProperties 对象
   */
  function buildDeviceProperties(comEquipment, extraConfig) {
    return {
      // 基本设备信息
      deviceId: comEquipment.id || '',
      deviceName: comEquipment.name || '',
      deviceType: comEquipment.deviceType || '',
      deviceModel: comEquipment.deviceModel || '',
      //自动启动测量
      autoStartMeasurement: comEquipment.autoStartMeasurement == 'Y' || true,
      // 串口配置（保持向后兼容，优先使用 extraConfig 中的配置）
      portDescriptor: comEquipment.portDescriptor,
      baudRate: comEquipment.baudRate,
      dataBits: comEquipment.dataBits,
      stopBits: comEquipment.stopBits,
      parity: comEquipment.verifyBits,
      safetySleepTime: comEquipment.safetySleepTime,
      readTimeout: extraConfig.readTimeout || 5000,
      writeTimeout: extraConfig.writeTimeout || 5000,
      flowControl: extraConfig.flowControl || 'NONE',
      // 设备特定属性
      needsWarmup: extraConfig.needsWarmup || false,
      warmupTime: extraConfig.warmupTime || 0,
      measurementTime: extraConfig.measurementTime || 60,

      // 数据缓冲配置
      needsBuffering: extraConfig.needsBuffering || false,
      dataEndMarkers: extraConfig.dataEndMarkers || '[""]',
      dataStartMarkers: extraConfig.dataStartMarkers || '[""]',
      characteristicMarkers: extraConfig.characteristicMarkers || '["RD", "$", "*B"]',
      maxBufferSize: extraConfig.maxBufferSize || 1024,
      bufferTimeoutMs: extraConfig.bufferTimeoutMs || 10000,

      // 通讯配置
      needsStartCommand: extraConfig.needsStartCommand || true,
      startMeasurementCommand: extraConfig.startMeasurementCommand || 'RD',
      measurementMode: extraConfig.measurementMode || 'MANUAL',
      needsActiveQuery: extraConfig.needsActiveQuery || false,
      queryResultCommand: extraConfig.queryResultCommand || '',
      queryIntervalMs: extraConfig.queryIntervalMs || 1000,
      maxQueryAttempts: extraConfig.maxQueryAttempts || 10,
      queryTimeoutMs: extraConfig.queryTimeoutMs || 30000,
      supportsMultiStageQuery: extraConfig.supportsMultiStageQuery || false,
      stageQueryConfig: extraConfig.stageQueryConfig || '',

      // 其他扩展配置
      ...extraConfig,
    };
  }

  /**项目组合部分*/
  const groupLoading = ref(false);
  const activeKey = ref(['1', '2', '3']);
  const groupList = ref<CustomerRegItemGroup[]>([]);

  /**依赖关系部分*/
  const dependencyMap = ref(new Map());
  const dependencyLoading = ref(false);
  const dependencyChecker = ref(null);
  const dependencyModal = ref(null);

  /**依赖项目信息缓存*/
  const dependentItemsCache = ref(new Map()); // 缓存已构建的依赖项目信息

  /**性能监控统计*/
  const performanceStats = ref({
    lastLoadTime: 0,
    cacheHitRate: 0,
    totalQueries: 0,
    cacheHits: 0,
    integratedQueryUsed: false,
  });

  /**
   * 加载项目依赖关系
   */
  async function loadDependencies() {
    if (groupList.value.length === 0) {
      return;
    }

    try {
      dependencyLoading.value = true;
      const groupIds = groupList.value.map((g) => g.itemGroupId);

      // 1. 加载依赖关系
      console.log('正在调用getDependenciesByGroupIds，参数:', groupIds);
      const dependencyResponse = await getDependenciesByGroupIds(groupIds);
      console.log('getDependenciesByGroupIds响应:', dependencyResponse);

      // 构建依赖关系映射
      dependencyMap.value.clear();
      if (dependencyResponse && typeof dependencyResponse === 'object') {
        Object.entries(dependencyResponse).forEach(([groupId, deps]) => {
          dependencyMap.value.set(groupId, deps);
        });
      }

      console.log('依赖关系加载完成:', dependencyMap.value);

      // 2. 预构建依赖项目信息缓存
      await prebuildDependentItemsCache();

      // 3. 初始化依赖检查器
      dependencyChecker.value = new DependencyChecker(groupList.value, dependencyMap.value);
    } catch (error) {
      console.error('加载依赖关系失败:', error);
    } finally {
      dependencyLoading.value = false;
    }
  }

  /**
   * 从集成查询响应构建依赖映射（优化版本）
   * @param groupList 包含依赖信息的项目组列表
   */
  function buildDependencyMapFromResponse(groupList) {
    console.log('从集成查询响应构建依赖映射...');

    // 清除所有缓存
    dependencyMap.value.clear();
    dependentItemsCache.value.clear();
    clearDependentItemsMapCache();

    let dependencyCount = 0;
    let cacheCount = 0;

    // 优化：使用单次遍历构建所有映射
    groupList.forEach((group) => {
      // 构建依赖关系映射
      if (group.dependencies && group.dependencies.length > 0) {
        dependencyMap.value.set(group.itemGroupId, group.dependencies);
        dependencyCount++;
      }

      // 构建依赖项目结果缓存
      if (group.dependentResults && group.dependentResults.length > 0) {
        group.dependentResults.forEach((result) => {
          const cacheKey = result.itemType === 'GROUP' ? result.itemId : result.itemId;
          if (cacheKey && !dependentItemsCache.value.has(cacheKey)) {
            dependentItemsCache.value.set(cacheKey, result);
            cacheCount++;
          }
        });
      }
    });

    console.log(`依赖关系映射构建完成，共 ${dependencyCount} 个项目有依赖关系`);
    console.log(`依赖项目结果缓存构建完成，共缓存 ${cacheCount} 项`);
  }

  /**
   * 预构建依赖项目信息缓存（分离查询时使用）
   */
  async function prebuildDependentItemsCache() {
    console.log('开始预构建依赖项目信息缓存...');

    // 收集所有需要查询的项目组ID
    const allGroupIds = Array.from(dependencyMap.value.keys());

    if (allGroupIds.length === 0) {
      console.log('没有需要预构建的依赖项目');
      return;
    }

    try {
      // 批量查询所有项目的依赖项目结果
      console.log('正在调用getDependentItemResultsBatch，参数:', {
        customerRegId: currentReg.value.id,
        groupIds: allGroupIds,
      });
      const dependentResults = await getDependentItemResultsBatch({
        customerRegId: currentReg.value.id,
        groupIds: allGroupIds,
      });
      console.log('getDependentItemResultsBatch响应:', dependentResults);

      // 构建缓存
      dependentItemsCache.value.clear();

      Object.entries(dependentResults).forEach(([groupId, results]) => {
        if (results && results.length > 0) {
          results.forEach((result) => {
            // 根据类型存储到缓存中
            if (result.itemType === 'GROUP') {
              dependentItemsCache.value.set(result.itemId, result);
            } else if (result.itemType === 'ITEM') {
              dependentItemsCache.value.set(result.itemId, result);
            }
          });
        }
      });

      console.log(`依赖项目信息缓存构建完成，共缓存 ${dependentItemsCache.value.size} 项`);
    } catch (error) {
      console.error('预构建依赖项目信息缓存失败:', error);
    }
  }

  function getGroupByDepartId(departmentId) {
    groupLoading.value = true;
    const loadStartTime = performance.now();

    // 清除缓存，准备加载新数据
    clearDependentItemsMapCache();
    resetPerformanceStats();

    let reqParam = {
      regId: currentReg.value.id,
      includeDependencies: true, // 启用依赖查询
    };
    reqParam['departmentIds'] = departmentId;

    listGroupByRegId(reqParam)
      .then((res) => {
        // 处理项目组数据
        res.forEach((group) => {
          group.itemList.forEach((item) => {
            if (!item.itemResult) {
              item.itemResult = {
                value: item.normalDefaultValue || '',
                pic: [],
              };
            }
          });
        });

        groupList.value = res;

        // 智能选择依赖信息处理方式
        const hasIntegratedDependencies = res.length > 0 && res.some((group) => group.dependencies || group.dependentResults);

        if (hasIntegratedDependencies) {
          buildDependencyMapFromResponse(res);
          dependencyChecker.value = new DependencyChecker(groupList.value, dependencyMap.value);
          console.log('使用集成查询获取的依赖信息');
        } else {
          // 降级到分离查询
          loadDependencies();
          console.log('降级到分离查询获取依赖信息');
        }

        try {
          initComEquipment(groupList);
        } catch (e) {
          console.log('initComEquipment error:', e);
        }

        // 记录加载完成时间
        const loadEndTime = performance.now();
        performanceStats.value.lastLoadTime = (loadEndTime - loadStartTime).toFixed(2);

        // 输出性能报告
        const report = getPerformanceReport();
        console.log('依赖项目加载性能报告:', report);
      })
      .catch((error) => {
        console.error('加载项目组数据失败:', error);
        // 错误时也要清除缓存
        clearDependentItemsMapCache();
      })
      .finally(() => {
        groupLoading.value = false;
      });
  }

  function abandonItem(item: ItemInfo, group: CustomerRegItemGroup) {
    let tip = `确定要放弃${item.name}吗？`;
    createConfirm({
      iconType: 'warning',
      title: '放弃项目提示',
      content: tip,
      onOk: () => {
        item.itemResult.abandonFlag = 1;
        item.itemResult.itemId = item.id;
        item.itemResult.itemName = item.name;
        item.itemResult.valueType = item.itemType;
        item.itemResult.customerRegId = currentReg.value.id;
        item.itemResult.itemGroupId = group.itemGroupId;
        item.itemResult.itemGroupName = group.itemGroupName;

        abandon(item.itemResult).then(() => {
          emits('success');
        });
      },
    });
  }

  function recover(item: ItemInfo, group: CustomerRegItemGroup) {
    let tip = `确定要恢复${item.name}吗？`;
    createConfirm({
      iconType: 'warning',
      title: '恢复提示',
      content: tip,
      onOk: () => {
        item.itemResult.abandonFlag = 0;
        item.itemResult.itemId = item.id;
        item.itemResult.itemName = item.name;
        item.itemResult.valueType = item.itemType;
        item.itemResult.customerRegId = currentReg.value.id;
        item.itemResult.itemGroupId = group.itemGroupId;
        item.itemResult.itemGroupName = group.itemGroupName;
        item.itemResult.groupHisCode = group.hisCode;
        item.itemResult.groupHisName = group.hisName;
        item.itemResult.itemHisCode = item.hisCode;
        item.itemResult.itemHisName = item.hisName;
        item.itemResult.sortNo = item.seq;

        unAbandon(item.itemResult).then(() => {
          emits('success');
        });
      },
    });
  }

  function handelAbandonGroup(group: CustomerRegItemGroup) {
    let tip = `将一并放弃其包含的所有项目，确定放弃吗？`;
    createConfirm({
      iconType: 'warning',
      title: '放弃提示',
      content: tip,
      onOk: () => {
        abandonGroup({ groupId: group.itemGroupId, regId: group.customerRegId }).then(() => {
          emits('success');
        });
      },
    });
  }

  function confirmClearGroupResult(group: CustomerRegItemGroup) {
    let tip = `将一并清除其包含的所有项目和科室小结，确定清除吗？`;
    createConfirm({
      iconType: 'warning',
      title: '清除结果提示',
      content: tip,
      onOk: () => {
        clearGroupResult({
          groupId: group.itemGroupId,
          regId: group.customerRegId,
          departmentId: currentDepart.value.id,
        }).then(() => {
          emits('success');
        });
      },
    });
  }

  function recoverGroup(group: CustomerRegItemGroup) {
    let tip = `将一并恢复其包含的所有项目，确定恢复吗？`;
    createConfirm({
      iconType: 'warning',
      title: '恢复组合提示',
      content: tip,
      onOk: () => {
        unAbandonGroup({ groupId: group.itemGroupId, regId: group.customerRegId }).then(() => {
          emits('success');
        });
      },
    });
  }

  function showCriticalItems(criticalItems: CustomerRegCriticalItem[], refresh: boolean = false) {
    if (criticalItems.length == 0) {
      return;
    }
    // 根据severityDegree分类（A类和B类）
    const categoryA = criticalItems.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.filter((item) => item.severityDegree === 'B类');

    // 然后据此拼接提示信息
    const messageA = categoryA.map((item, index) => `${index + 1}. ${item.itemName}`).join(' <br/>');
    const messageB = categoryB.map((item, index) => `${index + 1}. ${item.itemName}`).join(' <br/>');

    let title = '';
    if (categoryA.length > 0) {
      title += `发现${categoryA.length}项A类危急值`;
    }
    if (categoryB.length > 0) {
      title += ` ${categoryB.length}项B类危急值`;
    }

    let message = '<div style="max-height: 50vh;overflow-y: auto;">';
    if (categoryA.length > 0) {
      message += `<span style="color:#f5222d;font-weight: normal">A类（需要立即进行临床干预，否则
将危及生命或导致严重不良后果的异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageA}</p><br/>`;
    }
    if (categoryB.length > 0) {
      message += `<span style="color:#faad14;font-weight: bold;">B类（需要临床进一步检查以明确诊断和(或)需要
医学治疗疗的重要异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageB}</p>`;
    }
    message += '</div>';

    createErrorModal({
      title: title,
      content: message,
      iconType: 'warning',
      onOk: () => {
        if (refresh) {
          getCriticalItemByRegId();
        }
      },
    });
  }

  function saveGroupItemResult(group: CustomerRegItemGroup) {
    if (departSummary.value?.id) {
      createConfirm({
        title: '更新项目结果提示',
        content: '已生成科室小结，更新项目后需要重新生成小结，确定更新项目结果吗？',
        iconType: 'warning',
        onOk: () => {
          doSaveGroupResult(group);
        },
      });
    } else {
      doSaveGroupResult(group);
    }
  }

  async function doSaveGroupResult(group: CustomerRegItemGroup) {
    // 依赖验证 - 针对单个项目组
    if (!(await validateGroupDependencies(group))) {
      return; // 依赖验证失败，中断保存
    }

    let validMsg: String[] = [];
    group.itemList?.forEach((item) => {
      if (item.id) {
        let itemResult = item.itemResult;
        if (item.requiredFlag && (!itemResult.abandonFlag || itemResult.abandonFlag == 0) && itemResult.value == '') {
          validMsg.push(`${item.name}`);
        }
      }
    });
    if (validMsg.length > 0) {
      //带数字索引的msg
      let msg = validMsg.map((item, index) => `${index + 1}.${item}`).join(' <br/>');
      createErrorModal({
        title: `以下${validMsg.length}项结果为空`,
        content: `<div style="max-height: 50vh;overflow-y: auto;">${msg}</div>`,
      });
      return;
    }
    let data: ICustomerRegItemResult[] = [];
    group.itemList?.forEach((item) => {
      let itemResult = item.itemResult;
      itemResult.itemId = item.id;
      itemResult.itemName = item.name;
      itemResult.valueType = item.itemType;
      itemResult.unit = item.unit;
      itemResult.customerRegId = currentReg.value.id;
      itemResult.idCard = currentReg.value.idCard;
      itemResult.archivesNum = currentReg.value.archivesNum;
      itemResult.itemGroupId = group.itemGroupId;
      itemResult.itemGroupName = group.itemGroupName;
      itemResult.departmentId = group.departmentId;
      itemResult.valueRefRange = item.normalRef;
      itemResult.groupHisCode = group.hisCode;
      itemResult.groupHisName = group.hisName;
      itemResult.itemHisCode = item.hisCode;
      itemResult.itemHisName = item.hisName;
      itemResult.examNo = currentReg.value.examNo;
      itemResult.sortNo = item.seq;

      data.push(itemResult);
    });

    saveItemResult({
      resultList: data,
      departmentId: currentDepart.value.id,
      reg: unref(currentReg.value),
    }).then(async (res) => {
      //await getGroupByRegId();
      //await getAllGroupByRegId();
      if (res.success) {
        emits('success');
      } else {
        message.error(res.message);
      }
    });
  }

  function extractItemResults() {
    return new Promise((resolve, reject) => {
      let targetGroupList = groupList.value.filter(
        (group) => (group.payerType == '个人支付' && group.payStatus == '已支付') || group.payerType == '单位支付'
      );
      //从groupList中提取数据
      let validMsg: String[] = [];
      targetGroupList.forEach((group) => {
        group.itemList?.forEach((item) => {
          if (item.id) {
            let itemResult = item.itemResult;
            if (item.requiredFlag && (!itemResult.abandonFlag || itemResult.abandonFlag == 0) && itemResult.value == '') {
              validMsg.push(`${item.name}`);
            }
          }
        });
      });
      if (validMsg.length > 0) {
        //带数字索引的msg
        let msg = validMsg.map((item, index) => `${index + 1}.${item}`).join(' <br/>');
        let msgModalOption: ModalOptionsPartial = {
          title: `以下${validMsg.length}项结果为空`,
          content: `<div style="max-height: 50vh;overflow-y: scroll;">${msg}</div>`,
        };
        reject({ modalOption: msgModalOption });
        return;
      }
      let data: ICustomerRegItemResult[] = [];
      targetGroupList.forEach((group) => {
        // 普通项目组，使用原有逻辑
        group.itemList?.forEach((item) => {
          let itemResult = item.itemResult;
          itemResult.itemId = item.id;
          itemResult.itemName = item.name;
          itemResult.valueType = item.itemType;
          itemResult.unit = item.unit;
          itemResult.customerRegId = currentReg.value.id;
          itemResult.idCard = currentReg.value.idCard;
          itemResult.archivesNum = currentReg.value.archivesNum;
          itemResult.itemGroupId = group.itemGroupId;
          itemResult.itemGroupName = group.itemGroupName;
          itemResult.departmentId = group.departmentId;
          itemResult.valueRefRange = item.normalRef;
          itemResult.groupHisCode = group.hisCode;
          itemResult.groupHisName = group.hisName;
          itemResult.itemHisCode = item.hisCode;
          itemResult.itemHisName = item.hisName;
          itemResult.sortNo = item.seq;
          itemResult.examNo = currentReg.value.examNo;

          data.push(itemResult);
        });
      });

      resolve(data);
    });
  }

  async function saveResult() {
    if (departSummary.value?.id) {
      createConfirm({
        title: '更新项目结果提示',
        content: '已生成科室小结，更新项目后需要重新生成小结，确定更新项目结果吗？',
        iconType: 'warning',
        onOk: () => {
          doSave();
        },
      });
    } else {
      doSave();
    }
  }

  async function doSave() {
    try {
      // 依赖验证
      if (!(await validateDependencies())) {
        return; // 依赖验证失败，中断保存
      }

      let data = await extractItemResults();
      if (data) {
        loading.value = true;
        const result = await saveItemResult({
          resultList: data,
          departmentId: currentDepart.value.id,
          reg: unref(currentReg.value),
        });
        if (result.success) {
          message.success('保存成功！');
          emits('success');
        } else {
          message.error(result.message);
        }
        loading.value = false;
      }
    } catch (error) {
      if (error?.modalOption) {
        createErrorModal(error?.modalOption);
      }
      return;
    }
  }

  async function loadData(reg: ICustomerReg, depart: Depart) {
    currentReg.value = reg;
    currentDepart.value = depart;
    if (currentReg.value?.id && currentDepart.value?.id) {
      getGroupByDepartId(currentDepart.value.id);
      getSummary({ departmentId: currentDepart.value.id, regId: currentReg.value.id }).then((res) => {
        if (res.success) {
          departSummary.value = res.result;
          characterSummary.value = res.result.characterSummary;
        } else {
          departSummary.value = null;
          characterSummary.value = '';
        }
      });

      getCriticalItemByRegId(true);
      getLatestChangeApplyList4CurrentReg();
    }
  }

  async function saveSummary() {
    try {
      // 依赖验证
      if (!(await validateDependencies())) {
        return; // 依赖验证失败，中断保存
      }

      let data = await extractItemResults();
      if (data) {
        loading.value = true;
        let saveResult = await saveItemResult({ resultList: data, reg: currentReg.value });
        if (saveResult.success) {
          // 生成标准科室小结
          await generateSummary({
            customerReg: currentReg.value,
            departmentId: currentDepart.value.id,
            updateByManual: '0',
          });
          emits('success');
        } else {
          if (saveResult.result == '-1') {
            handleChangeApplyTip();
          } else {
            message.error(saveResult.message);
          }
        }
        loading.value = false;
      }
    } catch (error) {
      createErrorModal(error?.modalOption);
      return;
    }
  }

  async function handleUpdateSummary() {
    let data = {
      departmentId: currentDepart.value.id,
      customerRegId: currentReg.value.id,
      characterSummary: characterSummary.value,
      updateByManual: '1',
    };
    loading.value = true;
    updateSummary(data)
      .then(() => {
        emits('success');
      })
      .finally(() => {
        loading.value = false;
      });
  }

  async function handleAudit() {
    if (!departSummary.value?.id) {
      message.error('请先生成科室小结！');
      return;
    }
    let data = {
      id: departSummary.value?.id,
      auditContent: '通过',
      departmentId: currentDepart.value.id,
      customerRegId: currentReg.value.id,
    };
    auditDepartSummary(data).then(() => {
      emits('success');
    });
  }

  async function handleUnAudit() {
    if (!departSummary.value?.id) {
      message.error('请先生成科室小结！');
      return;
    }
    let data = {
      id: departSummary.value?.id,
      departmentId: currentDepart.value.id,
      customerRegId: currentReg.value.id,
    };
    unAuditDepartSummary(data).then(() => {
      emits('success');
    });
  }

  async function handleRemoveSummary() {
    if (!departSummary.value?.id) {
      message.error('科室小结尚未生成！');
      return;
    }
    removeSummary({ summaryId: departSummary.value?.id }).then(() => {
      departSummary.value = null;
      characterSummary.value = '';
      emits('success');
    });
  }

  let stopFunctions: Function[] = [];
  watch(groupList, (newGroupList) => {
    stopFunctions.forEach((stop) => stop());
    stopFunctions = [];
    newGroupList.forEach((group) => {
      group.itemList?.forEach((item) => {
        if (item.itemType === '数值型') {
          const stop = watch(
            () => item.itemResult.value,
            () => {
              group.itemList?.forEach((calcItem) => {
                if (calcItem.itemType === '计算型' && calcItem.formula) {
                  let testData =
                    group.itemList
                      ?.filter((item) => item.itemType === '数值型' && !Number.isNaN(item.itemResult.value))
                      .map((item) => ({
                        name: item.name,
                        value: item.itemResult.value as number,
                      })) || [];
                  calcItem.itemResult.value = calculateExpression(calcItem.formula, testData);
                }
              });
            }
          );
          stopFunctions.push(stop);
        }
      });
    });
  });

  /*数据修改申请部分*/
  provide('customerReg4ItemResultChangeApply', currentReg);
  provide('departmentId4ItemResultChangeApply', currentDepart.value.id);
  provide('department4ItemResultChangeApply', currentDepart);
  const itemResultChangeApplyListRef = ref();
  const changeApplyModal = ref();
  const changeApplyList = ref([]);

  function openItemResultChangeModal() {
    if (!currentReg.value.id) {
      message.error('请先选择体检人！');
      return;
    }
    itemResultChangeApplyListRef.value?.open(currentReg.value, currentDepart.value.id);
  }

  function getLatestChangeApplyList4CurrentReg() {
    getLatestChangeApplyList({
      customerRegId: currentReg.value.id,
      departmentId: currentDepart.value.id,
    }).then((res) => {
      if (res.success) {
        changeApplyList.value = res.result;
      }
    });
  }

  function handleChangeApplyTip() {
    //检查是否有未完成的变更申请
    getLatestChangeApplyList({
      customerRegId: currentReg.value.id,
      departmentId: currentDepart.value.id,
    }).then((res) => {
      if (res.success) {
        if (res.result.length > 0) {
          createConfirm({
            iconType: 'warning',
            title: '提示',
            okText: '查看',
            content: '项目结果变更申请尚未确认，是否查看？',
            onOk: () => {
              itemResultChangeApplyListRef.value?.open(currentReg.value, currentDepart.value.id);
            },
          });
        } else {
          createConfirm({
            iconType: 'warning',
            title: '提示',
            okText: '创建申请',
            content: '已有总检记录，如需修改数据需要向主检医生发送修改申请，是否继续？',
            onOk: () => {
              changeApplyModal.value.disableSubmit = false;
              changeApplyModal.value.add();
            },
          });
        }
      }
    });
  }

  onUnmounted(() => {
    // 清理WebSocket连接
    websocketMap.forEach((ws) => {
      try {
        ws.disconnect();
      } catch (e) {
        console.error('Error disconnecting WebSocket:', e);
      }
    });
    websocketMap.clear();

    // 清理当前用户的设备映射关系
    clearCurrentUserDeviceMappings();
  });
  function handleHistoryResultReportId() {
    querySysParamByCode({ code: 'historyResultReportId' }).then((res) => {
      historyResultReportId.value = res.result;
    });
  }
  onMounted(() => {
    comEquipmentList.value = JSON.parse(getSettingFromLocalStorage('comEquipmentList', '[]'));
    comEquipmentList.value.forEach((item) => {
      const ws = websocketMap.has(item.websocketUrl) ? websocketMap.get(item.websocketUrl) : new WebSocketService(item.websocketUrl);
      websocketMap.set(item.websocketUrl, ws);
    });

    //获取是否自动收费系统参数
    querySysParamByCode({ code: 'autoCharge' }).then((res) => {
      autoCharge.value = res.result;
    });
    handleHistoryResultReportId();
  });

  /**
   * 判断是否应该显示"查询结果"按钮
   * @param group 项目组
   * @returns boolean
   */
  function shouldShowQueryResultsButton(group) {
    // 检查该项目组是否配置了设备
    const comEquipment = comEquipmentList.value.find((item) => item.groupId === group.itemGroupId);
    if (!comEquipment) {
      return false;
    }

    // 检查设备是否支持查询功能（根据设备型号判断）
    const supportedDevices = [
      'FGY-200肺功能',
      'FGY200',
      '电测听',
      'AUDIOMETRY',
      '丹麦国际听力电测听',
      'audiometry'
    ];

    // 支持模糊匹配
    const deviceModel = comEquipment.deviceModel || '';
    const comDeviceType = comEquipment.comDeviceType || '';

    const isSupported = supportedDevices.some(supported =>
      deviceModel.includes(supported) ||
      comDeviceType.includes(supported) ||
      deviceModel.toLowerCase().includes(supported.toLowerCase()) ||
      comDeviceType.toLowerCase().includes(supported.toLowerCase())
    );

    if (!isSupported) {
      return false;
    }

    // 检查设备连接状态（允许未连接状态下也能查询）
    // 因为查询功能可能不需要实时连接
    return true;
  }

  /**
   * 查询设备测量结果
   * @param group 项目组
   */
  function queryDeviceResults(group) {
    try {
      // 查找对应的设备配置
      const comEquipment = comEquipmentList.value.find((item) => item.groupId === group.itemGroupId);
      if (!comEquipment) {
        message.error('未找到对应的设备配置');
        return;
      }

      // 检查WebSocket连接
      const ws = websocketMap.get(comEquipment.websocketUrl);
      if (!ws || !ws.isConnected()) {
        message.error('设备连接已断开，请重新连接');
        return;
      }

      // 检查患者信息
      if (!currentReg.value || !currentReg.value.examNo) {
        message.error('患者信息不完整，无法查询结果');
        return;
      }

      // 设置查询状态
      group.queryingInProgress = true;
      group.comEquipmentTip = '正在查询测量结果...';

      // 使用统一的解析函数
      let extraConfig = parseExtraConfig(comEquipment.extraConfig || '{}');

      // 构建完整的 ConnectRequest 查询请求（与中间件期望的格式一致）
      const queryRequest = {
        priority: comEquipment.priority || 1,
        requestTimestamp: Date.now(),
        timeoutMs: extraConfig.timeoutMs || 30000,
        retryCount: extraConfig.retryCount || 3,
        retryIntervalMs: extraConfig.retryIntervalMs || 1000,

        // 患者信息对象
        patientInfo: {
          customerId: currentReg.value.customerId || '',
          customerRegId: currentReg.value.id || '',
          examNo: currentReg.value.examNo || '',
          name: currentReg.value.name || '',
          gender: currentReg.value.gender || '',
          age: currentReg.value.age || 0,
          birthDate: currentReg.value.birthday ? new Date(currentReg.value.birthday).toISOString().split('T')[0] : '',
          idCard: currentReg.value.idCard || '',
          phone: currentReg.value.phone || ''
        },

        // 体检项目信息对象
        examItemInfo: {
          customerRegId: currentReg.value.id || '',
          customerRegItemGroupId: group.id || '',
          itemId: comEquipment.groupId || '',
          itemName: group.itemGroupName || '',
          hisName: group.hisName || '',
          hisCode: group.hisCode || '',
          departmentId: currentDepart.value?.id || '',
          departmentName: currentDepart.value?.departName || '',
          value: '',
          unit: '',
          abnormalFlag: '',
          valueRefRange: '',
          dependentItems: []
        },

        // 操作员信息对象
        operatorInfo: {
          operatorId: user.id || '',
          operatorName: user.realname || '',
          departmentId: currentDepart.value?.id || '',
          departmentName: currentDepart.value?.departName || '',
          workstationId: user.workstationId || '',
          sessionId: user.id || ''
        },

        // 设备属性对象
        deviceProperties: {
          deviceId: comEquipment.deviceId || '',
          deviceModel: comEquipment.deviceModel || '',
          deviceType: comEquipment.deviceType || '',
          comDeviceType: comEquipment.comDeviceType || '',
          portDescriptor: comEquipment.portDescriptor || '',
          baudRate: extraConfig.baudRate || 9600,
          dataBits: extraConfig.dataBits || 8,
          stopBits: extraConfig.stopBits || 1,
          parity: extraConfig.parity || 'NONE',
          flowControl: extraConfig.flowControl || 'NONE',
          readTimeout: extraConfig.readTimeout || 5000,
          writeTimeout: extraConfig.writeTimeout || 5000,
          connectionTimeout: extraConfig.connectionTimeout || 10000,
          maxRetryAttempts: extraConfig.maxRetryAttempts || 3,
          retryDelay: extraConfig.retryDelay || 1000,
          autoReconnect: extraConfig.autoReconnect !== false,
          keepAlive: extraConfig.keepAlive !== false,
          bufferSize: extraConfig.bufferSize || 1024,
          encoding: extraConfig.encoding || 'UTF-8',
          lineEnding: extraConfig.lineEnding || '\r\n',
          commandPrefix: extraConfig.commandPrefix || '',
          commandSuffix: extraConfig.commandSuffix || '',
          responsePrefix: extraConfig.responsePrefix || '',
          responseSuffix: extraConfig.responseSuffix || '',
          dataValidation: extraConfig.dataValidation !== false,
          checksumType: extraConfig.checksumType || 'NONE',
          errorHandling: extraConfig.errorHandling || 'RETRY',
          debugMode: extraConfig.debugMode === true,
          customProperties: extraConfig.customProperties || {}
        }
      };

      console.log('发送查询请求:', queryRequest);
      ws.sendMessage('queryResults', queryRequest);

      message.info('查询请求已发送，请等待结果...');

      // 设置超时，避免按钮一直处于loading状态
      setTimeout(() => {
        if (group.queryingInProgress) {
          group.queryingInProgress = false;
          group.comEquipmentTip = '查询结果超时';
        }
      }, 30000); // 30秒超时

    } catch (error) {
      group.queryingInProgress = false;
      group.comEquipmentTip = '查询结果异常';
      console.error('查询结果异常:', error);
      message.error('查询结果异常：' + error.message);
    }
  }

  /**
   * 处理查询结果响应
   * @param response 查询响应数据
   * @param comEquipments 设备配置列表
   */
  function handleQueryResultsResponse(response, comEquipments) {
    try {
      console.log('处理查询结果响应:', response);

      // 查找对应的项目组
      let targetGroup = null;
      let targetEquipment = null;

      // 根据设备ID或设备型号查找对应的设备和项目组
      if (response.deviceId || response.deviceModel) {
        targetEquipment = comEquipments.find(equipment =>
          equipment.deviceId === response.deviceId ||
          equipment.deviceModel === response.deviceModel
        );

        if (targetEquipment) {
          targetGroup = groupList.value.find(group =>
            group.itemGroupId === targetEquipment.groupId
          );
        }
      }

      // 如果没有找到，尝试根据查询状态查找
      if (!targetGroup) {
        targetGroup = groupList.value.find(group => group.queryingInProgress);
      }

      if (targetGroup) {
        targetGroup.queryingInProgress = false;

        if (response.success) {
          targetGroup.comEquipmentTip = response.message || '查询完成';

          // 如果有查询结果数据，处理数据
          if (response.data) {
            console.log('收到查询结果数据:', response.data);
            // 这里可以进一步处理查询到的数据
            // 例如显示在界面上或者保存到本地
            message.success('查询成功，已获取到测量结果');
          } else {
            message.success('查询完成，但未找到测量结果');
          }
        } else {
          targetGroup.comEquipmentTip = response.message || '查询失败';
          message.error(response.message || '查询失败');
        }
      } else {
        // 没有找到对应的项目组，显示通用消息
        if (response.success) {
          message.success(response.message || '查询完成');
        } else {
          message.error(response.message || '查询失败');
        }
      }

    } catch (error) {
      console.error('处理查询结果响应异常:', error);
      message.error('处理查询结果响应异常：' + error.message);
    }
  }

  defineExpose({
    loadData,
  });
</script>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }
</style>
