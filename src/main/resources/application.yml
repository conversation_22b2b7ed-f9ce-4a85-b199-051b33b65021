# 禁用Web环境
spring:
  main:
    web-application-type: none
  profiles:
    # 默认激活audiometry profile，如需其他环境请通过启动参数覆盖
    # 启动参数示例：-Dspring.profiles.active=fgy200   -Dspring.profiles.active=audiometry
    active: fgy200

# Socket.IO服务器配置（无需Web服务器）
socketio:
  hostname: localhost
  port: 9092

# 应用配置
app:
  name: "设备通信服务"
  version: "1.0.0"
  description: "设备通信服务"

# 数据库配置
database:
  # 体检主数据库配置（MySQL 5.7）
  main:
    enabled: true
    type: mysql
    host: **************
    port: 3306
    database: physicalex-lkd
    username: pes
    password: Pes123!@#
    parameters: "characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai"

    # 连接池配置
    pool:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      test-on-borrow: true
      validation-query: "SELECT 1"

    # 超时配置
    timeout:
      connection-timeout: 30
      query-timeout: 60
      transaction-timeout: 300
# MinIO对象存储配置
minio:
  enabled: true  # 默认关闭，需要时启用
  endpoint: http://localhost:9000
  access-key: cIHSfxmIFVjpplRUQ80t
  secret-key: eJbm7pTyMWoylccTeuCJqFrz8A5NOmgR0YLzr6yW
  default-bucket: images
  # Java客户端优化配置 - 其他程序正常，问题在Java客户端
  connect-timeout: 30      # 连接超时（秒）- 保守设置，避免过长等待
  write-timeout: 120       # 写入超时（秒）- 2分钟，参考其他程序的设置
  read-timeout: 60         # 读取超时（秒）- 1分钟
  max-retry-count: 3       # 最大重试次数 - 适中重试
  retry-interval: 1000     # 重试间隔（毫秒）- 1秒间隔
  max-connections: 1       # 连接池最大连接数 - 单连接避免并发冲突
  keep-alive-duration: 5   # 连接保持时间（分钟）- 适中保持时间


