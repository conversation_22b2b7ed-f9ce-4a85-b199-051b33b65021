-- 电测听原始数据表
-- 用于存储原始TonePoint数据，供体检主程序生成PDF报告使用

CREATE TABLE audiometry_raw_data (
    -- 主键
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    
    -- 患者信息
    exam_no VARCHAR(50) NOT NULL COMMENT '体检号',
    customer_reg_id VARCHAR(36) NOT NULL COMMENT '客户登记ID',
    customer_reg_item_group_id VARCHAR(36) NOT NULL COMMENT '体检项目组ID',
    patient_name VARCHAR(100) NOT NULL COMMENT '患者姓名',
    patient_id_card VARCHAR(20) COMMENT '患者身份证号',
    
    -- 设备信息
    device_model VARCHAR(100) NOT NULL COMMENT '设备型号',
    
    -- 原始数据
    raw_data_json TEXT NOT NULL COMMENT '原始TonePoint数据JSON格式',
    
    -- 时间信息
    test_date DATE NOT NULL COMMENT '测试日期',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    
    -- 操作信息
    created_by VARCHAR(50) COMMENT '创建人',
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'PENDING_PDF' COMMENT '状态：PENDING_PDF-待生成PDF, PDF_GENERATED-已生成PDF, PDF_FAILED-生成失败',
    pdf_url VARCHAR(500) COMMENT 'PDF报告URL',
    pdf_generated_time DATETIME COMMENT 'PDF生成时间',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数'
) COMMENT='电测听原始数据表';

-- 创建索引
CREATE INDEX idx_audiometry_raw_exam_no ON audiometry_raw_data(exam_no);
CREATE INDEX idx_audiometry_raw_customer_reg_id ON audiometry_raw_data(customer_reg_id);
CREATE INDEX idx_audiometry_raw_item_group_id ON audiometry_raw_data(customer_reg_item_group_id);
CREATE INDEX idx_audiometry_raw_test_date ON audiometry_raw_data(test_date);
CREATE INDEX idx_audiometry_raw_status ON audiometry_raw_data(status);
CREATE INDEX idx_audiometry_raw_create_time ON audiometry_raw_data(create_time);

-- 添加注释
ALTER TABLE audiometry_raw_data COMMENT = '电测听原始数据表，存储完整的TonePoint数据供PDF报告生成使用';
