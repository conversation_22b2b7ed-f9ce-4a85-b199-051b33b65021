# FGY-200肺功能检测仪配置文件
# 使用方法：在主配置文件中添加 spring.profiles.include: fgy200

# FGY-200肺功能检测仪配置
fgy200:
  # 基本配置
  program-path: "D:\\Program Files\\健桥医电\\肺功能检测系统"
  database-file: "fei.mdb"
  patient-file: "Tester.ini"
  file-encoding: "GBK"
  his-code: "fgn"

  database:
    # 是否启用FGY-200数据库连接
    enabled: true

    # 数据库文件路径（根据实际安装路径调整）
    path: "D:\\Program Files\\健桥医电\\肺功能检测系统\\fei.mdb"

    # 数据库驱动类名
    driver: "net.ucanaccess.jdbc.UcanaccessDriver"

    # 连接池配置（可选）
    pool:
      # 最大连接数
      maximum-pool-size: 5
      # 最小空闲连接数
      minimum-idle: 1
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲超时时间（毫秒）
      idle-timeout: 600000
  # 报告图片保存配置
  report:
    # 报告图片保存目录
    image-directory: "D:\\20231025"
    # 图片格式
    image-format: "jpg"
    # 图片质量 (0.0-1.0)
    image-quality: 0.9
    # 报告图片文件名模式
    # 支持的占位符：
    # {examNo} - 体检号
    # {序号} 或 {index} - 序号（0,1,2等）
    # 示例：202501060001.0, 202501060001.1, 202501060001.2
    file-name-pattern: "{examNo}.{序号}"
    # 定时处理间隔（秒）
    process-interval: 30
    # 最大重试次数
    max-retry-count: 5
    # 任务超时时间（小时）
    task-timeout-hours: 2

  # 医生配置
  doctor-config:
    # 检查医生配置
    check-doctor:
      code: "00245"
      name: "白春艳"
      sign-pic: ""

    # 报告医生配置
    report-doctor:
      code: "00202"
      name: "武明玺"
      sign-pic: ""

    # 审核医生配置
    audit-doctor:
      code: "00202"
      name: "武明玺"
      sign-pic: ""

  # 数据库配置
  sql-config:
    select-by-patient-sql: "SELECT TOP 1 * FROM 存储数据表 WHERE ID = #{patientId} order by 时间 desc"
    count-by-patient-sql: "SELECT COUNT(*) FROM 存储数据表 WHERE ID = #{patientId}"
    # 查询参数
    query-params:
      date-format: "yyyy-MM-dd"
      time-format: "HH:mm:ss"

  # 连接配置
  device-connection:
    connection-timeout: 30
    retry-count: 3
    auto-reconnect: true

  # 监控配置
  monitoring:
    interval: 10
    timeout: 300

  # 结果完整性验证配置
  result-validation:
    # 是否启用结果完整性验证
    enabled: true

    # 必需的基础指标字段（这些字段必须有有效值）
    required-fields:
      - "FVC"      # 用力肺活量
      - "FEV1"     # 第一秒用力呼气量
      - "姓名"      # 患者姓名
      - "ID"       # 患者ID
      - "日期"      # 检测日期

    # 可选但重要的指标字段
    important-fields:
      - "FEV1%"    # FEV1百分比
      - "FEV_1"    # FEV1/FVC比值
      - "PEF"      # 呼气峰流速
      - "VC"       # 肺活量
      - "MVV"      # 最大通气量

    # 数值范围验证（用于检查数据合理性）
    value-ranges:
      FVC:
        min: 0.5
        max: 8.0
        unit: "L"
      FEV1:
        min: 0.3
        max: 6.0
        unit: "L"
      PEF:
        min: 2.0
        max: 15.0
        unit: "L/s"
      VC:
        min: 1.0
        max: 8.0
        unit: "L"
      MVV:
        min: 30
        max: 250
        unit: "L"

    # 患者状态验证
    patient-status:
      # 表示检测完成的状态值
      completed-status:
        - "配合良好"
        - "检测完成"
        - "正常完成"
        - "已完成"
      # 表示检测中或未完成的状态值
      incomplete-status:
        - "检测中"
        - "等待检测"
        - "准备中"
        - "未开始"
        - "中断"

    # 时间验证配置
    time-validation:
      # 是否验证检测时间必须在测量开始之后
      validate-test-time: true
      # 允许的时间偏差（秒），防止时钟不同步问题
      time-tolerance: 300

  # 肺功能指标配置
  lung-function:
    indicators:
      # 用力肺活量
      - code: "FVC"
        name: "用力肺活量"
        unit: "L"
        data-key: "FVC"
        enabled: true
        sort-order: 1
        abnormal-rules:
          # 基于预期值的判断（如果有预期FVC值）
          - name: "FVC严重偏低"
            condition: "#FVC_PRE != null and (#value / #FVC_PRE) < 0.6"
            flag: "1"
            description: "严重偏低"
            symbol: "↓↓"
            priority: 1
            enabled: true
          - name: "FVC偏低"
            condition: "#FVC_PRE != null and (#value / #FVC_PRE) < 0.8"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 2
            enabled: true
          - name: "FVC偏高"
            condition: "#FVC_PRE != null and (#value / #FVC_PRE) > 1.2"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 3
            enabled: true
          # 基于固定阈值的备用判断（当没有预期值时）
          - name: "FVC绝对偏低"
            condition: "#FVC_PRE == null and #value < 2.5"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 4
            enabled: true
          - name: "FVC绝对偏高"
            condition: "#FVC_PRE == null and #value > 6.0"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 5
            enabled: true

      # 第一秒用力呼气量
      - code: "FEV1"
        name: "第一秒用力呼气量"
        unit: "L"
        data-key: "FEV1"
        enabled: true
        sort-order: 2
        abnormal-rules:
          # 基于预期值的判断
          - name: "FEV1严重偏低"
            condition: "#FEV1_PRE != null and (#value / #FEV1_PRE) < 0.6"
            flag: "1"
            description: "严重偏低"
            symbol: "↓↓"
            priority: 1
            enabled: true
          - name: "FEV1偏低"
            condition: "#FEV1_PRE != null and (#value / #FEV1_PRE) < 0.8"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 2
            enabled: true
          - name: "FEV1偏高"
            condition: "#FEV1_PRE != null and (#value / #FEV1_PRE) > 1.2"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 3
            enabled: true
          # 基于固定阈值的备用判断
          - name: "FEV1绝对偏低"
            condition: "#FEV1_PRE == null and #value < 2.0"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 4
            enabled: true
          - name: "FEV1绝对偏高"
            condition: "#FEV1_PRE == null and #value > 5.0"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 5
            enabled: true

      # FEV1占预计值百分比（直接使用百分比值）
      - code: "FEV1_PERCENT"
        name: "FEV1占预计值百分比"
        unit: "%"
        data-key: "FEV1%"
        enabled: true
        sort-order: 3
        abnormal-rules:
          - name: "FEV1%严重偏低"
            condition: "#value < 60"
            flag: "1"
            description: "严重偏低"
            symbol: "↓↓"
            priority: 1
            enabled: true
          - name: "FEV1%偏低"
            condition: "#value < 80"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 2
            enabled: true
          - name: "FEV1%偏高"
            condition: "#value > 120"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 3
            enabled: true

      # FEV1/FVC比值（重要的阻塞性指标）
      - code: "FEV1_FVC"
        name: "FEV1/FVC比值"
        unit: "%"
        data-key: "FEV_1"
        enabled: true
        sort-order: 4
        abnormal-rules:
          # 基于实际FEV1和FVC值计算的比值判断
          - name: "FEV1/FVC严重偏低"
            condition: "#FEV1 != null and #FVC != null and (#FEV1 / #FVC * 100) < 60"
            flag: "1"
            description: "严重偏低"
            symbol: "↓↓"
            priority: 1
            enabled: true
          - name: "FEV1/FVC偏低"
            condition: "#FEV1 != null and #FVC != null and (#FEV1 / #FVC * 100) < 70"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 2
            enabled: true
          # 基于直接数据字段的备用判断
          - name: "FEV1/FVC直接偏低"
            condition: "(#FEV1 == null or #FVC == null) and #value < 70"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 3
            enabled: true

      # 呼气峰流速
      - code: "PEF"
        name: "呼气峰流速"
        unit: "L/s"
        data-key: "PEF"
        enabled: true
        sort-order: 5
        abnormal-rules:
          # 基于预期值的判断
          - name: "PEF偏低"
            condition: "#PEF_PRE != null and (#value / #PEF_PRE) < 0.8"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 1
            enabled: true
          - name: "PEF偏高"
            condition: "#PEF_PRE != null and (#value / #PEF_PRE) > 1.2"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 2
            enabled: true
          # 基于固定阈值的备用判断
          - name: "PEF绝对偏低"
            condition: "#PEF_PRE == null and #value < 6.0"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 3
            enabled: true
          - name: "PEF绝对偏高"
            condition: "#PEF_PRE == null and #value > 12.0"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 4
            enabled: true

      # 肺活量
      - code: "VC"
        name: "肺活量"
        unit: "L"
        data-key: "VC"
        enabled: true
        sort-order: 6
        abnormal-rules:
          # 基于预期值的判断
          - name: "VC偏低"
            condition: "#VC_PRE != null and (#value / #VC_PRE) < 0.8"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 1
            enabled: true
          - name: "VC偏高"
            condition: "#VC_PRE != null and (#value / #VC_PRE) > 1.2"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 2
            enabled: true
          # 基于固定阈值的备用判断
          - name: "VC绝对偏低"
            condition: "#VC_PRE == null and #value < 3.0"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 3
            enabled: true
          - name: "VC绝对偏高"
            condition: "#VC_PRE == null and #value > 6.5"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 4
            enabled: true

      # 最大通气量
      - code: "MVV"
        name: "最大通气量"
        unit: "L/min"
        data-key: "MVV"
        enabled: true
        sort-order: 7
        abnormal-rules:
          # 基于预期值的判断
          - name: "MVV偏低"
            condition: "#MVV_PRE != null and (#value / #MVV_PRE) < 0.8"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 1
            enabled: true
          - name: "MVV偏高"
            condition: "#MVV_PRE != null and (#value / #MVV_PRE) > 1.2"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 2
            enabled: true
          # 基于固定阈值的备用判断
          - name: "MVV绝对偏低"
            condition: "#MVV_PRE == null and #value < 80"
            flag: "1"
            description: "偏低"
            symbol: "↓"
            priority: 3
            enabled: true
          - name: "MVV绝对偏高"
            condition: "#MVV_PRE == null and #value > 180"
            flag: "1"
            description: "偏高"
            symbol: "↑"
            priority: 4
            enabled: true

    # 预期值计算配置（可选，如果设备提供预期值）
    predicted-values:
      # 是否启用预期值计算
      enabled: true

      # 预期值数据字段映射
      field-mapping:
        FVC_PRE: "FVC预计值"      # FVC预期值字段名
        FEV1_PRE: "FEV1预计值"    # FEV1预期值字段名
        PEF_PRE: "PEF预计值"      # PEF预期值字段名
        VC_PRE: "VC预计值"        # VC预期值字段名
        MVV_PRE: "MVV预计值"      # MVV预期值字段名

      # 预期值计算公式（如果设备不提供，可以根据年龄性别计算）
      calculation-formulas:
        # 男性FVC预期值计算公式示例
        FVC_PRE_MALE: "(0.0576 * #height - 0.026 * #age - 4.34)"
        # 女性FVC预期值计算公式示例
        FVC_PRE_FEMALE: "(0.0491 * #height - 0.019 * #age - 3.76)"



# 日志配置
logging:
  level:
    org.bj.common.device.processors.org.bj.device.processors.FGY200DeviceProcessor: DEBUG
    org.bj.config.FGY200DatabaseConfig: DEBUG
    net.ucanaccess: WARN

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
