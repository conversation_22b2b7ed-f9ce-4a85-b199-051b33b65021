let bmr = null;
let boneKg = null;
let boneRange = null;

let bodyAge = null;
let bodyScore = null;
let fatKg = null;
let fatPercent = null;
let fatSubcutKg = null;

let impedance = null;
let mineral = null;

let muscleKg = null;
let muscleRange = null;
let proteinPercent = null;
let vfal = null;
let waterECW = null;
let waterICW = null;
let waterKg = null;
let waterPercent = null;
group.itemList.forEach((item) => {
    if (item.name == '基础代谢') {
        bmr = item;
    }
    if (item.name == '肌肉量') {
        muscleKg = item;
    }
    if (item.name == '体骨骼量') {
        boneKg = item;
    }
    if (item.name == '体水分率') {
        waterPercent = item;
    }
    if (item.name == '体脂肪率') {
        fatPercent = item;
    }
    if (item.name == '内脏脂肪等级') {
        vfal = item;
    }
    if (item.name == '蛋白质率') {
        proteinPercent = item;
    }
    if (item.name == '脂肪量') {
        fatKg = item;
    }
    if (item.name == '无机盐量') {
        mineral = item;
    }
    if (item.name == '细胞外液') {
        waterECW = item;
    }
    if (item.name == '细胞内液') {
        waterICW = item;
    }
    if (item.name == '体水分量') {
        waterKg = item;
    }
    if (item.name == '人体阻抗率') {
        impedance = item;
    }
    if (item.name == '身体年龄') {
        bodyAge = item;
    }
    if (item.name == '身体得分') {
        bodyScore = item;
    }
    if (item.name == '皮下脂肪量') {
        fatSubcutKg = item;
    }
});
if (bmr) {
    bmr.itemResult.value = comData.bmr;
    let flag = comData.bmrAssess;
    let abnormalFlag = flag!=2?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '高';
    }
    if(flag==1)
    {
        abnormalDesc = '低';
    }
    bmr.itemResult.abnormalFlag = abnormalFlag;
    bmr.itemResult.abnormalFlagDesc = abnormalDesc;
    bmr.itemResult.valueRefRange = comData.bmrRange;
}
if (boneKg) {
    boneKg.itemResult.value = comData.boneKg;
    let flag = comData.boneAssess;
    let abnormalFlag = flag!=2?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '高';
    }
    if(flag==1)
    {
        abnormalDesc = '低';
    }
    boneKg.itemResult.abnormalFlag = abnormalFlag;
    boneKg.itemResult.abnormalFlagDesc = abnormalDesc;
    boneKg.itemResult.valueRefRange = comData.boneRange;
}
if (bodyAge) {
    bodyAge.itemResult.value = comData.bodyAge;
}
if (bodyScore) {
    bodyScore.itemResult.value = comData.bodyScore;
}
if (fatKg) {
    fatKg.itemResult.value = comData.fatKg;
}
if (fatPercent) {
    fatPercent.itemResult.value = comData.fatPercent;
    let flag = comData.fatAssess;
    let abnormalFlag = flag!=2?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '高';
    }
    if(flag==1)
    {
        abnormalDesc = '低';
    }
    fatPercent.itemResult.abnormalFlag = abnormalFlag;
    fatPercent.itemResult.abnormalFlagDesc = abnormalDesc;
    fatPercent.itemResult.valueRefRange = comData.fatRange;
}
if (fatSubcutKg) {
    fatSubcutKg.itemResult.value = comData.fatSubcutKg;
}
if (impedance) {
    impedance.itemResult.value = comData.impedance;
}
if (muscleKg) {
    muscleKg.itemResult.value = comData.muscleKg;
    let flag = comData.muscleAssess;
    let abnormalFlag = flag!=2?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '高';
    }
    if(flag==1)
    {
        abnormalDesc = '低';
    }
    muscleKg.itemResult.abnormalFlag = abnormalFlag;
    muscleKg.itemResult.abnormalFlagDesc = abnormalDesc;
    muscleKg.itemResult.valueRefRange = comData.muscleRange;
}
if (proteinPercent) {
    proteinPercent.itemResult.value = comData.proteinPercent;
    let flag = comData.proteinAssess;
    let abnormalFlag = flag!=2?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '高';
    }
    if(flag==1)
    {
        abnormalDesc = '低';
    }
    proteinPercent.itemResult.abnormalFlag = abnormalFlag;
    proteinPercent.itemResult.abnormalFlagDesc = abnormalDesc;
    proteinPercent.itemResult.valueRefRange = comData.proteinRange;
}
if (vfal) {
    vfal.itemResult.value = comData.vfal;
    let flag = comData.vfalAssess;
    let abnormalFlag = flag!=1?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '危险';
    }
    if(flag==2)
    {
        abnormalDesc = '警惕';
    }
    vfal.itemResult.abnormalFlag = abnormalFlag;
    vfal.itemResult.abnormalFlagDesc = abnormalDesc;
    vfal.itemResult.valueRefRange = comData.vfalRange;
}
if (waterECW) {
    waterECW.itemResult.value = comData.waterECW;
}
if (waterICW) {
    waterICW.itemResult.value = comData.waterICW;
}
if (waterKg) {
    waterKg.itemResult.value = comData.waterKg;
}
if (waterPercent) {
    waterPercent.itemResult.value = comData.waterPercent;
    let flag = comData.waterAssess;
    let abnormalFlag = flag!=2?1:0;
    let abnormalDesc = '正常';
    if(flag==0)
    {
        abnormalDesc = '高';
    }
    if(flag==1)
    {
        abnormalDesc = '低';
    }
    waterPercent.itemResult.abnormalFlag = abnormalFlag;
    waterPercent.itemResult.abnormalFlagDesc = abnormalDesc;
    waterPercent.itemResult.valueRefRange = comData.waterRange;
}



