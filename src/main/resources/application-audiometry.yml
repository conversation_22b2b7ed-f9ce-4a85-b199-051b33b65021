# 电测听设备配置文件
# 使用方法：在主配置文件中添加 spring.profiles.include: audiometry

# 电测听设备配置
audiometry:
  # 基本配置
  program-path: "C:/Program Files (x86)/Interacoustics/Diagnostic Suite"
  software-executable: "DiagnosticSuite.exe"
  his-code: "audiometry"

  # 设备连接配置
  device-connection:
    # 服务器地址
    server-host: "localhost"
    server-port: 47294
    # Hook端口
    hook-port: 9093
    # 连接超时时间（秒）
    connection-timeout: 30
    # 重试次数
    retry-count: 3
    # 自动重连
    auto-reconnect: true
    # API Key
    api-key: "ApiKey Public"

  # 监控配置
  monitoring:
    # 监控间隔（秒）
    interval: 30
    # 超时时间（秒）
    timeout: 300
    # 最大错误次数
    max-error-count: 5

  # 报告图片保存配置
  report:
    # 报告图片保存目录
    image-directory: "C:/AudiometryReports"
    # 图片格式
    image-format: "png"
    # 图片质量 (0.0-1.0)
    image-quality: 0.9
    # 报告图片文件名模式
    # 支持的占位符：
    # {examNo} - 体检号
    # {序号} 或 {index} - 序号（0,1,2等）
    # 示例：202501060001.0, 202501060001.1, 202501060001.2
    file-name-pattern: "{examNo}.{序号}"
    # 定时处理间隔（秒）
    process-interval: 30
    # 最大重试次数
    max-retry-count: 5
    # 任务超时时间（小时）
    task-timeout-hours: 2

  # 医生配置
  doctor-config:
    # 检查医生配置
    check-doctor:
      code: "00201"
      name: "张拴良"
      sign-pic: ""

    # 报告医生配置
    report-doctor:
      code: "00201"
      name: "张拴良"
      sign-pic: ""

    # 审核医生配置
    audit-doctor:
      code: "00201"
      name: "张拴良"
      sign-pic: ""

  # 数据存储配置
  data-storage:
    # 存储类型：transfer（直接传输）或 db（数据库存储）
    storage-type: "db"
    # 是否启用数据验证
    validation-enabled: true

  # 软件管理配置
  software-management:
    # 是否在测量完成后自动关闭软件
    auto-close-after-measurement: false
    # 是否在新患者开始前自动启动软件
    auto-start-before-measurement: true
    # 是否在启动前先关闭已运行的软件（确保软件重新初始化）
    close-before-start: false
    # 软件关闭等待时间（秒）
    close-wait-timeout: 10
    # 软件启动等待时间（秒）
    start-wait-timeout: 15
    # 是否以管理员权限启动软件（解决C盘权限问题）
    run-as-admin: true
    # 是否自动检测权限需求
    auto-detect-admin-required: true
    # 软件重启策略配置
    restart-strategy:
      # 是否启用软件重启策略（每次测量前重启软件）
      enabled: true
      # 重启前的等待时间（秒）
      wait-before-restart: 2
      # 重启后的等待时间（秒）
      wait-after-restart: 3
      # 最大重启尝试次数
      max-restart-attempts: 3
  
  # 听力测试指标配置
  hearing-test:
    indicators:
      # 左耳气导阈值
      - code: "AC_LEFT"
        name: "左耳气导阈值"
        unit: "dB HL"
        data-key: "acLeft"
        enabled: true
        sort-order: 1
        abnormal-rules:
          - name: "轻度听力损失"
            condition: "#value >= 26 and #value <= 40"
            flag: "1"
            description: "轻度听力损失"
            symbol: "↓"
            priority: 1
            enabled: true
          - name: "中度听力损失"
            condition: "#value >= 41 and #value <= 55"
            flag: "1"
            description: "中度听力损失"
            symbol: "↓↓"
            priority: 2
            enabled: true
          - name: "中重度听力损失"
            condition: "#value >= 56 and #value <= 70"
            flag: "1"
            description: "中重度听力损失"
            symbol: "↓↓↓"
            priority: 3
            enabled: true
          - name: "重度听力损失"
            condition: "#value >= 71 and #value <= 90"
            flag: "1"
            description: "重度听力损失"
            symbol: "↓↓↓↓"
            priority: 4
            enabled: true
          - name: "极重度听力损失"
            condition: "#value > 90"
            flag: "1"
            description: "极重度听力损失"
            symbol: "↓↓↓↓↓"
            priority: 5
            enabled: true
      
      # 右耳气导阈值
      - code: "AC_RIGHT"
        name: "右耳气导阈值"
        unit: "dB HL"
        data-key: "acRight"
        enabled: true
        sort-order: 2
        abnormal-rules:
          - name: "轻度听力损失"
            condition: "#value >= 26 and #value <= 40"
            flag: "1"
            description: "轻度听力损失"
            symbol: "↓"
            priority: 1
            enabled: true
          - name: "中度听力损失"
            condition: "#value >= 41 and #value <= 55"
            flag: "1"
            description: "中度听力损失"
            symbol: "↓↓"
            priority: 2
            enabled: true
          - name: "中重度听力损失"
            condition: "#value >= 56 and #value <= 70"
            flag: "1"
            description: "中重度听力损失"
            symbol: "↓↓↓"
            priority: 3
            enabled: true
          - name: "重度听力损失"
            condition: "#value >= 71 and #value <= 90"
            flag: "1"
            description: "重度听力损失"
            symbol: "↓↓↓↓"
            priority: 4
            enabled: true
          - name: "极重度听力损失"
            condition: "#value > 90"
            flag: "1"
            description: "极重度听力损失"
            symbol: "↓↓↓↓↓"
            priority: 5
            enabled: true
      
      # 左耳骨导阈值
      - code: "BC_LEFT"
        name: "左耳骨导阈值"
        unit: "dB HL"
        data-key: "bcLeft"
        enabled: true
        sort-order: 3
        abnormal-rules:
          - name: "骨导异常"
            condition: "#value > 25"
            flag: "1"
            description: "骨导异常"
            symbol: "↓"
            priority: 1
            enabled: true
      
      # 右耳骨导阈值
      - code: "BC_RIGHT"
        name: "右耳骨导阈值"
        unit: "dB HL"
        data-key: "bcRight"
        enabled: true
        sort-order: 4
        abnormal-rules:
          - name: "骨导异常"
            condition: "#value > 25"
            flag: "1"
            description: "骨导异常"
            symbol: "↓"
            priority: 1
            enabled: true
      
      # 平均听阈
      - code: "PTA_LEFT"
        name: "左耳平均听阈"
        unit: "dB HL"
        data-key: "ptaLeft"
        enabled: true
        sort-order: 5
        abnormal-rules:
          - name: "听力正常"
            condition: "#value <= 25"
            flag: "0"
            description: "正常"
            symbol: ""
            priority: 1
            enabled: true
          - name: "轻度听力损失"
            condition: "#value >= 26 and #value <= 40"
            flag: "1"
            description: "轻度听力损失"
            symbol: "↓"
            priority: 2
            enabled: true
          - name: "中度听力损失"
            condition: "#value >= 41 and #value <= 55"
            flag: "1"
            description: "中度听力损失"
            symbol: "↓↓"
            priority: 3
            enabled: true
          - name: "中重度听力损失"
            condition: "#value >= 56 and #value <= 70"
            flag: "1"
            description: "中重度听力损失"
            symbol: "↓↓↓"
            priority: 4
            enabled: true
          - name: "重度听力损失"
            condition: "#value >= 71 and #value <= 90"
            flag: "1"
            description: "重度听力损失"
            symbol: "↓↓↓↓"
            priority: 5
            enabled: true
          - name: "极重度听力损失"
            condition: "#value > 90"
            flag: "1"
            description: "极重度听力损失"
            symbol: "↓↓↓↓↓"
            priority: 6
            enabled: true

      - code: "PTA_RIGHT"
        name: "右耳平均听阈"
        unit: "dB HL"
        data-key: "ptaRight"
        enabled: true
        sort-order: 6
        abnormal-rules:
          - name: "听力正常"
            condition: "#value <= 25"
            flag: "0"
            description: "正常"
            symbol: ""
            priority: 1
            enabled: true
          - name: "轻度听力损失"
            condition: "#value >= 26 and #value <= 40"
            flag: "1"
            description: "轻度听力损失"
            symbol: "↓"
            priority: 2
            enabled: true
          - name: "中度听力损失"
            condition: "#value >= 41 and #value <= 55"
            flag: "1"
            description: "中度听力损失"
            symbol: "↓↓"
            priority: 3
            enabled: true
          - name: "中重度听力损失"
            condition: "#value >= 56 and #value <= 70"
            flag: "1"
            description: "中重度听力损失"
            symbol: "↓↓↓"
            priority: 4
            enabled: true
          - name: "重度听力损失"
            condition: "#value >= 71 and #value <= 90"
            flag: "1"
            description: "重度听力损失"
            symbol: "↓↓↓↓"
            priority: 5
            enabled: true
          - name: "极重度听力损失"
            condition: "#value > 90"
            flag: "1"
            description: "极重度听力损失"
            symbol: "↓↓↓↓↓"
            priority: 6
            enabled: true

# 日志配置
logging:
  level:
    org.bj.device.processors.AudiometryDeviceProcessor: DEBUG
    org.bj.config.AudiometryProperties: DEBUG
    org.bj.device.processors.service.AudiometryService: DEBUG
    org.bj.util.WindowsProcessUtil: DEBUG

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
