<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>本地websocket测试</title>
        <meta name="robots" content="all" />
        <meta name="keywords" content="本地,websocket,测试工具" />
        <meta name="description" content="本地,websocket,测试工具" />
        <style>
            .btn-group{
                display: inline-block;
            }
        </style>
    </head>
    <body>
        <input type='text' value='通信地址, ws://开头..' class="form-control" style='width:390px;display:inline'
               id='wsaddr' />
        <div class="btn-group" >
            <button type="button" class="btn btn-default" onclick='addsocket();'>连接</button>
            <button type="button" class="btn btn-default" onclick='closesocket();'>断开</button>
            <button type="button" class="btn btn-default" onclick='$("#wsaddr").val("")'>清空</button>
        </div>
        <div class="row">
            <div id="output" style="border:1px solid #ccc;height:365px;overflow: auto;margin: 20px 0;"></div>
            <input type="text" id='message' class="form-control" style='width:810px' placeholder="待发信息" onkeydown="en(event);">
            <span class="input-group-btn">
				<button class="btn btn-default" type="button" onclick="doSend();">发送</button>
			</span>
        </div>
        </div>
    </body>

    <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
    <script language="javascript" type="text/javascript">
        function formatDate(now) {
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var date = now.getDate();
            var hour = now.getHours();
            var minute = now.getMinutes();
            var second = now.getSeconds();
            return year + "-" + (month = month < 10 ? ("0" + month) : month) + "-" + (date = date < 10 ? ("0" + date) : date) +
                " " + (hour = hour < 10 ? ("0" + hour) : hour) + ":" + (minute = minute < 10 ? ("0" + minute) : minute) + ":" + (
                    second = second < 10 ? ("0" + second) : second);
        }
        var output;
        var websocket;

        function init() {
            output = document.getElementById("output");
            testWebSocket();
        }

        function addsocket() {
            var wsaddr = $("#wsaddr").val();
            if (wsaddr == '') {
                alert("请填写websocket的地址");
                return false;
            }
            StartWebSocket(wsaddr);
        }

        function closesocket() {
            WebSocketElectronic.close();
        }

        function StartWebSocket(wsUri) {
            WebSocketElectronic = new WebSocket(wsUri);
            WebSocketElectronic.onopen = function(evt) {
                onOpen(evt)
            };
            WebSocketElectronic.onclose = function(evt) {
                onClose(evt)
            };
            WebSocketElectronic.onmessage = function(evt) {
                onMessage(evt)
            };
            WebSocketElectronic.onerror = function(evt) {
                onError(evt)
            };
        }

        function onOpen(evt) {
            writeToScreen("<span style='color:red'>连接成功，现在你可以发送信息啦！！！</span>");
        }

        function onClose(evt) {
            writeToScreen("<span style='color:red'>websocket连接已断开!!!</span>");
            WebSocketElectronic.close();
        }

        function onMessage(evt) {
            writeToScreen('<span style="color:blue">服务端回应&nbsp;' + formatDate(new Date()) + '</span><br/><span class="bubble">' +
                evt.data + '</span>');
        }

        function onError(evt) {
            writeToScreen('<span style="color: red;">发生错误:</span> ' + evt.data);
        }

        function doSend() {
            var message = $("#message").val();
            if (message == '') {
                alert("请先填写发送信息");
                $("#message").focus();
                return false;
            }
            if (typeof WebSocketElectronic === "undefined") {
                alert("websocket还没有连接，或者连接失败，请检测");
                return false;
            }
            if (WebSocketElectronic.readyState == 3) {
                alert("websocket已经关闭，请重新连接");
                return false;
            }
            console.log(WebSocketElectronic);
            $("#message").val('');
            writeToScreen('<span style="color:green">你发送的信息&nbsp;' + formatDate(new Date()) + '</span><br/>' + message);
            WebSocketElectronic.send(message);
        }

        function writeToScreen(message) {
            var div = "<div class='newmessage'>" + message + "</div>";
            var d = $("#output");
            var d = d[0];
            var doScroll = d.scrollTop == d.scrollHeight - d.clientHeight;
            $("#output").append(div);
            if (doScroll) {
                d.scrollTop = d.scrollHeight - d.clientHeight;
            }
        }


        function en(event) {
            var evt = evt ? evt : (window.event ? window.event : null);
            if (evt.keyCode == 13) {
                doSend()
            }
        }
    </script>

</html>
