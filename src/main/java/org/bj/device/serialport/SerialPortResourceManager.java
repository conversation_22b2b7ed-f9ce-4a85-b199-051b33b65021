package org.bj.device.serialport;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 串口资源管理器
 * 统一管理串口的打开、关闭和资源清理
 * 解决用户切换时串口占用问题
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Service
public class SerialPortResourceManager {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /** 串口使用映射：端口描述符 -> 会话标识符 */
    private final ConcurrentMap<String, String> portSessionMapping = new ConcurrentHashMap<>();

    /** 串口实例映射：端口描述符 -> 串口实例 */
    private final ConcurrentMap<String, com.fazecast.jSerialComm.SerialPort> portInstanceMapping = new ConcurrentHashMap<>();

    /** 会话串口映射：会话标识符 -> 端口描述符列表 */
    private final ConcurrentMap<String, List<String>> sessionPortMapping = new ConcurrentHashMap<>();

    /** 会话信息映射：会话标识符 -> 会话信息 */
    private final ConcurrentMap<String, SessionInfo> sessionInfoMapping = new ConcurrentHashMap<>();

    /**
     * 会话信息类
     */
    public static class SessionInfo {
        private String userId;
        private String patientId;
        private String patientName;
        private long createTime;

        public SessionInfo(String userId, String patientId, String patientName) {
            this.userId = userId;
            this.patientId = patientId;
            this.patientName = patientName;
            this.createTime = System.currentTimeMillis();
        }

        // Getters
        public String getUserId() { return userId; }
        public String getPatientId() { return patientId; }
        public String getPatientName() { return patientName; }
        public long getCreateTime() { return createTime; }

        public String getSessionDescription() {
            return String.format("用户=%s, 患者=%s(%s)", userId, patientName, patientId);
        }
    }

    /**
     * 生成会话标识符
     */
    private String generateSessionId(String userId, String patientId) {
        if (patientId == null || patientId.trim().isEmpty()) {
            return userId; // 兜底：只使用用户ID
        }
        return userId + ":" + patientId;
    }

    /**
     * 检查串口是否被占用
     */
    public boolean isPortOccupied(String portDescriptor) {
        if (portDescriptor == null || portDescriptor.trim().isEmpty()) {
            return false;
        }

        try {
            // 1. 检查内存映射
            if (portSessionMapping.containsKey(portDescriptor)) {
                String sessionId = portSessionMapping.get(portDescriptor);
                SessionInfo sessionInfo = sessionInfoMapping.get(sessionId);
                log.debug("串口在内存映射中被占用: 端口={}, 会话={}", portDescriptor,
                    sessionInfo != null ? sessionInfo.getSessionDescription() : sessionId);
                return true;
            }

            // 2. 检查实际串口状态
            com.fazecast.jSerialComm.SerialPort serialPort = com.fazecast.jSerialComm.SerialPort.getCommPort(portDescriptor);
            if (serialPort != null && serialPort.isOpen()) {
                log.debug("串口实际处于打开状态: 端口={}", portDescriptor);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.warn("检查串口占用状态失败: 端口={}", portDescriptor, e);
            return false;
        }
    }

    /**
     * 获取串口占用者信息
     */
    public String getPortOccupier(String portDescriptor) {
        String sessionId = portSessionMapping.get(portDescriptor);
        if (sessionId == null) {
            return null;
        }

        SessionInfo sessionInfo = sessionInfoMapping.get(sessionId);
        return sessionInfo != null ? sessionInfo.getUserId() : sessionId;
    }

    /**
     * 获取串口占用会话信息
     */
    public SessionInfo getPortOccupierSession(String portDescriptor) {
        String sessionId = portSessionMapping.get(portDescriptor);
        return sessionId != null ? sessionInfoMapping.get(sessionId) : null;
    }

    /**
     * 检查是否为同一会话（用户+患者相同）
     */
    public boolean isSameSession(String portDescriptor, String userId, String patientId) {
        String currentSessionId = portSessionMapping.get(portDescriptor);
        if (currentSessionId == null) {
            return false;
        }

        String newSessionId = generateSessionId(userId, patientId);
        return currentSessionId.equals(newSessionId);
    }

    /**
     * 注册串口使用（支持患者信息）
     */
    public boolean registerPortUsage(String portDescriptor, String userId, String patientId, String patientName,
                                   com.fazecast.jSerialComm.SerialPort serialPort) {
        if (portDescriptor == null || userId == null || serialPort == null) {
            return false;
        }

        try {
            String sessionId = generateSessionId(userId, patientId);

            // 1. 检查是否已被其他会话占用
            String currentSessionId = portSessionMapping.get(portDescriptor);
            if (currentSessionId != null && !currentSessionId.equals(sessionId)) {
                SessionInfo currentSession = sessionInfoMapping.get(currentSessionId);
                log.warn("串口仍被其他会话占用，强制接管: 端口={}, 当前会话={}, 请求会话={}",
                    portDescriptor,
                    currentSession != null ? currentSession.getSessionDescription() : currentSessionId,
                    sessionId);
                // 强制释放后重新注册
                forceReleasePort(portDescriptor);
            }

            // 2. 注册使用关系
            portSessionMapping.put(portDescriptor, sessionId);
            portInstanceMapping.put(portDescriptor, serialPort);

            // 3. 创建会话信息
            SessionInfo sessionInfo = new SessionInfo(userId, patientId, patientName);
            sessionInfoMapping.put(sessionId, sessionInfo);

            // 4. 更新会话端口映射
            sessionPortMapping.computeIfAbsent(sessionId, k -> new ArrayList<>()).add(portDescriptor);

            log.info("注册串口使用: 端口={}, 会话={}", portDescriptor, sessionInfo.getSessionDescription());

            // 5. 发布端口打开事件
            eventPublisher.publishEvent(org.bj.debug.SerialPortDebugEvent.portOpened(this, portDescriptor));

            return true;

        } catch (Exception e) {
            log.error("注册串口使用失败: 端口={}, 用户={}, 患者={}", portDescriptor, userId, patientId, e);
            return false;
        }
    }

    /**
     * 注册串口使用（兼容性方法，不包含患者信息）
     */
    public boolean registerPortUsage(String portDescriptor, String userId, com.fazecast.jSerialComm.SerialPort serialPort) {
        return registerPortUsage(portDescriptor, userId, null, null, serialPort);
    }

    /**
     * 支持用户切换的串口接管
     * 强制踢掉原用户，让新用户使用串口
     */
    public boolean takeoverPort(String portDescriptor, String oldUserId, String newUserId, com.fazecast.jSerialComm.SerialPort newSerialPort) {
        if (portDescriptor == null || newUserId == null || newSerialPort == null) {
            return false;
        }

        try {
            log.info("执行串口接管: 端口={}, 原用户={}, 新用户={}", portDescriptor, oldUserId, newUserId);

            // 1. 强制释放原有连接
            forceReleasePort(portDescriptor);

            // 2. 注册新用户使用
            boolean registered = registerPortUsage(portDescriptor, newUserId, newSerialPort);

            if (registered) {
                log.info("串口接管成功: 端口={}, 原用户={}, 新用户={}", portDescriptor, oldUserId, newUserId);
            } else {
                log.error("串口接管失败: 端口={}, 原用户={}, 新用户={}", portDescriptor, oldUserId, newUserId);
            }

            return registered;

        } catch (Exception e) {
            log.error("串口接管失败: 端口={}, 原用户={}, 新用户={}", portDescriptor, oldUserId, newUserId, e);
            return false;
        }
    }

    /**
     * 注销串口使用（支持患者信息）
     */
    public boolean unregisterPortUsage(String portDescriptor, String userId, String patientId) {
        if (portDescriptor == null || userId == null) {
            return false;
        }

        try {
            String sessionId = generateSessionId(userId, patientId);

            // 1. 检查是否为当前会话
            String currentSessionId = portSessionMapping.get(portDescriptor);
            if (currentSessionId == null) {
                log.debug("串口未被注册使用: 端口={}", portDescriptor);
                return true;
            }

            if (!currentSessionId.equals(sessionId)) {
                SessionInfo currentSession = sessionInfoMapping.get(currentSessionId);
                log.warn("无权注销串口使用: 端口={}, 当前会话={}, 请求会话={}",
                    portDescriptor,
                    currentSession != null ? currentSession.getSessionDescription() : currentSessionId,
                    sessionId);
                return false;
            }

            // 2. 关闭串口
            com.fazecast.jSerialComm.SerialPort serialPort = portInstanceMapping.get(portDescriptor);
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.closePort();
                log.info("关闭串口: 端口={}", portDescriptor);
            }

            // 3. 清理映射关系
            portSessionMapping.remove(portDescriptor);
            portInstanceMapping.remove(portDescriptor);

            // 4. 更新会话端口映射
            List<String> sessionPorts = sessionPortMapping.get(sessionId);
            if (sessionPorts != null) {
                sessionPorts.remove(portDescriptor);
                if (sessionPorts.isEmpty()) {
                    sessionPortMapping.remove(sessionId);
                    sessionInfoMapping.remove(sessionId);
                }
            }

            SessionInfo sessionInfo = sessionInfoMapping.get(sessionId);
            log.info("注销串口使用: 端口={}, 会话={}", portDescriptor,
                sessionInfo != null ? sessionInfo.getSessionDescription() : sessionId);

            // 5. 发布端口关闭事件
            eventPublisher.publishEvent(org.bj.debug.SerialPortDebugEvent.portClosed(this, portDescriptor));

            return true;

        } catch (Exception e) {
            log.error("注销串口使用失败: 端口={}, 用户={}, 患者={}", portDescriptor, userId, patientId, e);
            return false;
        }
    }

    /**
     * 注销串口使用（兼容性方法）
     */
    public boolean unregisterPortUsage(String portDescriptor, String userId) {
        return unregisterPortUsage(portDescriptor, userId, null);
    }

    /**
     * 强制释放串口
     */
    public void forceReleasePort(String portDescriptor) {
        if (portDescriptor == null || portDescriptor.trim().isEmpty()) {
            return;
        }
        
        try {
            log.info("强制释放串口: 端口={}", portDescriptor);
            
            // 1. 获取当前占用会话
            String currentSessionId = portSessionMapping.get(portDescriptor);
            
            // 2. 关闭串口实例
            com.fazecast.jSerialComm.SerialPort serialPort = portInstanceMapping.get(portDescriptor);
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.closePort();
                log.info("强制关闭串口实例: 端口={}", portDescriptor);
            }
            
            // 3. 尝试获取新的串口实例并关闭（防止遗漏）
            com.fazecast.jSerialComm.SerialPort newSerialPort = com.fazecast.jSerialComm.SerialPort.getCommPort(portDescriptor);
            if (newSerialPort != null && newSerialPort.isOpen()) {
                newSerialPort.closePort();
                log.info("强制关闭新串口实例: 端口={}", portDescriptor);
            }
            
            // 4. 清理所有映射关系
            portSessionMapping.remove(portDescriptor);
            portInstanceMapping.remove(portDescriptor);

            // 5. 清理会话端口映射
            if (currentSessionId != null) {
                List<String> sessionPorts = sessionPortMapping.get(currentSessionId);
                if (sessionPorts != null) {
                    sessionPorts.remove(portDescriptor);
                    if (sessionPorts.isEmpty()) {
                        sessionPortMapping.remove(currentSessionId);
                        sessionInfoMapping.remove(currentSessionId);
                    }
                }
            }
            
            SessionInfo sessionInfo = sessionInfoMapping.get(currentSessionId);
            log.info("强制释放串口完成: 端口={}, 原会话={}", portDescriptor,
                sessionInfo != null ? sessionInfo.getSessionDescription() : currentSessionId);
            
            // 6. 发布端口关闭事件
            eventPublisher.publishEvent(org.bj.debug.SerialPortDebugEvent.portClosed(this, portDescriptor));

        } catch (Exception e) {
            log.error("强制释放串口失败: 端口={}", portDescriptor, e);
        }
    }

    /**
     * 清理用户的所有串口资源
     */
    public void cleanupUserPorts(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return;
        }

        try {
            log.info("清理用户串口资源: 用户={}", userId);

            // 1. 查找用户的所有会话
            List<String> userSessions = new ArrayList<>();
            for (Map.Entry<String, SessionInfo> entry : sessionInfoMapping.entrySet()) {
                if (userId.equals(entry.getValue().getUserId())) {
                    userSessions.add(entry.getKey());
                }
            }

            if (userSessions.isEmpty()) {
                log.debug("用户{}没有活跃的会话", userId);
                return;
            }

            // 2. 逐个清理会话的端口
            int releasedCount = 0;
            for (String sessionId : userSessions) {
                try {
                    List<String> sessionPorts = sessionPortMapping.get(sessionId);
                    if (sessionPorts != null) {
                        List<String> portsToRelease = new ArrayList<>(sessionPorts);
                        for (String portDescriptor : portsToRelease) {
                            try {
                                SessionInfo sessionInfo = sessionInfoMapping.get(sessionId);
                                unregisterPortUsage(portDescriptor, userId,
                                    sessionInfo != null ? sessionInfo.getPatientId() : null);
                                releasedCount++;
                                log.debug("释放用户串口: 用户={}, 端口={}, 会话={}", userId, portDescriptor, sessionId);
                            } catch (Exception e) {
                                log.error("释放用户串口失败: 用户={}, 端口={}, 会话={}", userId, portDescriptor, sessionId, e);
                                // 强制释放
                                forceReleasePort(portDescriptor);
                                releasedCount++;
                            }
                        }
                    }

                    // 清理会话信息
                    sessionPortMapping.remove(sessionId);
                    sessionInfoMapping.remove(sessionId);

                } catch (Exception e) {
                    log.error("清理用户会话失败: 用户={}, 会话={}", userId, sessionId, e);
                }
            }

            log.info("用户串口资源清理完成: 用户={}, 释放端口数量={}", userId, releasedCount);

        } catch (Exception e) {
            log.error("清理用户串口资源失败: 用户={}", userId, e);
        }
    }

    /**
     * 获取所有占用的端口信息
     */
    public Map<String, String> getAllOccupiedPorts() {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, String> entry : portSessionMapping.entrySet()) {
            String portDescriptor = entry.getKey();
            String sessionId = entry.getValue();
            SessionInfo sessionInfo = sessionInfoMapping.get(sessionId);
            result.put(portDescriptor, sessionInfo != null ? sessionInfo.getSessionDescription() : sessionId);
        }
        return result;
    }

    /**
     * 获取用户占用的端口列表
     */
    public List<String> getUserPorts(String userId) {
        List<String> userPorts = new ArrayList<>();
        for (Map.Entry<String, SessionInfo> entry : sessionInfoMapping.entrySet()) {
            if (userId.equals(entry.getValue().getUserId())) {
                String sessionId = entry.getKey();
                List<String> sessionPorts = sessionPortMapping.get(sessionId);
                if (sessionPorts != null) {
                    userPorts.addAll(sessionPorts);
                }
            }
        }
        return userPorts;
    }

    /**
     * 获取用户的所有会话信息
     */
    public List<SessionInfo> getUserSessions(String userId) {
        List<SessionInfo> userSessions = new ArrayList<>();
        for (SessionInfo sessionInfo : sessionInfoMapping.values()) {
            if (userId.equals(sessionInfo.getUserId())) {
                userSessions.add(sessionInfo);
            }
        }
        return userSessions;
    }

    /**
     * 获取串口实例
     */
    public com.fazecast.jSerialComm.SerialPort getPortInstance(String portDescriptor) {
        return portInstanceMapping.get(portDescriptor);
    }

    /**
     * 验证串口状态是否有效
     */
    public boolean isPortValid(String portDescriptor) {
        try {
            com.fazecast.jSerialComm.SerialPort serialPort = portInstanceMapping.get(portDescriptor);
            return serialPort != null && serialPort.isOpen();
        } catch (Exception e) {
            log.warn("验证串口状态失败: 端口={}", portDescriptor, e);
            return false;
        }
    }

    /**
     * 清理无效的串口会话
     */
    public void cleanupInvalidSessions() {
        try {
            List<String> invalidPorts = new ArrayList<>();

            for (Map.Entry<String, com.fazecast.jSerialComm.SerialPort> entry : portInstanceMapping.entrySet()) {
                String portDescriptor = entry.getKey();
                com.fazecast.jSerialComm.SerialPort serialPort = entry.getValue();

                if (serialPort == null || !serialPort.isOpen()) {
                    invalidPorts.add(portDescriptor);
                    log.debug("发现无效串口会话: 端口={}", portDescriptor);
                }
            }

            for (String portDescriptor : invalidPorts) {
                forceReleasePort(portDescriptor);
                log.info("清理无效串口会话: 端口={}", portDescriptor);
            }

            if (!invalidPorts.isEmpty()) {
                log.info("清理无效串口会话完成: 清理数量={}", invalidPorts.size());
            }

        } catch (Exception e) {
            log.error("清理无效串口会话失败", e);
        }
    }

    /**
     * 清理所有资源
     */
    public void cleanup() {
        log.info("清理所有串口资源");
        
        try {
            // 1. 关闭所有串口
            for (Map.Entry<String, com.fazecast.jSerialComm.SerialPort> entry : portInstanceMapping.entrySet()) {
                try {
                    com.fazecast.jSerialComm.SerialPort serialPort = entry.getValue();
                    if (serialPort != null && serialPort.isOpen()) {
                        serialPort.closePort();
                        log.debug("关闭串口: 端口={}", entry.getKey());
                    }
                } catch (Exception e) {
                    log.warn("关闭串口失败: 端口={}", entry.getKey(), e);
                }
            }
            
            // 2. 清理所有映射
            portSessionMapping.clear();
            portInstanceMapping.clear();
            sessionPortMapping.clear();
            sessionInfoMapping.clear();
            
            log.info("所有串口资源清理完成");
            
        } catch (Exception e) {
            log.error("清理所有串口资源失败", e);
        }
    }
}
