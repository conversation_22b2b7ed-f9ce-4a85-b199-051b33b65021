package org.bj.device.serialport;

import com.fazecast.jSerialComm.SerialPort;

public class SerialPortMonitor {
    private SerialPort[] lastKnownPorts;

    public SerialPortMonitor() {
        lastKnownPorts = SerialPort.getCommPorts();
    }

    public void startMonitoring() {
        new Thread(() -> {
            while (true) {
                SerialPort[] currentPorts = SerialPort.getCommPorts();
                for (SerialPort sp : currentPorts) {
                    boolean found = false;
                    for (SerialPort knownPort : lastKnownPorts) {
                        if (sp.getSystemPortName().equals(knownPort.getSystemPortName())) {
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        System.out.println("New port detected: " + sp.getSystemPortName());
                        // Handle port connection
                    }
                }

                for (SerialPort knownPort : lastKnownPorts) {
                    boolean found = false;
                    for (SerialPort sp : currentPorts) {
                        if (sp.getSystemPortName().equals(knownPort.getSystemPortName())) {
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        System.out.println("Port disconnected: " + knownPort.getSystemPortName());
                        // Handle port disconnection
                    }
                }

                lastKnownPorts = currentPorts;

                try {
                    Thread.sleep(5000);  // Check every 5 seconds
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }
}
