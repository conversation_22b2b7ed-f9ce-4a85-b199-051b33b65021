package org.bj.device.serialport;

import com.fazecast.jSerialComm.SerialPort;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.entity.ConnectRequest;
import org.bj.device.entity.DeviceProperties;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SerialPortFactoryImpl implements SerialPortFactory {

    @Override
    public SerialPort createSerialPort(ConnectRequest request) {
        // 确保设备属性存在
        request.ensureDeviceProperties();

        // 直接从DeviceProperties中获取串口配置
        DeviceProperties deviceProperties = request.getDeviceProperties();

        String portDescriptor = deviceProperties.getPortDescriptor();
        int baudRate = deviceProperties.getBaudRate() != null ? deviceProperties.getBaudRate() : 9600;
        int dataBits = deviceProperties.getDataBits() != null ? deviceProperties.getDataBits() : 8;
        int stopBits = deviceProperties.getStopBits() != null ? deviceProperties.getStopBits() : 1;
        int parity = deviceProperties.getParity() != null ? deviceProperties.getParity() : 0;
        int readTimeout = deviceProperties.getReadTimeout() != null ? deviceProperties.getReadTimeout() : 10000;
        int writeTimeout = deviceProperties.getWriteTimeout() != null ? deviceProperties.getWriteTimeout() : 10000;

        log.info("创建串口: 端口={}, 波特率={}, 数据位={}, 停止位={}, 校验位={}",
                portDescriptor, baudRate, dataBits, stopBits, parity);

        SerialPort serialPort = SerialPort.getCommPort(portDescriptor);
        serialPort.setComPortParameters(baudRate, dataBits, stopBits, parity);
        serialPort.setComPortTimeouts(0, readTimeout, writeTimeout); // timeoutMode = 0
        return serialPort;
    }


    @Override
    public boolean validateSerialPort(SerialPort serialPort) {
        if (serialPort == null || !serialPort.openPort()) {
            log.error("Failed to open serial port.");
            return false;
        }

        // Send a test command to check device response
        byte[] testCommand = "TEST".getBytes();
        serialPort.writeBytes(testCommand, testCommand.length);

        byte[] readBuffer = new byte[1024];
        int numRead = serialPort.readBytes(readBuffer, readBuffer.length);

        if (numRead > 0) {
            String response = new String(readBuffer, 0, numRead);
            log.info("Device response: {}", response);
            return true;
        } else {
            log.error("No response from the device.");
            return false;
        }
    }
}