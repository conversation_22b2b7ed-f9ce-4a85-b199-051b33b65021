package org.bj.device;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.entity.DeviceContext;
import org.bj.device.entity.DeviceProperties;
import org.bj.device.entity.CommonDeviceResponse;
import org.bj.constants.ResponseTypeConstants;
import org.bj.constants.EventConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 设备处理器管理器
 * 负责管理各种设备的专有处理器，提供统一的设备操作接口
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@Component
public class DeviceProcessorManager {

    /**
     * 设备处理器映射
     */
    private final ConcurrentMap<String, DeviceProcessor> deviceProcessors = new ConcurrentHashMap<>();

    /**
     * 设备处理器工厂
     */
    @Autowired(required = false)
    private DeviceProcessorFactory deviceProcessorFactory;

    /**
     * 注册设备处理器
     */
    public void registerDeviceProcessor(String deviceModel, DeviceProcessor processor) {
        deviceProcessors.put(deviceModel, processor);
        log.info("注册设备处理器: 设备型号={}, 处理器={}", deviceModel, processor.getClass().getSimpleName());
    }

    /**
     * 获取设备处理器
     */
    public DeviceProcessor getDeviceProcessor(String deviceModel) {
        if (deviceModel == null) {
            return null;
        }

        DeviceProcessor processor = deviceProcessors.get(deviceModel);

        // 如果没有找到处理器，尝试通过工厂创建
        if (processor == null && deviceProcessorFactory != null) {
            processor = deviceProcessorFactory.createProcessor(deviceModel);
            if (processor != null) {
                registerDeviceProcessor(deviceModel, processor);
            }
        }

        return processor;
    }

    /**
     * 检查是否有专有处理器
     */
    public boolean hasSpecializedProcessor(String deviceModel) {
        return getDeviceProcessor(deviceModel) != null;
    }

    /**
     * 获取已注册的设备型号列表
     */
    public java.util.Set<String> getRegisteredDeviceModels() {
        return deviceProcessors.keySet();
    }

    /**
     * 处理设备连接
     */
    public boolean handleDeviceConnection(SocketIOClient client, String portDescriptor, String userId, String deviceModel) {
        try {
            DeviceProcessor processor = getDeviceProcessor(deviceModel);
            if (processor != null) {
                log.info("使用专有处理器处理设备连接: 设备类型={}, 端口={}, 用户={}", deviceModel, portDescriptor, userId);

                // 使用旧版本的方法签名以保持向后兼容
                return processor.handleConnection(client, portDescriptor, userId);
            } else {
                log.debug("未找到专有处理器，使用通用处理逻辑: 设备类型={}", deviceModel);
                return false;
            }
        } catch (Exception e) {
            log.error("处理设备连接失败: 设备类型={}, 端口={}", deviceModel, portDescriptor, e);
            return false;
        }
    }

    /**
     * 处理设备断开连接
     */
    public boolean handleDeviceDisconnection(SocketIOClient client, String portDescriptor, String userId, String deviceType) {
        try {
            DeviceProcessor processor = getDeviceProcessor(deviceType);
            if (processor != null) {
                log.info("使用专有处理器处理设备断开: 设备类型={}, 端口={}, 用户={}", deviceType, portDescriptor, userId);

                // 使用旧版本的方法签名以保持向后兼容
                return processor.handleDisconnection(client, portDescriptor, userId);
            } else {
                log.debug("未找到专有处理器，使用通用断开逻辑: 设备类型={}", deviceType);
                return false;
            }
        } catch (Exception e) {
            log.error("处理设备断开失败: 设备类型={}, 端口={}", deviceType, portDescriptor, e);
            return false;
        }
    }

    /**
     * 处理设备数据
     */
    public boolean handleDeviceData(DeviceContext context, String rawData) {
        try {
            String deviceType = context.getDeviceType();
            DeviceProcessor processor = getDeviceProcessor(deviceType);

            if (processor != null) {
                log.debug("使用专有处理器处理设备数据: 设备类型={}, 数据长度={}", deviceType, rawData != null ? rawData.length() : 0);

                return processor.handleData(context, rawData);
            } else {
                log.debug("未找到专有处理器，使用通用数据处理逻辑: 设备类型={}", deviceType);
                return false;
            }
        } catch (Exception e) {
            log.error("处理设备数据失败: 设备类型={}", context.getDeviceType(), e);
            return false;
        }
    }

    /**
     * 发送设备指令
     */
    public boolean sendDeviceCommand(SocketIOClient client, String portDescriptor, String command, String userId, String deviceType) {
        try {
            DeviceProcessor processor = getDeviceProcessor(deviceType);
            if (processor != null) {
                log.info("使用专有处理器发送设备指令: 设备类型={}, 指令={}, 端口={}", deviceType, command, portDescriptor);

                // 使用旧版本的方法签名以保持向后兼容
                return processor.sendCommand(client, portDescriptor, command, userId);
            } else {
                log.debug("未找到专有处理器，无法发送设备指令: 设备类型={}, 指令={}", deviceType, command);
                return false;
            }
        } catch (Exception e) {
            log.error("发送设备指令失败: 设备类型={}, 指令={}", deviceType, command, e);
            return false;
        }
    }

    /**
     * 获取设备状态
     */
    public String getDeviceStatus(String portDescriptor, String deviceType) {
        try {
            DeviceProcessor processor = getDeviceProcessor(deviceType);
            if (processor != null) {
                // 使用旧版本的方法签名以保持向后兼容
                return processor.getDeviceStatus(portDescriptor);
            } else {
                return "UNKNOWN";
            }
        } catch (Exception e) {
            log.error("获取设备状态失败: 设备类型={}, 端口={}", deviceType, portDescriptor, e);
            return "ERROR";
        }
    }

    /**
     * 重置设备
     */
    public boolean resetDevice(SocketIOClient client, String portDescriptor, String userId, String deviceType) {
        try {
            DeviceProcessor processor = getDeviceProcessor(deviceType);
            if (processor != null) {
                log.info("使用专有处理器重置设备: 设备类型={}, 端口={}, 用户={}", deviceType, portDescriptor, userId);

                // 使用旧版本的方法签名以保持向后兼容
                return processor.resetDevice(client, portDescriptor, userId);
            } else {
                log.debug("未找到专有处理器，无法重置设备: 设备类型={}", deviceType);
                return false;
            }
        } catch (Exception e) {
            log.error("重置设备失败: 设备类型={}, 端口={}", deviceType, portDescriptor, e);
            return false;
        }
    }

    /**
     * 校准设备
     */
    public boolean calibrateDevice(SocketIOClient client, String portDescriptor, String userId, String deviceType) {
        try {
            DeviceProcessor processor = getDeviceProcessor(deviceType);
            if (processor != null) {
                log.info("使用专有处理器校准设备: 设备类型={}, 端口={}, 用户={}", deviceType, portDescriptor, userId);

                // 使用旧版本的方法签名以保持向后兼容
                return processor.calibrateDevice(client, portDescriptor, userId);
            } else {
                log.debug("未找到专有处理器，无法校准设备: 设备类型={}", deviceType);
                return false;
            }
        } catch (Exception e) {
            log.error("校准设备失败: 设备类型={}, 端口={}", deviceType, portDescriptor, e);
            return false;
        }
    }

    /**
     * 获取所有已注册的设备类型
     */
    public java.util.Set<String> getRegisteredDeviceTypes() {
        return new java.util.HashSet<>(deviceProcessors.keySet());
    }

    /**
     * 获取设备处理器数量
     */
    public int getProcessorCount() {
        return deviceProcessors.size();
    }

    /**
     * 移除设备处理器
     */
    public DeviceProcessor removeDeviceProcessor(String deviceType) {
        DeviceProcessor removed = deviceProcessors.remove(deviceType.toUpperCase());
        if (removed != null) {
            log.info("移除设备处理器: 设备类型={}", deviceType);
        }
        return removed;
    }

    /**
     * 清空所有设备处理器
     */
    public void clearAllProcessors() {
        deviceProcessors.clear();
        log.info("已清空所有设备处理器");
    }

    /**
     * 获取设备处理器信息
     */
    public String getProcessorInfo(String deviceType) {
        DeviceProcessor processor = getDeviceProcessor(deviceType);
        if (processor != null) {
            return String.format("设备类型: %s, 处理器: %s, 版本: %s", deviceType, processor.getClass().getSimpleName(), processor.getVersion());
        }
        return "未找到处理器";
    }

    /**
     * 检查设备处理器是否支持指定功能
     */
    public boolean supportsFeature(String deviceType, String feature) {
        DeviceProcessor processor = getDeviceProcessor(deviceType);
        if (processor != null) {
            return processor.supportsFeature(feature);
        }
        return false;
    }

    // =================== 主动查询相关方法 ===================

    /**
     * 检查设备是否支持主动查询结果
     */
    public boolean supportsActiveQuery(String deviceType) {
        return supportsFeature(deviceType, "ACTIVE_QUERY");
    }

    /**
     * 检查设备是否需要主动查询结果
     */
    public boolean needsActiveQuery(DeviceContext context) {
        if (context == null || context.getDeviceProperties() == null) {
            return false;
        }

        DeviceProperties deviceProperties = context.getDeviceProperties();
        Boolean needsQuery = deviceProperties.getNeedsActiveQuery();

        return needsQuery != null && needsQuery;
    }

    /**
     * 启动主动查询结果
     */
    public boolean startActiveQuery(DeviceContext context) {
        try {
            if (!needsActiveQuery(context)) {
                log.debug("设备不需要主动查询: 设备类型={}", context.getDeviceType());
                return false;
            }

            DeviceProcessor processor = getDeviceProcessor(context.getDeviceType());
            if (processor == null) {
                log.warn("未找到设备处理器，无法启动主动查询: 设备类型={}", context.getDeviceType());
                return false;
            }

            // 获取查询指令
            String queryCommand = getQueryCommand(context);
            if (queryCommand == null || queryCommand.trim().isEmpty()) {
                log.warn("设备需要主动查询但未配置查询指令: 设备类型={}", context.getDeviceType());
                sendUnifiedTestResult(context, null, "未配置查询指令", false);
                return false;
            }

            // 发送查询开始状态（可选，用于调试）
            log.info("开始主动查询: 设备类型={}, 指令={}", context.getDeviceType(), queryCommand);

            // 使用设备处理器发送查询指令
            boolean success = processor.sendCommand(context, queryCommand);

            if (success) {
                log.info("主动查询指令发送成功: 设备类型={}, 指令={}", context.getDeviceType(), queryCommand);
                return true;
            } else {
                log.error("主动查询指令发送失败: 设备类型={}, 指令={}", context.getDeviceType(), queryCommand);
                sendUnifiedTestResult(context, null, "查询指令发送失败", false);
                return false;
            }

        } catch (Exception e) {
            log.error("启动主动查询失败: 设备类型={}", context.getDeviceType(), e);
            sendUnifiedTestResult(context, null, "启动查询异常: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * 处理主动查询结果
     */
    public boolean handleActiveQueryResult(DeviceContext context, Object resultData) {
        try {
            log.info("处理主动查询结果: 设备类型={}, 数据类型={}",
                context.getDeviceType(), resultData != null ? resultData.getClass().getSimpleName() : "null");

            if (resultData == null) {
                log.warn("查询结果为空: 设备类型={}", context.getDeviceType());
                sendUnifiedTestResult(context, null, "查询结果为空", false);
                return false;
            }

            // 发送统一的测试结果事件
            sendUnifiedTestResult(context, resultData, null, true);

            log.info("主动查询结果处理完成: 设备类型={}", context.getDeviceType());
            return true;

        } catch (Exception e) {
            log.error("处理主动查询结果失败: 设备类型={}", context.getDeviceType(), e);
            sendUnifiedTestResult(context, null, "处理结果异常: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * 取消主动查询
     */
    public boolean cancelActiveQuery(DeviceContext context) {
        try {
            log.info("取消主动查询: 设备类型={}", context.getDeviceType());

            // 发送查询取消结果（可选）
            log.info("主动查询已取消: 设备类型={}", context.getDeviceType());

            return true;

        } catch (Exception e) {
            log.error("取消主动查询失败: 设备类型={}", context.getDeviceType(), e);
            return false;
        }
    }

    /**
     * 获取查询指令
     */
    private String getQueryCommand(DeviceContext context) {
        DeviceProperties deviceProperties = context.getDeviceProperties();
        if (deviceProperties == null) {
            return null;
        }

        String queryCommand = deviceProperties.getQueryResultCommand();
        if (queryCommand == null || queryCommand.trim().isEmpty()) {
            // 使用默认查询指令
            return "QUERY_RESULTS";
        }

        return queryCommand.trim();
    }

    /**
     * 发送统一的测试结果事件
     */
    private void sendUnifiedTestResult(DeviceContext context, Object resultData, String errorMessage, boolean success) {
        try {
            // 计算测量耗时
            long duration = 0L;
            if (context.getMeasurementStartTime() != null) {
                duration = System.currentTimeMillis() - context.getMeasurementStartTime();
            }

            // 创建统一的设备响应数据
            CommonDeviceResponse.CommonDeviceResponseBuilder responseBuilder = CommonDeviceResponse.builder()
                .data(resultData)
                .sessionId(context.getSessionId())
                .deviceId(context.getDeviceProperties() != null ? context.getDeviceProperties().getDeviceId() : null)
                .deviceModel(context.getDeviceModel())
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(context.getExamItemInfo())
                .hasError(!success)
                .isComplete(success);

            // 设置响应类型
            if (success) {
                responseBuilder.responseType(ResponseTypeConstants.MEASUREMENT_RESULT);
                responseBuilder.dataStatus("PROCESSED");
            } else {
                responseBuilder.responseType(ResponseTypeConstants.ERROR);
                responseBuilder.dataStatus("ERROR");
                responseBuilder.errorMessage(errorMessage);
            }

            // 添加患者信息
            if (context.getPatientInfo() != null) {
                responseBuilder.patientInfo(context.getPatientInfo());
            }

            // 添加时间信息
            if (context.getMeasurementStartTime() != null) {
                responseBuilder.startTime(context.getMeasurementStartTime());
                responseBuilder.endTime(System.currentTimeMillis());
            }

            CommonDeviceResponse response = responseBuilder.build();

            // 发送统一的testResult事件
            context.getClient().sendEvent(EventConstants.TEST_RESULT, response.toMap());

            log.debug("发送统一测试结果事件: 成功={}, 设备={}, 上下文={}",
                success, context.getDeviceModel(), context.getContextId());

        } catch (Exception e) {
            log.error("发送统一测试结果事件失败: 上下文={}", context.getContextId(), e);
        }
    }
}
