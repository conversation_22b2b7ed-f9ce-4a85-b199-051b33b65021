package org.bj.device;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.Getter;
import org.bj.device.entity.DeviceContext;

/**
 * 设备处理器接口
 * 定义设备专有处理器的标准接口
 * 
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
public interface DeviceProcessor {
    
    /**
     * 获取设备类型
     */
    String getDeviceModel();
    
    /**
     * 获取处理器版本
     */
    default String getVersion() {
        return "1.0.0";
    }
    
    /**
     * 获取处理器描述
     */
    default String getDescription() {
        return getDeviceModel() + "设备处理器";
    }
    
    /**
     * 处理设备连接（通用版本）
     * @param context 设备上下文，包含所有连接所需的信息
     * @return 连接是否成功
     */
    boolean handleConnection(DeviceContext context);

    /**
     * 处理设备断开连接（通用版本）
     * @param context 设备上下文
     * @return 断开是否成功
     */
    boolean handleDisconnection(DeviceContext context);

    /**
     * 处理设备连接（向后兼容版本）
     * @deprecated 使用 handleConnection(DeviceContext context) 替代
     */
    @Deprecated
    default boolean handleConnection(SocketIOClient client, String portDescriptor, String userId) {
        // 提供默认实现以保持向后兼容
        throw new UnsupportedOperationException("请使用 handleConnection(DeviceContext context) 方法");
    }

    /**
     * 处理设备断开连接（向后兼容版本）
     * @deprecated 使用 handleDisconnection(DeviceContext context) 替代
     */
    @Deprecated
    default boolean handleDisconnection(SocketIOClient client, String portDescriptor, String userId) {
        // 提供默认实现以保持向后兼容
        throw new UnsupportedOperationException("请使用 handleDisconnection(DeviceContext context) 方法");
    }
    
    /**
     * 处理设备数据
     */
    boolean handleData(DeviceContext context, String rawData);
    
    /**
     * 发送设备指令（通用版本）
     * @param context 设备上下文
     * @param command 指令内容
     * @return 发送是否成功
     */
    boolean sendCommand(DeviceContext context, String command);

    /**
     * 获取设备状态（通用版本）
     * @param context 设备上下文
     * @return 设备状态
     */
    String getDeviceStatus(DeviceContext context);

    /**
     * 发送设备指令（向后兼容版本）
     * @deprecated 使用 sendCommand(DeviceContext context, String command) 替代
     */
    @Deprecated
    default boolean sendCommand(SocketIOClient client, String portDescriptor, String command, String userId) {
        throw new UnsupportedOperationException("请使用 sendCommand(DeviceContext context, String command) 方法");
    }

    /**
     * 获取设备状态（向后兼容版本）
     * @deprecated 使用 getDeviceStatus(DeviceContext context) 替代
     */
    @Deprecated
    default String getDeviceStatus(String portDescriptor) {
        throw new UnsupportedOperationException("请使用 getDeviceStatus(DeviceContext context) 方法");
    }
    
    /**
     * 重置设备（通用版本）
     * @param context 设备上下文
     * @return 重置是否成功
     */
    default boolean resetDevice(DeviceContext context) {
        return sendCommand(context, "RESET");
    }

    /**
     * 校准设备（通用版本）
     * @param context 设备上下文
     * @return 校准是否成功
     */
    default boolean calibrateDevice(DeviceContext context) {
        return sendCommand(context, "CALIBRATE");
    }

    /**
     * 重置设备（向后兼容版本）
     * @deprecated 使用 resetDevice(DeviceContext context) 替代
     */
    @Deprecated
    default boolean resetDevice(SocketIOClient client, String portDescriptor, String userId) {
        throw new UnsupportedOperationException("请使用 resetDevice(DeviceContext context) 方法");
    }

    /**
     * 校准设备（向后兼容版本）
     * @deprecated 使用 calibrateDevice(DeviceContext context) 替代
     */
    @Deprecated
    default boolean calibrateDevice(SocketIOClient client, String portDescriptor, String userId) {
        throw new UnsupportedOperationException("请使用 calibrateDevice(DeviceContext context) 方法");
    }
    
    /**
     * 检查是否支持指定功能
     */
    default boolean supportsFeature(String feature) {
        return false;
    }
    
    /**
     * 初始化处理器
     */
    default void initialize() {
        // 默认空实现
    }
    
    /**
     * 销毁处理器
     */
    default void destroy() {
        // 默认空实现
    }

    // =================== 监控相关方法 ===================

    /**
     * 执行监控检查
     * 由全局监控服务调用，用于检查设备状态或查询结果
     *
     * @param context 设备上下文
     * @param customData 自定义数据（如患者信息等）
     * @return 监控是否成功执行
     */
    default boolean executeMonitoring(DeviceContext context, Object customData) {
        // 默认实现：不执行任何监控
        return true;
    }

    /**
     * 检查监控是否已完成
     * 用于判断是否应该停止监控任务
     *
     * @param context 设备上下文
     * @param customData 自定义数据（如患者信息等）
     * @return 监控是否已完成
     */
    default boolean isMonitoringCompleted(DeviceContext context, Object customData) {
        // 默认实现：总是返回false，表示需要持续监控
        return false;
    }

    /**
     * 获取监控配置
     * 返回该设备类型的监控配置信息
     *
     * @return 监控配置，如果返回null则使用默认配置
     */
    default MonitoringConfig getMonitoringConfig() {
        // 默认配置：10秒间隔，300秒超时，最多5次错误
        return new MonitoringConfig(10, 300, 5);
    }

    /**
     * 监控配置类
     */
    class MonitoringConfig {
        private final int intervalSeconds;
        private final int timeoutSeconds;
        private final int maxErrorCount;

        public MonitoringConfig(int intervalSeconds, int timeoutSeconds, int maxErrorCount) {
            this.intervalSeconds = intervalSeconds;
            this.timeoutSeconds = timeoutSeconds;
            this.maxErrorCount = maxErrorCount;
        }

        public int getIntervalSeconds() { return intervalSeconds; }
        public int getTimeoutSeconds() { return timeoutSeconds; }
        public int getMaxErrorCount() { return maxErrorCount; }
    }
}
