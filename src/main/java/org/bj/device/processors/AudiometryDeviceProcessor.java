package org.bj.device.processors;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.bj.device.audiometry.entity.*;
import org.bj.device.entity.*;
import org.bj.device.util.DeviceStatusSender;
import org.bj.util.AgeRange;
import org.bj.util.HearingCorrectionSystem;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import org.bj.device.DeviceProcessor;

import org.bj.device.audiometry.client.AudiometryHttpClient;
import org.bj.device.audiometry.parser.AudiometryDataParser;
import org.bj.constants.EventConstants;
import org.bj.service.GlobalDeviceMonitoringService;
import org.bj.config.AudiometryProperties;
import org.bj.util.WindowsProcessUtil;
import org.bj.device.processors.service.AudiometryService;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 电测听设备处理器
 * 支持OtoAccess ADI Server接口的电测听设备
 * <p>
 * 设备特点：
 * 1. 通过HTTP接口与OtoAccess ADI Server通信
 * 2. 支持Hook钩子机制接收设备状态变化
 * 3. 多步骤流程：设置诊所→设置检查员→添加患者→启动软件→获取结果
 * 4. Base64编码的XML测试结果数据
 * 5. 支持双耳多频率纯音测听
 *
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
@Profile("audiometry")
public class AudiometryDeviceProcessor implements DeviceProcessor {

    /**
     * 设备型号
     */
    private static final String DEVICE_MODEL = "丹麦国际听力电测听";

    /**
     * 默认Hook端口
     */
    private static final int DEFAULT_HOOK_PORT = 9092;

    /**
     * HTTP客户端
     */
    @Autowired
    private AudiometryHttpClient httpClient;

    /**
     * 数据解析器
     */
    @Autowired
    private AudiometryDataParser dataParser;

    /**
     * 设备连接状态映射
     */
    private final ConcurrentHashMap<String, String> deviceStatusMap = new ConcurrentHashMap<>();

    /**
     * 患者状态映射
     */
    private final ConcurrentHashMap<String, String> patientStatusMap = new ConcurrentHashMap<>();

    /**
     * 全局设备监控服务
     */
    @Autowired
    private GlobalDeviceMonitoringService globalMonitoringService;

    /**
     * 电测听配置属性
     */
    @Autowired
    private AudiometryProperties audiometryProperties;

    /**
     * Windows进程工具
     */
    @Autowired
    private WindowsProcessUtil windowsProcessUtil;

    /**
     * 电测听数据保存服务
     */
    @Autowired
    private AudiometryService audiometryService;

    /**
     * 软件启动状态跟踪
     */
    private volatile boolean softwareStarted = false;

    @Override
    public String getDeviceModel() {
        return DEVICE_MODEL;
    }

    @Override
    public String getVersion() {
        return "1.0.0";
    }

    @Override
    public String getDescription() {
        return "电测听设备处理器 - 支持OtoAccess ADI Server接口";
    }

    @Override
    public boolean handleConnection(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            String userId = context.getSessionId();

            log.info("电测听设备连接处理开始: 设备ID={}, 用户={}", deviceId, userId);

            // 1. 检查服务器连接
            if (!httpClient.checkConnection()) {
                String errorMsg = "无法连接到电测听服务器";
                log.error(errorMsg);
                // 发送统一设备状态事件
                DeviceStatusSender.sendConnectionFailed(context.getClient(), context, errorMsg, "SERVER_CONNECTION_FAILED");
                // 保持兼容性，同时发送传统事件
                context.getClient().sendEvent("deviceConnectionError", createErrorEvent(deviceId, errorMsg));
                return false;
            }

            // 2. 注册Hook钩子
            try {
                String hookUri = httpClient.registerHook(DEFAULT_HOOK_PORT);
                log.info("Hook注册成功: {}", hookUri);
            } catch (Exception e) {
                log.warn("Hook注册失败，继续连接流程", e);
            }

            // 3. 设置诊所信息
            if (!setupClinic()) {
                String errorMsg = "设置诊所信息失败";
                log.error(errorMsg);
                context.getClient().sendEvent("deviceConnectionError", createErrorEvent(deviceId, errorMsg));
                return false;
            }

            // 4. 设置检查员信息
            if (!setupExaminer(context.getOperatorInfo())) {
                String errorMsg = "设置检查员信息失败";
                log.error(errorMsg);
                context.getClient().sendEvent("deviceConnectionError", createErrorEvent(deviceId, errorMsg));
                return false;
            }

            // 5. 清空并添加患者信息
            if (!setupPatient(context.getPatientInfo())) {
                String errorMsg = "设置患者信息失败";
                log.error(errorMsg);
                context.getClient().sendEvent("deviceConnectionError", createErrorEvent(deviceId, errorMsg));
                return false;
            }

            // 6. 启动设备软件（如果配置启用）
            if (audiometryProperties.getSoftwareManagement().isAutoStartBeforeMeasurement()) {
                if (!startDeviceSoftware()) {
                    String errorMsg = "启动设备软件失败";
                    log.error(errorMsg);
                    context.getClient().sendEvent("deviceConnectionError", createErrorEvent(deviceId, errorMsg));
                    return false;
                }
            }

            // 7. 更新设备状态
            deviceStatusMap.put(deviceId, "CONNECTED");
            context.setStatus(DeviceContext.ContextStatus.CONNECTED);

            // 8. 发送连接成功事件
            // 发送统一设备状态事件
            DeviceStatusSender.sendConnected(context.getClient(), context);

            // 保持兼容性，同时发送传统事件
            Map<String, Object> connectionEvent = new HashMap<>();
            connectionEvent.put("deviceId", deviceId);
            connectionEvent.put("status", "CONNECTED");
            connectionEvent.put("message", "电测听设备连接成功，请在设备上进行测试");
            connectionEvent.put("timestamp", System.currentTimeMillis());
            context.getClient().sendEvent("deviceConnected", connectionEvent);

            // 9. 注册到全局监控服务
            GlobalDeviceMonitoringService.GlobalMonitoringConfig config =
                    new GlobalDeviceMonitoringService.GlobalMonitoringConfig(30, 300, 5);
            globalMonitoringService.registerDeviceProcessor(DEVICE_MODEL, this, config);
            globalMonitoringService.registerMonitoringTask(deviceId, DEVICE_MODEL, context, null);

            log.info("电测听设备连接成功: 设备ID={}", deviceId);
            return true;

        } catch (Exception e) {
            log.error("电测听设备连接失败", e);
            return false;
        }
    }

    @Override
    public boolean handleDisconnection(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            log.info("电测听设备断开连接: 设备ID={}", deviceId);

            // 1. 取消监控任务
            globalMonitoringService.unregisterMonitoringTask(deviceId);

            // 2. 清理状态
            deviceStatusMap.remove(deviceId);
            patientStatusMap.entrySet().removeIf(entry ->
                    entry.getKey().startsWith(deviceId + "_"));

            // 3. 更新上下文状态
            context.setStatus(DeviceContext.ContextStatus.DISCONNECTED);

            // 4. 发送断开连接事件
            // 发送统一设备状态事件
            DeviceStatusSender.sendDisconnected(context.getClient(), context);

            // 保持兼容性，同时发送传统事件
            Map<String, Object> disconnectionEvent = new HashMap<>();
            disconnectionEvent.put("deviceId", deviceId);
            disconnectionEvent.put("status", "DISCONNECTED");
            disconnectionEvent.put("timestamp", System.currentTimeMillis());
            context.getClient().sendEvent("deviceDisconnected", disconnectionEvent);

            log.info("电测听设备断开连接完成: 设备ID={}", deviceId);
            return true;

        } catch (Exception e) {
            log.error("电测听设备断开连接失败", e);
            return false;
        }
    }

    @Override
    public boolean handleData(DeviceContext context, String rawData) {
        // 电测听设备不通过串口传输数据，而是通过HTTP接口查询
        // 这个方法主要用于处理手动触发的数据查询请求
        try {
            log.debug("电测听数据处理: {}", rawData);

            if ("QUERY_RESULTS".equals(rawData)) {
                return queryLatestResults(context);
            }

            return true;
        } catch (Exception e) {
            log.error("电测听数据处理失败", e);
            return false;
        }
    }

    @Override
    public boolean sendCommand(DeviceContext context, String command) {
        try {
            String deviceId = generateDeviceId(context);
            log.info("电测听发送指令: 设备ID={}, 指令={}", deviceId, command);

            return switch (command.toUpperCase()) {
                case "START_MEASUREMENT" -> startMeasurement(context);
                case "QUERY_RESULTS" -> queryLatestResults(context);
                case "RESET" -> resetDevice(context);
                case "START_SOFTWARE" -> startDeviceSoftware();
                case "STOP_SOFTWARE" -> stopDeviceSoftware();
                default -> {
                    log.warn("不支持的电测听指令: {}", command);
                    yield false;
                }
            };

        } catch (Exception e) {
            log.error("电测听发送指令失败: 指令={}", command, e);
            return false;
        }
    }

    @Override
    public String getDeviceStatus(DeviceContext context) {
        String deviceId = generateDeviceId(context);
        return deviceStatusMap.getOrDefault(deviceId, "DISCONNECTED");
    }

    @Override
    public boolean resetDevice(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            log.info("重置电测听设备: 设备ID={}", deviceId);

            // 1. 取消监控任务
            globalMonitoringService.unregisterMonitoringTask(deviceId);

            // 2. 关闭当前软件
            stopDeviceSoftware();

            // 3. 清理患者状态
            patientStatusMap.entrySet().removeIf(entry ->
                    entry.getKey().startsWith(deviceId + "_"));

            // 4. 重新设置患者信息
            if (!setupPatient(context.getPatientInfo())) {
                log.error("重置时设置患者信息失败");
                return false;
            }

            // 5. 重置设备状态
            deviceStatusMap.put(deviceId, "CONNECTED");

            // 6. 重新注册监控任务
            globalMonitoringService.registerMonitoringTask(deviceId, DEVICE_MODEL, context, null);

            // 7. 发送重置事件
            Map<String, Object> resetEvent = new HashMap<>();
            resetEvent.put("deviceId", deviceId);
            resetEvent.put("status", "RESET_COMPLETED");
            resetEvent.put("timestamp", System.currentTimeMillis());

            context.getClient().sendEvent("deviceReset", resetEvent);

            log.info("电测听设备重置完成: 设备ID={}", deviceId);
            return true;

        } catch (Exception e) {
            log.error("电测听设备重置失败", e);
            return false;
        }
    }

    @Override
    public boolean supportsFeature(String feature) {
        return switch (feature) {
            case "HTTP_COMMUNICATION" -> true;
            case "HOOK_MECHANISM" -> true;
            case "PATIENT_SETUP" -> true;
            case "RESULT_MONITORING" -> true;
            case "XML_DATA_PARSING" -> true;
            case "MULTI_FREQUENCY_TESTING" -> true;
            case "BILATERAL_TESTING" -> true;
            case "QUERY_RESULTS" -> true; // 电测听设备支持查询功能
            case "SOFTWARE_CONTROL" -> true; // 支持软件启动和关闭控制
            case "AUTO_SOFTWARE_MANAGEMENT" -> true; // 支持自动软件管理
            default -> false;
        };
    }

    @Override
    public void initialize() {
        log.info("初始化电测听设备处理器");
        deviceStatusMap.clear();
        patientStatusMap.clear();
    }

    @Override
    public void destroy() {
        log.info("销毁电测听设备处理器");

        // 关闭软件
        stopDeviceSoftware();

        // 取消所有监控任务
        for (String deviceId : deviceStatusMap.keySet()) {
            globalMonitoringService.unregisterMonitoringTask(deviceId);
        }

        // 清理状态
        deviceStatusMap.clear();
        patientStatusMap.clear();

        log.info("电测听设备处理器销毁完成");
    }

    // =================== 私有辅助方法 ===================

    /**
     * 生成设备ID
     */
    private String generateDeviceId(DeviceContext context) {
        return DEVICE_MODEL + "_" + context.getSessionId();
    }

    /**
     * 创建错误事件
     */
    private Map<String, Object> createErrorEvent(String deviceId, String errorMessage) {
        Map<String, Object> errorEvent = new HashMap<>();
        errorEvent.put("deviceId", deviceId);
        errorEvent.put("error", errorMessage);
        errorEvent.put("timestamp", System.currentTimeMillis());
        return errorEvent;
    }

    /**
     * 设置诊所信息
     */
    private boolean setupClinic() {
        try {
            AudiometryClinic clinic = AudiometryClinic.createDefault();
            return httpClient.setClinic(clinic);
        } catch (Exception e) {
            log.error("设置诊所信息失败", e);
            return false;
        }
    }

    /**
     * 设置检查员信息
     */
    private boolean setupExaminer(OperatorInfo operatorInfo) {
        try {
            AudiometryExaminer examiner = AudiometryExaminer.fromOperatorInfo(operatorInfo);
            return httpClient.setExaminer(examiner);
        } catch (Exception e) {
            log.error("设置检查员信息失败", e);
            return false;
        }
    }

    /**
     * 设置患者信息
     */
    private boolean setupPatient(PatientInfo patientInfo) {
        try {
            // 清空现有患者
            httpClient.clearPatients();

            // 添加新患者
            AudiometryPatient patient = AudiometryPatient.fromPatientInfo(patientInfo);
            return httpClient.addPatient(patient);
        } catch (Exception e) {
            log.error("设置患者信息失败", e);
            return false;
        }
    }

    /**
     * 启动设备软件
     */
    private boolean startDeviceSoftware() {
        try {
            String softwarePath = audiometryProperties.getFullSoftwarePath();
            log.info("开始启动电测听软件: {}", softwarePath);

            // 打印当前配置状态（调试用）
            var softwareMgmt = audiometryProperties.getSoftwareManagement();
            var restartStrategy = softwareMgmt.getRestartStrategy();

            log.info("=== 软件管理配置状态 ===");
            log.info("autoCloseAfterMeasurement: {}", softwareMgmt.isAutoCloseAfterMeasurement());
            log.info("autoStartBeforeMeasurement: {}", softwareMgmt.isAutoStartBeforeMeasurement());
            log.info("closeBeforeStart: {}", softwareMgmt.isCloseBeforeStart());
            log.info("restartStrategy.enabled: {}", restartStrategy.isEnabled());
            log.info("restartStrategy.waitBeforeRestart: {}", restartStrategy.getWaitBeforeRestart());
            log.info("restartStrategy.waitAfterRestart: {}", restartStrategy.getWaitAfterRestart());
            log.info("restartStrategy.maxRestartAttempts: {}", restartStrategy.getMaxRestartAttempts());

            // 检查是否启用重启策略
            if (restartStrategy.isEnabled()) {
                log.info("启用软件重启策略，确保软件重新初始化");
                return startWithRestartStrategy(softwarePath);
            } else {
                log.info("使用传统启动方式");
                return startSoftwareTraditional(softwarePath);
            }

        } catch (Exception e) {
            log.error("启动设备软件失败", e);
            return false;
        }
    }

    /**
     * 使用重启策略启动软件
     */
    private boolean startWithRestartStrategy(String softwarePath) {
        try {
            var restartConfig = audiometryProperties.getSoftwareManagement().getRestartStrategy();
            int maxAttempts = restartConfig.getMaxRestartAttempts();

            for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                log.info("软件重启尝试 {}/{}", attempt, maxAttempts);

                // 1. 先关闭已运行的软件
                if (audiometryProperties.getSoftwareManagement().isCloseBeforeStart()) {
                    log.info("关闭已运行的软件实例");
                    windowsProcessUtil.closeAudiometrySoftware(softwarePath);

                    // 等待关闭完成
                    Thread.sleep(restartConfig.getWaitBeforeRestart() * 1000L);
                }

                // 2. 启动软件
                boolean started = startSoftwareTraditional(softwarePath);

                if (started) {
                    // 等待软件完全启动
                    Thread.sleep(restartConfig.getWaitAfterRestart() * 1000L);

                    // 验证软件是否真的启动成功
                    String processName = audiometryProperties.getSoftwareExecutable();
                    if (processName.endsWith(".exe")) {
                        processName = processName.substring(0, processName.length() - 4);
                    }

                    if (windowsProcessUtil.isProcessRunning(processName)) {
                        log.info("软件重启成功，第{}次尝试", attempt);
                        softwareStarted = true;
                        return true;
                    } else {
                        log.warn("软件启动后未检测到进程，第{}次尝试失败", attempt);
                    }
                } else {
                    log.warn("软件启动失败，第{}次尝试", attempt);
                }

                // 如果不是最后一次尝试，等待一段时间再重试
                if (attempt < maxAttempts) {
                    log.info("等待{}秒后重试", restartConfig.getWaitBeforeRestart());
                    Thread.sleep(restartConfig.getWaitBeforeRestart() * 1000L);
                }
            }

            log.error("软件重启失败，已尝试{}次", maxAttempts);
            return false;

        } catch (InterruptedException e) {
            log.error("软件重启被中断", e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("软件重启异常", e);
            return false;
        }
    }

    /**
     * 传统方式启动软件
     */
    private boolean startSoftwareTraditional(String softwarePath) {
        try {
            // 如果配置了启动前关闭，先关闭已运行的软件
            if (audiometryProperties.getSoftwareManagement().isCloseBeforeStart()) {
                log.info("启动前先关闭已运行的软件");
                windowsProcessUtil.closeAudiometrySoftware(softwarePath);
                Thread.sleep(2000); // 等待关闭完成
            }

            // 重置软件状态标志
            softwareStarted = false;

            // 方式1：尝试通过HTTP接口查找并启动设备
            try {
                Map<String, Object> deviceInfo = httpClient.findDiagnosticSuite();
                if (deviceInfo != null) {
                    String applicationPath = (String) deviceInfo.get("Filename");
                    if (applicationPath != null && !applicationPath.trim().isEmpty()) {
                        log.info("通过HTTP接口启动设备软件: {}", applicationPath);
                        boolean httpStarted = httpClient.startApplication(applicationPath);
                        if (httpStarted) {
                            softwareStarted = true;
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("通过HTTP接口启动设备软件失败，尝试本地启动", e);
            }

            // 方式2：使用模拟手动启动方式（最接近手动双击效果）
            log.info("使用模拟手动启动方式: {}", softwarePath);
            boolean manualStarted = windowsProcessUtil.openProgram(softwarePath, null, false);
            if (manualStarted) {
                log.info("模拟手动启动成功");
                softwareStarted = true;

                // 等待软件启动并验证
                Thread.sleep(3000);
                String processName = audiometryProperties.getSoftwareExecutable();
                if (processName.endsWith(".exe")) {
                    processName = processName.substring(0, processName.length() - 4);
                }

                if (windowsProcessUtil.isProcessRunning(processName)) {
                    log.info("确认软件进程已启动: {}", processName);
                    return true;
                } else {
                    log.warn("软件启动后未检测到进程，可能启动失败");
                }
            }

            // 方式3：使用专用的界面启动方法（多种方式尝试）
            log.info("使用专用界面启动方法: {}", softwarePath);
            boolean uiStarted = windowsProcessUtil.startAudiometrySoftwareWithUI(softwarePath);
            if (uiStarted) {
                log.info("专用界面启动方法成功");
                softwareStarted = true;
                return true;
            }

            // 方式4：使用配置的本地路径启动软件（传统方式）
            log.info("使用传统本地路径启动电测听软件: {}", softwarePath);

            // 检查是否需要管理员权限
            boolean needsAdmin = false;
            if (audiometryProperties.getSoftwareManagement().isAutoDetectAdminRequired()) {
                needsAdmin = windowsProcessUtil.requiresAdminPrivileges(softwarePath);
                log.info("自动检测权限需求: {}", needsAdmin ? "需要管理员权限" : "普通权限");
            } else if (audiometryProperties.getSoftwareManagement().isRunAsAdmin()) {
                needsAdmin = true;
                log.info("配置要求使用管理员权限启动");
            }

            boolean started;
            if (needsAdmin) {
                // 以管理员权限启动
                started = windowsProcessUtil.openProgramAsAdmin(softwarePath, null, false);
                if (started) {
                    log.info("电测听软件以管理员权限启动成功: {}", softwarePath);
                } else {
                    log.error("电测听软件以管理员权限启动失败: {}", softwarePath);
                }
            } else {
                // 普通权限启动
                started = windowsProcessUtil.openProgram(softwarePath, null, false);
                if (started) {
                    log.info("电测听软件以普通权限启动成功: {}", softwarePath);
                } else {
                    log.error("电测听软件以普通权限启动失败: {}", softwarePath);
                }
            }

            if (started) {
                softwareStarted = true;
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            log.error("传统方式启动软件失败", e);
            return false;
        }
    }

    /**
     * 诊断软件启动问题的方法
     */
    public void diagnoseSoftwareStartupIssues() {
        try {
            String softwarePath = audiometryProperties.getFullSoftwarePath();
            log.info("=== 电测听软件启动问题诊断 ===");
            log.info("软件路径: {}", softwarePath);

            // 1. 检查文件是否存在
            File softwareFile = new File(softwarePath);
            log.info("文件存在: {}", softwareFile.exists());
            log.info("文件可执行: {}", softwareFile.canExecute());

            // 2. 检查系统环境
            log.info("操作系统: {}", System.getProperty("os.name"));
            log.info("系统版本: {}", System.getProperty("os.version"));
            log.info("用户名: {}", System.getProperty("user.name"));
            log.info("用户目录: {}", System.getProperty("user.home"));

            // 3. 检查权限
            boolean isAdmin = windowsProcessUtil.isRunningAsAdmin();
            log.info("当前进程是否管理员权限: {}", isAdmin);

            boolean needsAdmin = windowsProcessUtil.requiresAdminPrivileges(softwarePath);
            log.info("软件是否需要管理员权限: {}", needsAdmin);

            // 4. 检查进程状态
            String processName = audiometryProperties.getSoftwareExecutable();
            if (processName.endsWith(".exe")) {
                processName = processName.substring(0, processName.length() - 4);
            }
            boolean isRunning = windowsProcessUtil.isProcessRunning(processName);
            log.info("软件进程是否运行: {}", isRunning);

            // 5. 列出相关进程
            log.info("=== 相关进程列表 ===");
            var processes = windowsProcessUtil.listRunningProcesses(processName);
            for (String process : processes) {
                log.info("进程: {}", process);
            }

            // 6. 检查显示环境
            log.info("=== 显示环境检查 ===");
            checkDisplayEnvironment();

            log.info("=== 诊断完成 ===");

        } catch (Exception e) {
            log.error("诊断过程中发生异常", e);
        }
    }

    /**
     * 检查显示环境
     */
    private void checkDisplayEnvironment() {
        try {
            // 检查桌面会话
            String command = "query session";
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "GBK"))) {
                String line;
                log.info("当前会话信息:");
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0) {
                        log.info("会话: {}", line.trim());
                    }
                }
            }

            // 检查显示器信息
            String displayCommand = "wmic desktopmonitor get screenheight,screenwidth /format:csv";
            ProcessBuilder displayPb = new ProcessBuilder("cmd", "/c", displayCommand);
            displayPb.redirectErrorStream(true);
            Process displayProcess = displayPb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(displayProcess.getInputStream(), "GBK"))) {
                String line;
                log.info("显示器信息:");
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0 && !line.toLowerCase().contains("node")) {
                        log.info("显示器: {}", line.trim());
                    }
                }
            }

        } catch (Exception e) {
            log.warn("检查显示环境失败", e);
        }
    }

    /**
     * 关闭设备软件
     */
    private boolean stopDeviceSoftware() {
        try {
            String softwarePath = audiometryProperties.getFullSoftwarePath();
            log.info("开始关闭电测听软件: {}", softwarePath);

            // 打印关闭配置状态（调试用）
            var softwareMgmt = audiometryProperties.getSoftwareManagement();
            log.info("=== 软件关闭配置状态 ===");
            log.info("autoCloseAfterMeasurement: {}", softwareMgmt.isAutoCloseAfterMeasurement());
            log.info("closeWaitTimeout: {}", softwareMgmt.getCloseWaitTimeout());

            // 使用专门针对听力设备软件的关闭方法
            boolean closed = windowsProcessUtil.closeAudiometrySoftware(softwarePath);

            if (closed) {
                softwareStarted = false;
                log.info("电测听软件关闭成功: {}", softwarePath);
                return true;
            } else {
                log.error("电测听软件关闭失败: {}", softwarePath);

                // 如果专用方法失败，尝试调试模式
                log.info("启动调试模式分析关闭失败原因");
                windowsProcessUtil.debugCloseProgram(softwarePath);

                // 最后尝试通过进程名强制关闭
                String processName = audiometryProperties.getSoftwareExecutable();
                if (processName.endsWith(".exe")) {
                    processName = processName.substring(0, processName.length() - 4);
                }

                log.info("最后尝试：通过进程名强制关闭: {}", processName);
                boolean closedByName = windowsProcessUtil.closeProgramByName(processName);

                if (closedByName) {
                    softwareStarted = false;
                    log.info("通过进程名强制关闭成功: {}", processName);
                    return true;
                } else {
                    log.error("所有关闭方法都失败了，建议手动关闭软件");
                    // 即使关闭失败，也重置状态标志，避免重复尝试
                    softwareStarted = false;
                    return false;
                }
            }

        } catch (Exception e) {
            log.error("关闭电测听软件异常", e);
            // 发生异常时也重置状态标志
            softwareStarted = false;
            return false;
        }
    }

    /**
     * 开始测量
     */
    public boolean startMeasurement(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            log.info("开始电测听测量: 设备ID={}", deviceId);

            // 发送测量开始事件
            // 发送统一设备状态事件
            DeviceStatusSender.sendMeasurementStarted(context.getClient(), context);

            // 更新患者状态
            String patientKey = deviceId + "_" + (context.getPatientInfo() != null ?
                    context.getPatientInfo().getExamNo() : "default");
            patientStatusMap.put(patientKey, "MEASURING");

            return true;
        } catch (Exception e) {
            log.error("开始电测听测量失败", e);
            return false;
        }
    }

    /**
     * 查询最新结果
     */
    public boolean queryLatestResults(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            log.info("开始查询电测听最新结果: 设备ID={}", deviceId);

            // 获取最新会话
            AudiometrySession latestSession = httpClient.getLatestSession();
            if (latestSession == null) {
                log.info("暂无测试会话");

                // 发送查询完成状态（无结果）
                DeviceStatusSender.sendQueryCompleted(context.getClient(), context, false,
                        "查询完成，暂无测试会话");
                return true;
            }

            // 获取会话的测试结果
            List<AudiometryTestResult> testResults = httpClient.getSessionTests(latestSession.getIndex());
            if (testResults.isEmpty()) {
                log.info("会话{}暂无测试结果", latestSession.getIndex());

                // 发送查询完成状态（无结果）
                DeviceStatusSender.sendQueryCompleted(context.getClient(), context, false,
                        "查询完成，会话暂无测试结果");
                return true;
            }

            // 处理测试结果
            boolean processResult = processTestResults(context, testResults);

            if (processResult) {
                // 发送查询完成状态（有结果）
                DeviceStatusSender.sendQueryCompleted(context.getClient(), context, true,
                        "查询成功，找到测试结果");
            } else {
                // 发送查询完成状态（处理失败）
                DeviceStatusSender.sendQueryCompleted(context.getClient(), context, false,
                        "查询完成，但结果处理失败");
            }

            return processResult;

        } catch (Exception e) {
            log.error("查询电测听结果失败", e);

            // 发送查询失败状态
            DeviceStatusSender.sendQueryStatus(context.getClient(), context, false,
                    "查询失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 处理测试结果
     */
    private boolean processTestResults(DeviceContext context, List<AudiometryTestResult> testResults) {
        try {
            String deviceId = generateDeviceId(context);

            for (AudiometryTestResult testResult : testResults) {
                if (testResult.isToneTest() && testResult.getData() != null) {
                    // 解析Base64编码的XML数据
                    Map<String, List<TonePoint>> parsedData =
                            dataParser.parseTestData(testResult.getData());

                    if (!parsedData.isEmpty()) {

                        log.info("成功解析电测听结果: {}", new ObjectMapper().writeValueAsString(parsedData));
                        // 发送解析后的数据
                        sendParsedResults(context, parsedData);

                        // 测量完成后关闭软件（如果配置启用）
                        if (audiometryProperties.getSoftwareManagement().isAutoCloseAfterMeasurement()) {
                            stopDeviceSoftware();
                        }

                        log.info("电测听结果处理完成: 设备ID={}, 数据组数={}", deviceId, parsedData.size());
                        return true;
                    }
                }
            }

            log.warn("未找到有效的纯音测试结果");
            return false;

        } catch (Exception e) {
            log.error("处理测试结果失败", e);
            return false;
        }
    }

    /**
     * 发送解析后的结果
     */
    private void sendParsedResults(DeviceContext context, Map<String, List<TonePoint>> parsedData) {
        try {
            // 计算测量耗时
            Long duration = 0L;
            if (context.getMeasurementStartTime() != null) {
                duration = System.currentTimeMillis() - context.getMeasurementStartTime();
            }

            // 创建统一的设备响应数据
            CommonDeviceResponse deviceResponse = CommonDeviceResponse.builder()
                    .data(parsedData)
                    .sessionId(context.getSessionId())
                    .deviceId(generateDeviceId(context))
                    .deviceModel(DEVICE_MODEL)
                    .timestamp(System.currentTimeMillis())
                    .duration(duration)
                    .examItemInfo(context.getExamItemInfo())
                    .responseType(org.bj.constants.ResponseTypeConstants.MEASUREMENT_RESULT)
                    .dataStatus("PROCESSED")
                    .hasError(false)
                    .isComplete(true)
                    .build();

            // 添加患者信息
            if (context.getPatientInfo() != null) {
                deviceResponse.withPatientInfo(context.getPatientInfo());
            }

            // 添加时间信息
            if (context.getMeasurementStartTime() != null) {
                deviceResponse.withTimeInfo(context.getMeasurementStartTime(), System.currentTimeMillis());
            }

            // 添加扩展属性
            String formattedResults = dataParser.formatResults(parsedData);
            deviceResponse.addExtendedProperty("formattedResults", formattedResults);
            deviceResponse.addExtendedProperty("resultType", "AUDIOMETRY_TEST");

            // 检查数据存储配置
            String storageType = audiometryProperties.getDataStorage().getStorageType();

            if ("db".equals(storageType)) {
                // 数据库存储模式：保存到数据库并发送通知
                try {
                    // 转换数据格式为数据库存储格式
                    ElectricalAudiometryResult hearingTestData = convertToHearingTestData(parsedData);
                    //修正后数据
                    ElectricalAudiometryResult correctedValue = convertToHearingCorrectedData(parsedData, context.getPatientInfo());

                    // 保存原始TonePoint数据供PDF生成使用
                    boolean rawDataSaved = audiometryService.saveRawAudiometryData(
                            context.getPatientInfo(),
                            context.getExamItemInfo(),
                            hearingTestData,
                            DEVICE_MODEL,
                            context.getSessionId()
                    );

                    // 保存聚合数据到数据库
                    boolean saved = audiometryService.saveAudiometryResult(
                            context.getPatientInfo(),
                            context.getExamItemInfo(),
                            correctedValue,
                            DEVICE_MODEL,
                            context.getSessionId()
                    );

                    if (saved && rawDataSaved) {
                        // 发送保存成功通知
                        deviceResponse.addExtendedProperty("dataStorageType", "db");
                        deviceResponse.addExtendedProperty("dataSaved", true);
                        deviceResponse.addExtendedProperty("rawDataSaved", true);
                        log.info("电测听数据已保存到数据库: 设备ID={}, 包含原始数据", generateDeviceId(context));
                    } else {
                        log.error("电测听数据保存到数据库失败: 设备ID={}, 聚合数据={}, 原始数据={}",
                                generateDeviceId(context), saved, rawDataSaved);
                        deviceResponse.addExtendedProperty("dataSaved", saved);
                        deviceResponse.addExtendedProperty("rawDataSaved", rawDataSaved);
                    }
                } catch (Exception e) {
                    log.error("保存电测听数据到数据库异常: 设备ID={}", generateDeviceId(context), e);
                    deviceResponse.addExtendedProperty("dataSaved", false);
                    deviceResponse.addExtendedProperty("rawDataSaved", false);
                }
            } else {
                // 直接传输模式：只发送数据
                deviceResponse.addExtendedProperty("dataStorageType", "transfer");
            }

            // 发送统一的testResult事件
            context.getClient().sendEvent(EventConstants.TEST_RESULT, deviceResponse.toMap());

            log.info("电测听结果已发送给客户端: 设备ID={}", generateDeviceId(context));

        } catch (Exception e) {
            log.error("发送电测听结果失败", e);
        }
    }

    private ElectricalAudiometryResult convertToHearingCorrectedData(Map<String, List<TonePoint>> parsedData, PatientInfo patientInfo) {

        ElectricalAudiometryResult electricalAudiometryResult = new ElectricalAudiometryResult();
        try {
            log.info("将解析后的数据转换为听力测试数据格式{}", new ObjectMapper().writeValueAsString(parsedData));

            //语频范围
            int[] speechFrequencyRange = {500, 1000, 2000};
            int[] highFrequencyRange = {3000, 4000, 6000};
            // 处理左耳数据（气导）
            List<TonePoint> leftEarAirConductionData = parsedData.get("Left_AC");
            if (leftEarAirConductionData != null && !leftEarAirConductionData.isEmpty()) {
                //左耳气导各个频率
                FrequencyData frequencyData = convertToFrequencyCorrectedData(leftEarAirConductionData, patientInfo);
                electricalAudiometryResult.setLeftEarAirData(frequencyData);
                //左耳气导语频平均
                double leftWhisperFrequencyAverage = calculatePTA(leftEarAirConductionData, speechFrequencyRange);
                electricalAudiometryResult.setLeftWhisperFrequencyAirAvg(leftWhisperFrequencyAverage);
                //左耳气导高频平均
                double leftHighFrequencyAirAvg = calculatePTA(leftEarAirConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyAirAvg(leftHighFrequencyAirAvg);

            }

            // 处理左耳数据（骨导）
            List<TonePoint> leftEarBoneConductionData = parsedData.get("Left_BC");
            if (leftEarBoneConductionData != null && !leftEarBoneConductionData.isEmpty()) {
                FrequencyData frequencyData = convertToFrequencyCorrectedData(leftEarBoneConductionData, patientInfo);
                electricalAudiometryResult.setLeftEarBoneData(frequencyData);
                // 计算左耳平均听阈（取500Hz, 1000Hz, 2000Hz的平均值）
                double leftWhisperFrequencyBoneAvg = calculatePTA(leftEarBoneConductionData, speechFrequencyRange);
                electricalAudiometryResult.setLeftWhisperFrequencyBoneAvg(leftWhisperFrequencyBoneAvg);

                double leftHighFrequencyBoneAvg = calculatePTA(leftEarBoneConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyBoneAvg(leftHighFrequencyBoneAvg);

            }

            // 处理右耳数据（气导）
            List<TonePoint> rightEarAirConductionData = parsedData.get("Right_AC");
            if (rightEarAirConductionData != null && !rightEarAirConductionData.isEmpty()) {
                FrequencyData frequencyData = convertToFrequencyCorrectedData(rightEarAirConductionData, patientInfo);
                electricalAudiometryResult.setRightEarAirData(frequencyData);

                // 计算右耳平均听阈
                double rightWhisperFrequencyAirAvg = calculatePTA(rightEarAirConductionData, speechFrequencyRange);
                electricalAudiometryResult.setRightWhisperFrequencyAirAvg(rightWhisperFrequencyAirAvg);

                double rightHighFrequencyAirAvg = calculatePTA(rightEarAirConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyAirAvg(rightHighFrequencyAirAvg);

            }

            // 处理右耳数据（骨导）
            List<TonePoint> rightEarBoneConductionData = parsedData.get("Left_BC");
            if (rightEarBoneConductionData != null && !rightEarBoneConductionData.isEmpty()) {
                FrequencyData frequencyData = convertToFrequencyCorrectedData(rightEarBoneConductionData, patientInfo);
                electricalAudiometryResult.setRightEarBoneData(frequencyData);

                // 计算左耳平均听阈（取500Hz, 1000Hz, 2000Hz的平均值）
                double rightWhisperFrequencyBoneAvg = calculatePTA(rightEarBoneConductionData, speechFrequencyRange);
                electricalAudiometryResult.setRightWhisperFrequencyBoneAvg(rightWhisperFrequencyBoneAvg);

                double rightHighFrequencyBoneAvg = calculatePTA(rightEarBoneConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyBoneAvg(rightHighFrequencyBoneAvg);

            }

            if (CollectionUtil.isNotEmpty(leftEarAirConductionData) && CollectionUtil.isNotEmpty(rightEarAirConductionData)) {
                List<TonePoint> binauralAirConduction = Stream.concat(leftEarAirConductionData.stream(), rightEarAirConductionData.stream()).toList();
                double speech = calculatePTA(binauralAirConduction, speechFrequencyRange);
                double high = calculatePTA(binauralAirConduction, highFrequencyRange);

                electricalAudiometryResult.setBothWhisperFrequencyAirAvg(speech);
                electricalAudiometryResult.setBothHighFrequencyAirAvg(high);
            }
            if (CollectionUtil.isNotEmpty(leftEarBoneConductionData) && CollectionUtil.isNotEmpty(rightEarBoneConductionData)) {
                List<TonePoint> binauralBoneConduction = Stream.concat(leftEarBoneConductionData.stream(), rightEarBoneConductionData.stream()).toList();
                double speech = calculatePTA(binauralBoneConduction, speechFrequencyRange);
                double high = calculatePTA(binauralBoneConduction, highFrequencyRange);
                electricalAudiometryResult.setBothWhisperFrequencyBoneAvg(speech);
                electricalAudiometryResult.setBothHighFrequencyBoneAvg(high);
            }
            log.debug("电测听数据转换完成: {}", electricalAudiometryResult);

        } catch (Exception e) {
            log.error("转换电测听数据失败", e);
        }

        return electricalAudiometryResult;

    }

    @NotNull
    private FrequencyData convertToFrequencyCorrectedData(List<TonePoint> leftEarAirConductionData, PatientInfo patientInfo) {
        log.info("修正听力基础数据赋值方法:{}","convertToFrequencyCorrectedData");
        FrequencyData frequencyData = new FrequencyData();
        for (TonePoint tonePoint : leftEarAirConductionData) {
            Integer threshold = tonePoint.getEffectiveThreshold();
            int frequency = tonePoint.getFrequency();
            // 跳过空值
            if (threshold == null) {
                continue;
            }

            // 应用频率修正
            Integer correctedThreshold = applyFrequencyCorrection(threshold, frequency, patientInfo);

            switch (frequency) {
                case 500:
                    frequencyData.setHearingThreshold500Hz(correctedThreshold);
                    break;
                case 1000:
                    frequencyData.setHearingThreshold1000Hz(correctedThreshold);
                    break;
                case 2000:
                    frequencyData.setHearingThreshold2000Hz(correctedThreshold);
                    break;
                case 3000:
                    frequencyData.setHearingThreshold3000Hz(correctedThreshold);
                    break;
                case 4000:
                    frequencyData.setHearingThreshold4000Hz(correctedThreshold);
                    break;
                case 6000:
                    frequencyData.setHearingThreshold6000Hz(correctedThreshold);
                    break;
            }
        }
        log.info("修正听力基础数据赋值方法结束:{}","convertToFrequencyCorrectedData");
        return frequencyData;
    }

    /**
     * 应用频率修正规则
     *
     * @param originalThreshold 原始阈值
     * @param frequency         频率
     * @return 修正后的阈值
     */
    private Integer applyFrequencyCorrection(Integer originalThreshold, int frequency, PatientInfo patientInfo) {
        log.info("<应用频率修正规则修正数据开始>:{}","applyFrequencyCorrection");
        Integer correction = 0;

        Integer age = patientInfo.getAge();
        String gender = patientInfo.getGender();

        AgeRange ageRange = AgeRange.fromAge(age);
        if (ObjectUtils.isNotEmpty(ageRange)) {
            int min = ageRange.getMin();
            int max = ageRange.getMax();
            String ageRangeStr = min + "-" + max;
            log.info("<该人员处于年段>:{}",ageRangeStr);
            correction = HearingCorrectionSystem.getCorrectionValue(ageRangeStr, frequency, gender);
        }
        log.info("<应用频率修正规则修正数据结束>:{},修正结果是:{},修正幅度:{}","applyFrequencyCorrection",originalThreshold + correction,correction);
        return originalThreshold + correction;
    }


    /**
     * 将解析后的数据转换为听力测试数据格式
     */
    private ElectricalAudiometryResult convertToHearingTestData(Map<String, List<TonePoint>> parsedData) {
        ElectricalAudiometryResult electricalAudiometryResult = new ElectricalAudiometryResult();
        try {
            log.info("将解析后的数据转换为听力测试数据格式{}", new ObjectMapper().writeValueAsString(parsedData));

            //语频范围
            int[] speechFrequencyRange = {500, 1000, 2000};
            int[] highFrequencyRange = {3000, 4000, 6000};
            // 处理左耳数据（气导）
            List<TonePoint> leftEarAirConductionData = parsedData.get("Left_AC");
            if (leftEarAirConductionData != null && !leftEarAirConductionData.isEmpty()) {
                //左耳气导各个频率
                FrequencyData frequencyData = convertToFrequencyData(leftEarAirConductionData);
                electricalAudiometryResult.setLeftEarAirData(frequencyData);
                //左耳气导语频平均
                double leftWhisperFrequencyAverage = calculatePTA(leftEarAirConductionData, speechFrequencyRange);
                electricalAudiometryResult.setLeftWhisperFrequencyAirAvg(leftWhisperFrequencyAverage);
                //左耳气导高频平均
                double leftHighFrequencyAirAvg = calculatePTA(leftEarAirConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyAirAvg(leftHighFrequencyAirAvg);

            }

            // 处理左耳数据（骨导）
            List<TonePoint> leftEarBoneConductionData = parsedData.get("Left_BC");
            if (leftEarBoneConductionData != null && !leftEarBoneConductionData.isEmpty()) {
                FrequencyData frequencyData = convertToFrequencyData(leftEarBoneConductionData);
                electricalAudiometryResult.setLeftEarBoneData(frequencyData);
                // 计算左耳平均听阈（取500Hz, 1000Hz, 2000Hz的平均值）
                double leftWhisperFrequencyBoneAvg = calculatePTA(leftEarBoneConductionData, speechFrequencyRange);
                electricalAudiometryResult.setLeftWhisperFrequencyBoneAvg(leftWhisperFrequencyBoneAvg);

                double leftHighFrequencyBoneAvg = calculatePTA(leftEarBoneConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyBoneAvg(leftHighFrequencyBoneAvg);

            }

            // 处理右耳数据（气导）
            List<TonePoint> rightEarAirConductionData = parsedData.get("Right_AC");
            if (rightEarAirConductionData != null && !rightEarAirConductionData.isEmpty()) {
                FrequencyData frequencyData = convertToFrequencyData(rightEarAirConductionData);
                electricalAudiometryResult.setRightEarAirData(frequencyData);

                // 计算右耳平均听阈
                double rightWhisperFrequencyAirAvg = calculatePTA(rightEarAirConductionData, speechFrequencyRange);
                electricalAudiometryResult.setRightWhisperFrequencyAirAvg(rightWhisperFrequencyAirAvg);

                double rightHighFrequencyAirAvg = calculatePTA(rightEarAirConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyAirAvg(rightHighFrequencyAirAvg);

            }

            // 处理右耳数据（骨导）
            List<TonePoint> rightEarBoneConductionData = parsedData.get("Left_BC");
            if (rightEarBoneConductionData != null && !rightEarBoneConductionData.isEmpty()) {
                FrequencyData frequencyData = convertToFrequencyData(rightEarBoneConductionData);
                electricalAudiometryResult.setRightEarBoneData(frequencyData);

                // 计算左耳平均听阈（取500Hz, 1000Hz, 2000Hz的平均值）
                double rightWhisperFrequencyBoneAvg = calculatePTA(rightEarBoneConductionData, speechFrequencyRange);
                electricalAudiometryResult.setRightWhisperFrequencyBoneAvg(rightWhisperFrequencyBoneAvg);

                double rightHighFrequencyBoneAvg = calculatePTA(rightEarBoneConductionData, highFrequencyRange);
                electricalAudiometryResult.setLeftHighFrequencyBoneAvg(rightHighFrequencyBoneAvg);

            }

            if (CollectionUtil.isNotEmpty(leftEarAirConductionData) && CollectionUtil.isNotEmpty(rightEarAirConductionData)) {
                List<TonePoint> binauralAirConduction = Stream.concat(leftEarAirConductionData.stream(), rightEarAirConductionData.stream()).toList();
                double speech = calculatePTA(binauralAirConduction, speechFrequencyRange);
                double high = calculatePTA(binauralAirConduction, highFrequencyRange);

                electricalAudiometryResult.setBothWhisperFrequencyAirAvg(speech);
                electricalAudiometryResult.setBothHighFrequencyAirAvg(high);
            }
            if (CollectionUtil.isNotEmpty(leftEarBoneConductionData) && CollectionUtil.isNotEmpty(rightEarBoneConductionData)) {
                List<TonePoint> binauralBoneConduction = Stream.concat(leftEarBoneConductionData.stream(), rightEarBoneConductionData.stream()).toList();
                double speech = calculatePTA(binauralBoneConduction, speechFrequencyRange);
                double high = calculatePTA(binauralBoneConduction, highFrequencyRange);
                electricalAudiometryResult.setBothWhisperFrequencyBoneAvg(speech);
                electricalAudiometryResult.setBothHighFrequencyBoneAvg(high);
            }
            log.debug("电测听数据转换完成: {}", electricalAudiometryResult);

        } catch (Exception e) {
            log.error("转换电测听数据失败", e);
        }

        return electricalAudiometryResult;
    }

    @NotNull
    private static FrequencyData convertToFrequencyData(List<TonePoint> leftEarAirConductionData) {
        FrequencyData frequencyData = new FrequencyData();
        for (TonePoint tonePoint : leftEarAirConductionData) {
            switch (tonePoint.getFrequency()) {
                case 500:
                    frequencyData.setHearingThreshold500Hz(tonePoint.getEffectiveThreshold());
                    break;
                case 1000:
                    frequencyData.setHearingThreshold1000Hz(tonePoint.getEffectiveThreshold());
                    break;
                case 2000:
                    frequencyData.setHearingThreshold2000Hz(tonePoint.getEffectiveThreshold());
                    break;
                case 3000:
                    frequencyData.setHearingThreshold3000Hz(tonePoint.getEffectiveThreshold());
                    break;
                case 4000:
                    frequencyData.setHearingThreshold4000Hz(tonePoint.getEffectiveThreshold());
                    break;
                case 6000:
                    frequencyData.setHearingThreshold6000Hz(tonePoint.getEffectiveThreshold());
                    break;
            }
        }
        return frequencyData;
    }

    /**
     * 计算纯音平均听阈（PTA）
     * 使用500Hz, 1000Hz, 2000Hz的平均值
     */
    private double calculatePTA(List<TonePoint> tonePoints, int[] targetFrequencies) {
        if (tonePoints == null || tonePoints.isEmpty()) {
            return 0.0;
        }

        // 查找特定频率的听阈值
        double sum = 0.0;
        int count = 0;

        for (int frequency : targetFrequencies) {
            for (TonePoint point : tonePoints) {
                if (point.getFrequency() != null && point.getFrequency() == frequency) {
                    Integer threshold = point.getEffectiveThreshold();
                    if (threshold != null) {
                        sum += threshold;
                        count++;
                        break;
                    }
                }
            }
        }

        return count > 0 ? Math.round(sum / count * 100) / 100.0 : 0.0;
    }


    // =================== 全局监控接口实现 ===================

    @Override
    public boolean executeMonitoring(DeviceContext context, Object customData) {
        try {
            return queryLatestResults(context);
        } catch (Exception e) {
            String deviceId = generateDeviceId(context);
            log.error("电测听监控任务执行失败: 设备ID={}", deviceId, e);
            return false;
        }
    }

    @Override
    public boolean isMonitoringCompleted(DeviceContext context, Object customData) {
        // 电测听设备需要持续监控，直到手动停止
        return false;
    }

    @Override
    public MonitoringConfig getMonitoringConfig() {
        // 电测听设备：30秒间隔，300秒超时，最多5次错误
        return new MonitoringConfig(30, 300, 5);
    }
}
