package org.bj.device.processors;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bj.constants.ResponseType;
import org.bj.device.processors.service.AccessDatabaseConnector;
import org.bj.device.DeviceProcessor;
import org.bj.device.util.DeviceResponseBuilder;
import org.bj.config.FGY200Properties;
import org.bj.device.processors.service.FGY200Service;
import org.bj.device.entity.DeviceContext;
import org.bj.device.entity.ExamItemInfo;
import org.bj.device.entity.PatientInfo;
import org.bj.device.entity.CommonDeviceResponse;
import org.bj.device.util.DeviceStatusSender;
import org.bj.util.FileUploadUtil;
import org.bj.utils.NamedParameterSqlProcessor;
import org.bj.constants.EventConstants;
import org.bj.service.GlobalDeviceMonitoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.Period;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * FGY-200肺功能检测仪设备处理器
 * 健桥医电FGY-200肺功能检测仪专用处理器
 * <p>
 * 设备特点：
 * 1. 通过文件系统进行患者信息输入（Tester.ini）
 * 2. 通过Access数据库获取检测结果（fei.mdb）
 * 3. 支持单条和批量患者信息录入
 * 4. 自动生成PDF/JPG格式报告
 *
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
@Profile("fgy200")
public class FGY200DeviceProcessor implements DeviceProcessor {

    /**
     * 设备型号
     */
    private static final String DEVICE_MODEL = "FGY-200肺功能";

    /**
     * 设备连接状态映射
     */
    private final ConcurrentMap<String, String> deviceStatusMap = new ConcurrentHashMap<>();

    /**
     * 患者检测状态映射
     */
    private final ConcurrentMap<String, PatientTestStatus> patientStatusMap = new ConcurrentHashMap<>();

    /**
     * 全局设备监控服务
     */
    @Autowired
    private GlobalDeviceMonitoringService globalMonitoringService;



    /**
     * FGY-200配置属性
     */
    @Autowired
    private FGY200Properties fgy200Properties;

    @Autowired(required = false)
    private FGY200Service fgy200Service;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    /**
     * 构造函数 - 支持依赖注入
     */
    public FGY200DeviceProcessor() {
        // 默认构造函数
    }

    /**
     * 初始化后检查配置状态
     */
    @PostConstruct
    public void checkConfiguration() {
        log.info("=== FGY200DeviceProcessor 配置检查 ===");
        log.info("fgy200Properties: {}", fgy200Properties != null ? "已注入" : "未注入");

        if (fgy200Properties != null) {
            log.info("数据库配置启用状态: {}", fgy200Properties.getDatabase().isEnabled());
            log.info("数据库文件路径: {}", fgy200Properties.getDatabase().getPath());

            // 检查数据库文件是否存在
            String dbPath = fgy200Properties.getDatabase().getPath();
            boolean fileExists = new File(dbPath).exists();
            log.info("数据库文件是否存在: {} (路径: {})", fileExists, dbPath);
        }

        log.info("数据库连接方式: 直接连接");

        // 注册到全局监控服务
        if (globalMonitoringService != null && fgy200Properties != null) {
            GlobalDeviceMonitoringService.GlobalMonitoringConfig config =
                    new GlobalDeviceMonitoringService.GlobalMonitoringConfig(
                            fgy200Properties.getMonitoring().getInterval(),
                            fgy200Properties.getMonitoring().getTimeout(),
                            5 // 最大错误次数
                    );
            globalMonitoringService.registerDeviceProcessor(DEVICE_MODEL, this, config);
            log.info("FGY-200设备处理器已注册到全局监控服务");
        }

        log.info("=== 配置检查完成 ===");
    }

    /**
     * 获取数据库连接状态信息（用于诊断）
     */
    public String getDatabaseConnectionStatus() {
        StringBuilder status = new StringBuilder();
        status.append("FGY200 数据库连接状态:\n");

        // 检查配置
        if (fgy200Properties == null) {
            status.append("- 配置属性: 未注入\n");
            return status.toString();
        }

        status.append("- 配置属性: 已注入\n");
        status.append("- 数据库启用: ").append(fgy200Properties.getDatabase().isEnabled()).append("\n");
        status.append("- 数据库路径: ").append(fgy200Properties.getDatabase().getPath()).append("\n");

        // 检查文件存在性
        String dbPath = fgy200Properties.getDatabase().getPath();
        boolean fileExists = new File(dbPath).exists();
        status.append("- 文件存在: ").append(fileExists).append("\n");

        // 使用直接连接方式
        status.append("- 连接方式: 直接连接\n");

        // 测试连接
        try {
            boolean connected = validateDirectDatabaseConnection(dbPath);
            status.append("- 连接测试: ").append(connected ? "成功" : "失败").append("\n");
        } catch (Exception e) {
            status.append("- 连接测试: 失败 (").append(e.getMessage()).append(")\n");
        }

        return status.toString();
    }


    @Override
    public String getDeviceModel() {
        return DEVICE_MODEL;
    }

    @Override
    public String getDescription() {
        return "健桥医电FGY-200肺功能检测仪处理器 - 支持文件系统和Access数据库通信";
    }

    @Override
    public boolean handleConnection(DeviceContext context) {

        String deviceId = generateDeviceId(context);
        String userId = context.getSessionId();
        SocketIOClient client = context.getClient();

        log.info("FGY-200设备连接处理开始: 设备ID={}, 用户={}", deviceId, userId);

        // 1. 检查设备程序路径
        String programPath = fgy200Properties.getProgramPath();
        if (!validateProgramPath(programPath)) {
            String errorMsg = "FGY-200程序路径不存在或无效: " + programPath;
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        // 2. 检查数据库连接
        if (!validateDatabaseConnection(programPath)) {
            String errorMsg = "无法连接到FGY-200数据库";
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        // 3. 更新设备状态
        deviceStatusMap.put(deviceId, "CONNECTED");

        // 4. 发送连接成功事件
        DeviceStatusSender.sendConnected(client, context);

        log.info("FGY-200设备连接成功: 设备ID={}, 用户={}", deviceId, userId);
        return true;
    }

    @Override
    public boolean handleDisconnection(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            String userId = context.getSessionId();

            log.info("FGY-200设备断开连接: 设备ID={}, 用户={}", deviceId, userId);

            // 1. 清理设备状态
            deviceStatusMap.remove(deviceId);

            // 2. 清理患者状态
            patientStatusMap.entrySet().removeIf(entry -> entry.getKey().startsWith(deviceId + "_"));

            // 3. 发送断开连接事件
            Map<String, Object> disconnectionEvent = new HashMap<>();
            disconnectionEvent.put("deviceId", deviceId);
            disconnectionEvent.put("deviceModel", DEVICE_MODEL);
            disconnectionEvent.put("userId", userId);
            disconnectionEvent.put("status", "DISCONNECTED");
            disconnectionEvent.put("timestamp", System.currentTimeMillis());

            context.getClient().sendEvent("deviceDisconnected", disconnectionEvent);

            log.info("FGY-200设备断开连接完成: 设备ID={}", deviceId);
            return true;

        } catch (Exception e) {
            log.error("FGY-200设备断开连接失败", e);
            return false;
        }
    }

    /**
     * 开始测量（FGY-200专用方法）
     */
    public boolean startMeasurement(DeviceContext context) {

        String deviceId = generateDeviceId(context);
        PatientInfo patientInfo = context.getPatientInfo();
        ExamItemInfo examItemInfo = context.getExamItemInfo();

        if (patientInfo == null) {
            log.error("患者信息为空，无法开始测量");
            throw new RuntimeException("患者信息为空，无法开始测量");
        }

        log.info("开始FGY-200肺功能测量: 设备ID={}, 患者={}", deviceId, patientInfo.getName());

        // 1. 写入患者信息到Tester.ini文件
        String programPath = fgy200Properties.getProgramPath();
        if (!writePatientInfoToFile(programPath, patientInfo, examItemInfo)) {
            log.error("写入患者信息文件失败");
            throw new RuntimeException("写入患者信息文件失败");
        }

        // 2. 创建患者测试状态
        String patientKey = deviceId + "_" + patientInfo.getExamNo();
        PatientTestStatus testStatus = new PatientTestStatus();
        testStatus.setPatientInfo(patientInfo);
        testStatus.setDeviceId(deviceId);
        testStatus.setStartTime(System.currentTimeMillis());
        testStatus.setStatus("WAITING_FOR_TEST");
        testStatus.setProgramPath(programPath);

        patientStatusMap.put(patientKey, testStatus);

        // 3. 注册到全局监控服务
        globalMonitoringService.registerMonitoringTask(patientKey, DEVICE_MODEL, context, testStatus);

        // 4. 发送测量开始事件
        DeviceStatusSender.sendMeasurementStarted(context.getClient(), context);

        log.info("FGY-200测量启动成功: 患者={}", patientInfo.getName());
        return true;

    }

    @Override
    public boolean handleData(DeviceContext context, String rawData) {
        // FGY-200设备不通过串口传输数据，而是通过数据库查询
        // 这个方法主要用于处理手动触发的数据查询请求
        try {
            log.debug("FGY-200数据处理: {}", rawData);

            if ("QUERY_RESULTS".equals(rawData)) {
                return queryLatestResults(context);
            }

            return true;
        } catch (Exception e) {
            log.error("FGY-200数据处理失败", e);
            return false;
        }
    }

    @Override
    public boolean sendCommand(DeviceContext context, String command) {
        String deviceId = generateDeviceId(context);
        log.info("FGY-200发送指令: 设备ID={}, 指令={}", deviceId, command);

        return switch (command.toUpperCase()) {
            case "START_MEASUREMENT" -> startMeasurement(context);
            case "QUERY_RESULTS" -> queryLatestResults(context);
            case "RESET" -> resetDevice(context);
            default -> {
                log.warn("不支持的FGY-200指令: {}", command);
                yield false;
            }
        };
    }

    @Override
    public String getDeviceStatus(DeviceContext context) {
        String deviceId = generateDeviceId(context);
        return deviceStatusMap.getOrDefault(deviceId, "DISCONNECTED");
    }

    @Override
    public boolean resetDevice(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            log.info("重置FGY-200设备: 设备ID={}", deviceId);

            // 1. 清理患者状态
            patientStatusMap.entrySet().removeIf(entry -> entry.getKey().startsWith(deviceId + "_"));

            // 2. 重置设备状态
            deviceStatusMap.put(deviceId, "CONNECTED");

            // 3. 发送重置事件
            Map<String, Object> resetEvent = new HashMap<>();
            resetEvent.put("deviceId", deviceId);
            resetEvent.put("deviceModel", DEVICE_MODEL);
            resetEvent.put("status", "RESET_COMPLETED");
            resetEvent.put("timestamp", System.currentTimeMillis());

            context.getClient().sendEvent("deviceReset", resetEvent);

            log.info("FGY-200设备重置完成: 设备ID={}", deviceId);
            return true;

        } catch (Exception e) {
            log.error("FGY-200设备重置失败", e);
            return false;
        }
    }

    @Override
    public boolean calibrateDevice(DeviceContext context) {
        // FGY-200设备校准需要在设备端手动进行
        log.info("FGY-200设备校准需要在设备端手动进行");

        Map<String, Object> calibrationEvent = new HashMap<>();
        calibrationEvent.put("deviceId", generateDeviceId(context));
        calibrationEvent.put("deviceModel", DEVICE_MODEL);
        calibrationEvent.put("status", "MANUAL_CALIBRATION_REQUIRED");
        calibrationEvent.put("message", "请在FGY-200设备上手动进行校准");
        calibrationEvent.put("timestamp", System.currentTimeMillis());

        context.getClient().sendEvent("deviceCalibration", calibrationEvent);
        return true;
    }

    @Override
    public boolean supportsFeature(String feature) {
        return switch (feature) {
            case "FILE_COMMUNICATION" -> true;
            case "DATABASE_ACCESS" -> true;
            case "PATIENT_INFO_INPUT" -> true;
            case "RESULT_MONITORING" -> true;
            case "PDF_REPORT_GENERATION" -> true;
            case "BATCH_PATIENT_INPUT" -> true;
            case "QUERY_RESULTS" -> true; // FGY-200支持查询功能
            default -> false;
        };
    }

    @Override
    public void initialize() {
        log.info("初始化FGY-200肺功能检测仪处理器");
        deviceStatusMap.clear();
        patientStatusMap.clear();
    }

    @Override
    public void destroy() {
        log.info("销毁FGY-200肺功能检测仪处理器");

        // 取消所有监控任务
        for (String patientKey : patientStatusMap.keySet()) {
            globalMonitoringService.unregisterMonitoringTask(patientKey);
        }

        // 清理状态
        deviceStatusMap.clear();
        patientStatusMap.clear();

        log.info("FGY-200设备处理器销毁完成");
    }

    // =================== 私有辅助方法 ===================

    /**
     * 生成设备ID
     */
    private String generateDeviceId(DeviceContext context) {
        return DEVICE_MODEL + "_" + context.getSessionId();
    }

    /**
     * 获取程序安装路径
     */
    private String getProgramPath(DeviceContext context) {
        return fgy200Properties.getProgramPath();
    }

    /**
     * 验证程序路径
     */
    private boolean validateProgramPath(String programPath) {
        try {
            Path path = Paths.get(programPath);
            if (!Files.exists(path) || !Files.isDirectory(path)) {
                return false;
            }

            // 检查关键文件是否存在
            Path databasePath = path.resolve(fgy200Properties.getDatabaseFile());
            return Files.exists(databasePath);

        } catch (Exception e) {
            log.error("验证程序路径失败: {}", programPath, e);
            return false;
        }
    }

    /**
     * 验证数据库连接
     */
    private boolean validateDatabaseConnection(String programPath) {
        // 使用直接连接方式
        String databasePath = fgy200Properties.getDatabase().getPath();
        return validateDirectDatabaseConnection(databasePath);
    }

    /**
     * 验证直接数据库连接
     */
    private boolean validateDirectDatabaseConnection(String databasePath) {
        try (Connection conn = AccessDatabaseConnector.createConnection(databasePath)) {
            String countSql = fgy200Properties.getCountRecordsSql();
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(countSql)) {
                if (rs.next()) {
                    int count = rs.getInt(1);
                    log.info("直接数据库连接成功，数据表记录数: {}", count);
                    return true;
                }
            }
        } catch (SQLException e) {
            log.error("直接数据库连接失败: {}", databasePath, e);
        }

        return false;
    }

    /**
     * 写入患者信息到文件
     */
    private boolean writePatientInfoToFile(String programPath, PatientInfo patientInfo, ExamItemInfo examItemInfo) {
        Path testerIniPath = Paths.get(programPath, fgy200Properties.getPatientFile());

        if (examItemInfo == null) {
            log.error("写入健桥医电肺功能检测仪患者信息失败，examItemInfo为空，无法写入患者信息");
            throw new RuntimeException("写入健桥医电肺功能检测仪患者信息失败，examItemInfo为空，无法写入患者信息");
        }
        if (patientInfo == null) {
            log.error("写入健桥医电肺功能检测仪患者信息失败，patientInfo为空，无法写入患者信息");
            throw new RuntimeException("写入健桥医电肺功能检测仪患者信息失败，patientInfo为空，无法写入患者信息");
        }
        String height = "";
        String weight = "";
        //从examItemInfo的DependentItems中解析身高和体重的值
        if (examItemInfo.getDependentItems() != null) {
            for (ExamItemInfo dependentItem : examItemInfo.getDependentItems()) {
                if ("身高".equals(dependentItem.getItemName())) {
                    height = dependentItem.getValue();
                }
                if ("体重".equals(dependentItem.getItemName())) {
                    weight = dependentItem.getValue();
                }
            }
        }

        if (StringUtils.isEmpty(height) || StringUtils.isEmpty(weight)) {
            log.error("写入健桥医电肺功能检测仪患者信息失败，身高或体重为空，无法写入患者信息");
            throw new RuntimeException("写入健桥医电肺功能检测仪患者信息失败，身高或体重为空，无法写入患者信息");
        }

        String birthDate = patientInfo.getBirthDate();
        String[] ymd = {"", "", ""};
        int age = 0;

        if (StringUtils.isNotEmpty(birthDate) && birthDate.length() >= 10) {
            try {
                // 使用LocalDate解析生日
                LocalDate birthLocalDate = LocalDate.parse(birthDate.substring(0, 10));

                // 提取年月日
                ymd[0] = String.valueOf(birthLocalDate.getYear());
                ymd[1] = String.format("%02d", birthLocalDate.getMonthValue());
                ymd[2] = String.format("%02d", birthLocalDate.getDayOfMonth());

                // 计算年龄
                age = Period.between(birthLocalDate, LocalDate.now()).getYears();

            } catch (Exception e) {
                log.warn("生日格式解析失败: {}", birthDate, e);
            }
        }

        // 构建患者信息内容
        StringBuilder content = new StringBuilder();
        content.append("[Test]\n");
        content.append("No=").append(patientInfo.getExamNo()).append("\n");
        content.append("NAME=").append(patientInfo.getName()).append("\n");
        content.append("dianhua=").append(patientInfo.getPhone() != null ? patientInfo.getPhone() : "").append("\n");
        content.append("shengao=").append(height).append("\n"); // 身高 - PatientInfo中暂无此字段
        content.append("tizhong=").append(weight).append("\n"); // 体重 - PatientInfo中暂无此字段
        content.append("xingbie=").append(patientInfo.getGender() != null ? patientInfo.getGender() : "").append("\n");
        content.append("SSP=").append("").append("\n"); // 收缩压
        content.append("DP=").append("").append("\n");  // 舒张压
        content.append("Adress=").append("").append("\n"); // 地址 - PatientInfo中暂无此字段
        content.append("Year=").append(ymd[0]).append("\n");
        content.append("Month=").append(ymd[1]).append("\n");
        content.append("Day=").append(ymd[2]).append("\n");
        content.append("nianling=").append(age).append("\n");

        // 使用配置的编码写入文件
        try {
            Files.writeString(testerIniPath, content.toString(), Charset.forName(fgy200Properties.getFileEncoding()));
        } catch (IOException e) {
            log.error("创建目录失败: {}", testerIniPath.getParent(), e);
            throw new RuntimeException("写入患者信息文件失败", e);
        }

        log.info("患者信息已写入文件: {}", testerIniPath);
        return true;
    }


    // =================== 全局监控接口实现 ===================

    @Override
    public boolean executeMonitoring(DeviceContext context, Object customData) {
        if (!(customData instanceof PatientTestStatus testStatus)) {
            log.warn("FGY-200监控: 自定义数据类型不匹配");
            return false;
        }

        String patientKey = generatePatientKey(context, testStatus.getPatientInfo());

        try {
            // 原子性检查和更新患者状态
            PatientTestStatus currentStatus = patientStatusMap.get(patientKey);
            if (currentStatus == null) {
                log.debug("患者状态不存在，停止监控: {}", patientKey);
                return false; // 停止监控
            }

            // 使用同步块确保状态检查和更新的原子性
            synchronized (currentStatus) {
                if ("COMPLETED".equals(currentStatus.getStatus())) {
                    log.debug("患者检测已完成，停止监控: {}", patientKey);
                    return false; // 停止监控
                }

                if ("PROCESSING".equals(currentStatus.getStatus())) {
                    log.debug("患者结果正在处理中，跳过本次监控: {}", patientKey);
                    return true; // 跳过本次监控，避免重复处理
                }

                // 检查是否有新的检测结果
                Map<String, Object> latestResult = queryLatestResultForPatient(currentStatus);
                if (latestResult != null) {
                    // 验证结果完整性
                    ResultValidationResult validationResult = validateResultCompleteness(latestResult, currentStatus);

                    if (validationResult.isComplete()) {
                        // 设置为处理中状态，防止重复处理
                        currentStatus.setStatus("PROCESSING");

                        log.info("检测结果验证通过: 患者={}, 消息={}", currentStatus.getPatientInfo().getName(), validationResult.getMessage());

                        // 记录警告信息
                        if (!validationResult.getWarnings().isEmpty()) {
                            log.warn("检测结果警告: 患者={}, 警告={}", currentStatus.getPatientInfo().getName(), validationResult.getWarnings());
                        }

                        try {
                            sendTestResult(context, currentStatus, latestResult);

                            // 更新状态为完成
                            currentStatus.setStatus("COMPLETED");
                            currentStatus.setEndTime(System.currentTimeMillis());

                            log.info("患者检测结果处理完成: {}", currentStatus.getPatientInfo().getName());
                            return false; // 监控完成
                        } catch (Exception e) {
                            // 如果处理失败，恢复为等待状态，允许重试
                            currentStatus.setStatus("WAITING_FOR_TEST");
                            log.error("处理检测结果失败，恢复为等待状态: 患者={}", currentStatus.getPatientInfo().getName(), e);
                            throw e;
                        }
                    } else {
                        // 结果不完整，记录日志但继续监控
                        log.debug("检测结果不完整，继续监控: 患者={}, 原因={}", currentStatus.getPatientInfo().getName(), validationResult.getMessage());

                        // 如果有缺失字段，记录详细信息
                        if (!validationResult.getMissingFields().isEmpty()) {
                            log.debug("缺失字段: {}", validationResult.getMissingFields());
                        }
                    }
                }
            }

            return true; // 继续监控

        } catch (Exception e) {
            log.error("FGY-200监控任务执行失败: 患者={}", testStatus.getPatientInfo().getName(), e);
            return false;
        }
    }

    @Override
    public boolean isMonitoringCompleted(DeviceContext context, Object customData) {
        if (!(customData instanceof PatientTestStatus testStatus)) {
            return true; // 数据类型不匹配，认为已完成
        }

        String patientKey = generatePatientKey(context, testStatus.getPatientInfo());

        PatientTestStatus currentStatus = patientStatusMap.get(patientKey);
        if (currentStatus == null) {
            return true; // 状态不存在，认为已完成
        }

        return "COMPLETED".equals(currentStatus.getStatus());
    }

    @Override
    public MonitoringConfig getMonitoringConfig() {
        if (fgy200Properties != null) {
            return new MonitoringConfig(
                fgy200Properties.getMonitoring().getInterval(),
                fgy200Properties.getMonitoring().getTimeout(),
                5 // 最大错误次数
            );
        }
        // 返回默认配置
        return new MonitoringConfig(10, 300, 5);
    }

    /**
     * 生成患者监控任务的唯一键
     */
    private String generatePatientKey(DeviceContext context, PatientInfo patientInfo) {
        String deviceId = generateDeviceId(context);
        return deviceId + "_" + patientInfo.getExamNo();
    }

    /**
     * 查询最新检测结果
     * 从肺功能数据库查询数据，然后保存到体检主库
     */
    private boolean queryLatestResults(DeviceContext context) {
        try {
            String deviceId = generateDeviceId(context);
            String programPath = getProgramPath(context);

            log.info("开始查询FGY-200检测结果: 设备ID={}", deviceId);

            // 获取患者信息和检查日期
            String patientId = null;
            String checkDate = null;

            if (context.getPatientInfo() != null) {
                patientId = context.getPatientInfo().getExamNo();
                log.debug("使用患者ID查询: {}", patientId);
            }

            // 使用当前日期作为检查日期
            checkDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(fgy200Properties.getSqlConfig().getQueryParams().getDateFormat()));

            // 1. 查询肺功能数据库中的最新结果
            Map<String, Object> latestResult = queryLatestResultFromDatabaseWithParams(programPath, patientId, checkDate);
            if (latestResult != null) {
                log.info("从肺功能数据库查询到结果: 设备ID={}, 患者ID={}", deviceId, patientId);

                // 2. 保存到体检主数据库（复用现有逻辑）
                boolean savedToMainDb = saveToMainDatabase(context.getPatientInfo(), context.getExamItemInfo(), latestResult, context.getSessionId());

                // 3. 使用数据库存储响应构建器
                CommonDeviceResponse deviceResponse = DeviceResponseBuilder.createDatabaseStorageResponse(context.getSessionId(), deviceId, DEVICE_MODEL, context.getExamItemInfo(), context.getPatientInfo(), 0L,   // 没有耗时信息
                        "DATABASE");

                // 4. 转换为Map格式发送事件
                Map<String, Object> resultEvent = deviceResponse.toMap();
                resultEvent.put("resultType", ResponseType.MEASUREMENT_RESULT.getValue());

                context.getClient().sendEvent(EventConstants.TEST_RESULT, resultEvent);

                // 发送查询完成状态
                DeviceStatusSender.sendQueryCompleted(context.getClient(), context, true,
                    "查询成功，找到检测结果并已保存到数据库");

                log.info("FGY-200检测结果处理完成: 设备ID={}, 主数据库保存={}", deviceId, savedToMainDb ? "成功" : "失败");
                return true;
            } else {
                log.info("未找到新的检测结果: 设备ID={}, 患者ID={}", deviceId, patientId);

                // 发送查询完成状态（无结果）
                DeviceStatusSender.sendQueryCompleted(context.getClient(), context, false,
                    "查询完成，未找到新的检测结果");
                return false;
            }

        } catch (Exception e) {
            log.error("查询FGY-200检测结果失败", e);

            // 发送查询失败状态
            DeviceStatusSender.sendQueryStatus(context.getClient(), context, false,
                "查询失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 查询特定患者的最新结果
     */
    private Map<String, Object> queryLatestResultForPatient(PatientTestStatus testStatus) {
        try {
            String programPath = testStatus.getProgramPath();
            String patientId = testStatus.getPatientInfo().getExamNo();

            return queryResultFromDatabaseByPatientId(programPath, patientId, testStatus.getStartTime());

        } catch (Exception e) {
            log.error("查询患者检测结果失败: 患者ID={}", testStatus.getPatientInfo().getExamNo(), e);
            return null;
        }
    }


    /**
     * 从数据库查询最新结果（带参数）
     *
     * @param programPath 程序路径
     * @param patientId   患者ID
     * @param checkDate   检查日期
     */
    private Map<String, Object> queryLatestResultFromDatabaseWithParams(String programPath, String patientId, String checkDate) {

        String databasePath = fgy200Properties.getDatabase().getPath();
        return queryWithDirectConnectionWithParams(databasePath, patientId, checkDate);
    }





    /**
     * 根据日期范围查询数据（使用直接连接）
     */
    public List<Map<String, Object>> queryByDateRange(String startDate, String endDate) {
        String databasePath = fgy200Properties.getDatabase().getPath();

        try (Connection conn = AccessDatabaseConnector.createConnection(databasePath)) {
            String namedSql = fgy200Properties.getSelectByDateRangeSql();

            // 构建参数Map
            Map<String, Object> parameters = NamedParameterSqlProcessor.buildParams(
                    "startDate", startDate,
                    "endDate", endDate
            );

            // 验证参数
            List<String> missingParams = NamedParameterSqlProcessor.validateParameters(namedSql, parameters);
            if (!missingParams.isEmpty()) {
                log.warn("日期范围查询参数不完整，缺失参数: {}", missingParams);
                return new ArrayList<>();
            }

            log.debug("日期范围查询: startDate={}, endDate={}, SQL参数个数={}",
                    startDate, endDate, NamedParameterSqlProcessor.getParameterCount(namedSql));

            try (PreparedStatement pstmt = NamedParameterSqlProcessor.createPreparedStatement(conn, namedSql, parameters)) {
                try (ResultSet rs = pstmt.executeQuery()) {
                    List<Map<String, Object>> results = new ArrayList<>();
                    while (rs.next()) {
                        results.add(convertResultSetToMap(rs));
                    }
                    return results;
                }
            }
        } catch (SQLException e) {
            log.error("日期范围查询失败: startDate={}, endDate={}", startDate, endDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 统计患者记录数（使用直接连接）
     */
    public int countPatientRecords(String patientId) {
        String databasePath = fgy200Properties.getDatabase().getPath();

        try (Connection conn = AccessDatabaseConnector.createConnection(databasePath)) {
            String namedSql = fgy200Properties.getCountByPatientSql();

            // 构建参数Map
            Map<String, Object> parameters = NamedParameterSqlProcessor.buildParams("patientId", patientId);

            log.debug("统计患者记录: patientId={}, SQL参数个数={}",
                    patientId, NamedParameterSqlProcessor.getParameterCount(namedSql));

            try (PreparedStatement pstmt = NamedParameterSqlProcessor.createPreparedStatement(conn, namedSql, parameters)) {
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }
        } catch (SQLException e) {
            log.error("统计患者记录失败: patientId={}", patientId, e);
        }

        return 0;
    }


    /**
     * 使用直接文件连接查询（带参数）
     */
    private Map<String, Object> queryWithDirectConnectionWithParams(String databasePath, String patientId, String checkDate) {
        try (Connection conn = AccessDatabaseConnector.createConnection(databasePath)) {
            return executeQueryWithParams(conn, patientId, checkDate);
        } catch (SQLException e) {
            log.error("直接数据库查询失败: {}", databasePath, e);
            return null;
        }
    }


    /**
     * 执行查询
     */
    private Map<String, Object> executeQuery(Connection conn) throws SQLException {
        String sql = fgy200Properties.getSelectLatestSql();

        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return convertResultSetToMap(rs);
            }
        }

        return null;
    }

    /**
     * 执行查询（带参数，使用具名占位符）
     */
    private Map<String, Object> executeQueryWithParams(Connection conn, String patientId, String checkDate) throws SQLException {
        if (patientId != null && checkDate != null) {
            // 按患者ID和检查日期查询
            return executeQueryByPatientIdAndDate(conn, patientId, checkDate);
        } else if (patientId != null) {
            // 只按患者ID查询，使用当前日期作为起始日期
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern(fgy200Properties.getSqlConfig().getQueryParams().getDateFormat()));
            return executeQueryByPatientIdAndDate(conn, patientId, today);
        } else {
            // 查询所有患者的最新结果
            return executeQuery(conn);
        }
    }

    /**
     * 按患者ID和日期查询（使用具名占位符）
     */
    private Map<String, Object> executeQueryByPatientIdAndDate(Connection conn, String patientId, String checkDate) throws SQLException {
        String namedSql = fgy200Properties.getSelectByPatientSql();

        // 构建参数Map
        Map<String, Object> parameters = NamedParameterSqlProcessor.buildParams(
                "patientId", patientId,
                "startDate", checkDate
        );

        log.debug("执行患者和日期查询: patientId={}, checkDate={}, SQL参数个数={}",
                patientId, checkDate, NamedParameterSqlProcessor.getParameterCount(namedSql));

        try (PreparedStatement pstmt = NamedParameterSqlProcessor.createPreparedStatement(conn, namedSql, parameters)) {
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return convertResultSetToMap(rs);
                }
            }
        }

        return null;
    }

    /**
     * 根据患者ID查询结果
     */
    private Map<String, Object> queryResultFromDatabaseByPatientId(String programPath, String patientId, long startTime) {
        // 使用直接连接方式
        String databasePath = fgy200Properties.getDatabase().getPath();
        return queryByPatientIdWithDirectConnection(databasePath, patientId, startTime);
    }




    /**
     * 使用直接文件连接根据患者ID查询
     */
    private Map<String, Object> queryByPatientIdWithDirectConnection(String databasePath, String patientId, long startTime) {
        try (Connection conn = AccessDatabaseConnector.createConnection(databasePath)) {
            return executeQueryByPatientId(conn, patientId, startTime);
        } catch (SQLException e) {
            log.error("直接连接患者查询失败: 患者ID={}", patientId, e);
            return null;
        }
    }

    /**
     * 执行根据患者ID的查询（使用具名占位符）
     */
    private Map<String, Object> executeQueryByPatientId(Connection conn, String patientId, long startTime) throws SQLException {
        // 将时间戳转换为日期字符串进行比较
        String startDate = LocalDateTime.ofEpochSecond(startTime / 1000, 0, java.time.ZoneOffset.ofHours(8))
                .format(DateTimeFormatter.ofPattern(fgy200Properties.getSqlConfig().getQueryParams().getDateFormat()));

        String namedSql = fgy200Properties.getSelectByPatientSql();

        // 构建参数Map
        Map<String, Object> parameters = NamedParameterSqlProcessor.buildParams(
                "patientId", patientId,
                "startDate", startDate
        );

        // 验证参数完整性
        List<String> missingParams = NamedParameterSqlProcessor.validateParameters(namedSql, parameters);
        if (!missingParams.isEmpty()) {
            log.warn("SQL参数不完整，缺失参数: {}", missingParams);
        }

        log.debug("执行患者查询: patientId={}, startDate={}, SQL参数个数={}",
                patientId, startDate, NamedParameterSqlProcessor.getParameterCount(namedSql));

        try (PreparedStatement pstmt = NamedParameterSqlProcessor.createPreparedStatement(conn, namedSql, parameters)) {
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return convertResultSetToMap(rs);
                }
            }
        }

        return null;
    }

    /**
     * 将ResultSet转换为Map
     * 简化版本：直接将所有字段转换为Map，由体检主程序根据字段名获取值
     */
    private Map<String, Object> convertResultSetToMap(ResultSet rs) throws SQLException {
        Map<String, Object> result = new HashMap<>();

        // 获取结果集元数据
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        // 将所有字段添加到Map中
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            Object value = rs.getObject(i);
            result.put(columnName, value);
        }

        return result;

    }

    /**
     * 发送检测结果
     */
    private void sendTestResult(DeviceContext context, PatientTestStatus testStatus, Map<String, Object> result) {
        try {
            // 创建肺功能体检项目信息
            ExamItemInfo examItem = context.getExamItemInfo();


            // 使用数据库存储响应构建器
            CommonDeviceResponse deviceResponse = DeviceResponseBuilder.createDatabaseStorageResponse(context.getSessionId(), testStatus.getDeviceId(), DEVICE_MODEL, examItem, testStatus.getPatientInfo(), System.currentTimeMillis() - testStatus.getStartTime(), "DATABASE");

            // 保存到体检主数据库
            boolean savedToMainDb = saveToMainDatabase(testStatus.getPatientInfo(), examItem, result, context.getSessionId());


            // 转换为Map格式发送事件
            Map<String, Object> resultEvent = deviceResponse.toMap();
            resultEvent.put("resultType", ResponseType.MEASUREMENT_RESULT.getValue());

            context.getClient().sendEvent(EventConstants.TEST_RESULT, resultEvent);

            log.info("FGY-200检测结果处理完成: 患者={}, 主数据库保存={}", testStatus.getPatientInfo().getName(), savedToMainDb ? "成功" : "失败");

        } catch (Exception e) {
            log.error("发送检测结果失败", e);
        }
    }


    /**
     * 保存结果到体检主数据库
     */
    private boolean saveToMainDatabase(PatientInfo patientInfo, ExamItemInfo examItem, Map<String, Object> result, String userId) {
        try {
            // 检查服务是否可用
            if (fgy200Service == null) {
                log.warn("FGY200Service未注入，无法保存到体检主数据库");
                return false;
            }

            // 使用专门的肺功能数据库服务保存结果
            boolean saved = fgy200Service.saveLungFunctionResult(patientInfo, examItem, result, DEVICE_MODEL, userId);

            if (saved) {
                log.info("肺功能结果已保存到体检主数据库: 患者={}", patientInfo.getName());
            } else {
                log.warn("肺功能结果保存到体检主数据库失败: 患者={}", patientInfo.getName());
            }

            return saved;

        } catch (Exception e) {
            log.error("保存肺功能结果到体检主数据库时发生异常: 患者={}", patientInfo.getName(), e);
            return false;
        }
    }


    // =================== 结果完整性验证方法 ===================

    /**
     * 验证检测结果是否完整
     *
     * @param result     检测结果数据
     * @param testStatus 患者测试状态
     * @return 验证结果对象
     */
    private ResultValidationResult validateResultCompleteness(Map<String, Object> result, PatientTestStatus testStatus) {
        ResultValidationResult validationResult = new ResultValidationResult();

        if (!fgy200Properties.getResultValidation().isEnabled()) {
            // 如果未启用验证，则认为结果完整
            validationResult.setComplete(true);
            validationResult.setMessage("结果验证已禁用，跳过验证");
            return validationResult;
        }

        if (result == null || result.isEmpty()) {
            validationResult.setComplete(false);
            validationResult.setMessage("检测结果为空");
            return validationResult;
        }

        List<String> missingFields = new ArrayList<>();
        List<String> invalidValues = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 1. 验证必需字段
        if (!validateRequiredFields(result, missingFields)) {
            validationResult.setComplete(false);
            validationResult.setMessage("缺少必需字段: " + String.join(", ", missingFields));
            validationResult.setMissingFields(missingFields);
            return validationResult;
        }

        // 2. 验证数值范围
        validateValueRanges(result, invalidValues, warnings);

        // 3. 验证患者状态
        if (!validatePatientStatus(result, warnings)) {
            validationResult.setComplete(false);
            validationResult.setMessage("患者状态表明检测未完成");
            validationResult.setWarnings(warnings);
            return validationResult;
        }

        // 4. 验证检测时间
        if (!validateTestTime(result, testStatus, warnings)) {
            // 时间验证失败不影响结果完整性，只记录警告
            log.warn("检测时间验证失败，但不影响结果完整性");
        }

        // 5. 检查重要字段
        validateImportantFields(result, warnings);

        // 如果所有验证都通过
        validationResult.setComplete(true);
        validationResult.setMessage("检测结果完整且有效");
        validationResult.setInvalidValues(invalidValues);
        validationResult.setWarnings(warnings);

        return validationResult;
    }

    /**
     * 验证必需字段
     */
    private boolean validateRequiredFields(Map<String, Object> result, List<String> missingFields) {
        List<String> requiredFields = fgy200Properties.getResultValidation().getRequiredFields();

        for (String field : requiredFields) {
            Object value = result.get(field);
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                missingFields.add(field);
            }
        }

        return missingFields.isEmpty();
    }

    /**
     * 验证数值范围
     */
    private void validateValueRanges(Map<String, Object> result, List<String> invalidValues, List<String> warnings) {
        Map<String, FGY200Properties.ResultValidation.ValueRange> valueRanges = fgy200Properties.getResultValidation().getValueRanges();

        for (Map.Entry<String, FGY200Properties.ResultValidation.ValueRange> entry : valueRanges.entrySet()) {
            String fieldName = entry.getKey();
            FGY200Properties.ResultValidation.ValueRange range = entry.getValue();

            Object value = result.get(fieldName);
            if (value != null) {
                try {
                    double numValue = Double.parseDouble(value.toString());
                    if (numValue < range.getMin() || numValue > range.getMax()) {
                        String message = String.format("%s值%.2f超出正常范围[%.2f-%.2f]%s", fieldName, numValue, range.getMin(), range.getMax(), range.getUnit() != null ? range.getUnit() : "");
                        invalidValues.add(message);
                        log.warn("数值范围验证失败: {}", message);
                    }
                } catch (NumberFormatException e) {
                    warnings.add(fieldName + "值格式无效: " + value);
                }
            }
        }
    }

    /**
     * 验证患者状态
     */
    private boolean validatePatientStatus(Map<String, Object> result, List<String> warnings) {
        Object statusObj = result.get("受检者状态");
        if (statusObj == null) {
            warnings.add("未找到患者状态信息");
            return true; // 没有状态信息时不影响完整性判断
        }

        String status = statusObj.toString().trim();
        FGY200Properties.ResultValidation.PatientStatus patientStatusConfig = fgy200Properties.getResultValidation().getPatientStatus();

        // 检查是否为未完成状态
        for (String incompleteStatus : patientStatusConfig.getIncompleteStatus()) {
            if (status.contains(incompleteStatus)) {
                warnings.add("患者状态表明检测未完成: " + status);
                return false;
            }
        }

        // 检查是否为完成状态
        boolean isCompleted = false;
        for (String completedStatus : patientStatusConfig.getCompletedStatus()) {
            if (status.contains(completedStatus)) {
                isCompleted = true;
                break;
            }
        }

        if (!isCompleted) {
            warnings.add("患者状态未明确表明检测完成: " + status);
        }

        return true; // 即使状态不明确，也不阻止结果处理
    }

    /**
     * 验证检测时间
     */
    private boolean validateTestTime(Map<String, Object> result, PatientTestStatus testStatus, List<String> warnings) {
        if (!fgy200Properties.getResultValidation().getTimeValidation().isValidateTestTime()) {
            return true; // 未启用时间验证
        }

        Object dateObj = result.get("日期");
        Object timeObj = result.get("时间");

        if (dateObj == null || timeObj == null) {
            warnings.add("缺少检测日期或时间信息");
            return false;
        }

        try {
            String dateStr = dateObj.toString();
            String timeStr = timeObj.toString();

            // 解析检测时间
            LocalDateTime testDateTime = LocalDateTime.parse(dateStr + " " + timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 转换为时间戳
            long testTimestamp = testDateTime.atZone(java.time.ZoneId.systemDefault()).toEpochSecond() * 1000;

            // 检查是否在测量开始时间之后
            long timeTolerance = fgy200Properties.getResultValidation().getTimeValidation().getTimeTolerance() * 1000L;
            if (testTimestamp < (testStatus.getStartTime() - timeTolerance)) {
                warnings.add("检测时间早于测量开始时间");
                return false;
            }

            return true;

        } catch (Exception e) {
            warnings.add("检测时间格式解析失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证重要字段
     */
    private void validateImportantFields(Map<String, Object> result, List<String> warnings) {
        List<String> importantFields = fgy200Properties.getResultValidation().getImportantFields();
        List<String> missingImportantFields = new ArrayList<>();

        for (String field : importantFields) {
            Object value = result.get(field);
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                missingImportantFields.add(field);
            }
        }

        if (!missingImportantFields.isEmpty()) {
            warnings.add("缺少重要字段: " + String.join(", ", missingImportantFields));
        }
    }

    // =================== 内部类 ===================

    /**
     * 患者测试状态
     */
    @Data
    private static class PatientTestStatus {
        private PatientInfo patientInfo;
        private String deviceId;
        private String status;
        private long startTime;
        private long endTime;
        private String programPath;
    }

    /**
     * 结果验证结果
     */
    @Data
    private static class ResultValidationResult {
        /**
         * 结果是否完整
         */
        private boolean complete;

        /**
         * 验证消息
         */
        private String message;

        /**
         * 缺失的字段列表
         */
        private List<String> missingFields = new ArrayList<>();

        /**
         * 无效值列表
         */
        private List<String> invalidValues = new ArrayList<>();

        /**
         * 警告信息列表
         */
        private List<String> warnings = new ArrayList<>();

    }
}
