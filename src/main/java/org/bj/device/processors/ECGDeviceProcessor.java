package org.bj.device.processors;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.DeviceProcessor;
import org.bj.device.entity.DeviceContext;
import org.bj.device.entity.DeviceSession;
import org.bj.device.DeviceSessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ECG心电图设备处理器
 * 适配统一架构的ECG设备专有处理器
 * 
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@Component
public class ECGDeviceProcessor implements DeviceProcessor {

    @Autowired
    private DeviceSessionManager deviceSessionManager;

    /** 设备状态映射 */
    private final ConcurrentMap<String, String> deviceStatusMap = new ConcurrentHashMap<>();

    /** ECG会话映射 */
    private final ConcurrentMap<String, ECGSession> sessionMap = new ConcurrentHashMap<>();
    
    @Override
    public String getDeviceModel() {
        return "ECG";
    }
    
    @Override
    public String getDescription() {
        return "ECG心电图设备处理器 - 支持多导联心电数据采集和分析";
    }
    
    @Override
    public boolean handleConnection(DeviceContext context) {
        return handleConnection(context.getClient(), context.getPortDescriptor(), context.getSessionId());
    }

    // 保持向后兼容的方法
    public boolean handleConnection(SocketIOClient client, String portDescriptor, String userId) {
        try {
            log.info("ECG设备连接: 端口={}, 用户={}", portDescriptor, userId);

            // 1. 创建设备会话
            DeviceSession deviceSession = deviceSessionManager.createDeviceSession(
                client, userId, portDescriptor, "ECG");

            // 2. 处理患者关联（从客户端上下文获取患者信息）
            handlePatientAssociation(client, portDescriptor, userId, deviceSession);

            // 3. 更新设备状态
            deviceStatusMap.put(portDescriptor, "CONNECTING");

            // 4. 创建ECG会话
            ECGSession session = new ECGSession(portDescriptor, userId);
            session.setDeviceSessionId(deviceSession.getSessionId());
            sessionMap.put(portDescriptor, session);

            // 5. 发送初始化指令
            boolean initSuccess = sendCommand(client, portDescriptor, "ECG:INIT", userId);

            if (initSuccess) {
                deviceStatusMap.put(portDescriptor, "CONNECTED");

                // 发送连接成功事件
                client.sendEvent("ecgDeviceConnected", createConnectionResponse(true,
                    "ECG设备连接成功", portDescriptor, userId));

                log.info("ECG设备连接成功: 端口={}, 会话ID={}", portDescriptor, deviceSession.getSessionId());
                return true;
            } else {
                deviceStatusMap.put(portDescriptor, "ERROR");
                client.sendEvent("ecgDeviceConnected", createConnectionResponse(false,
                    "ECG设备初始化失败", portDescriptor, userId));
                return false;
            }

        } catch (Exception e) {
            log.error("ECG设备连接失败", e);
            deviceStatusMap.put(portDescriptor, "ERROR");
            client.sendEvent("ecgDeviceConnected", createConnectionResponse(false,
                "ECG设备连接异常: " + e.getMessage(), portDescriptor, userId));
            return false;
        }
    }
    
    @Override
    public boolean handleDisconnection(DeviceContext context) {
        return handleDisconnection(context.getClient(), context.getPortDescriptor(), context.getSessionId());
    }

    // 保持向后兼容的方法
    public boolean handleDisconnection(SocketIOClient client, String portDescriptor, String userId) {
        try {
            log.info("ECG设备断开: 端口={}, 用户={}", portDescriptor, userId);
            
            // 清理资源
            deviceStatusMap.remove(portDescriptor);
            sessionMap.remove(portDescriptor);
            
            // 发送断开事件
            client.sendEvent("ecgDeviceDisconnected", createConnectionResponse(true, 
                "ECG设备已断开", portDescriptor, userId));
            
            return true;
            
        } catch (Exception e) {
            log.error("ECG设备断开失败", e);
            return false;
        }
    }
    
    @Override
    public boolean handleData(DeviceContext context, String rawData) {
        try {
            log.debug("处理ECG数据: 端口={}, 数据={}", context.getPortDescriptor(), rawData);
            
            // 获取ECG会话
            ECGSession session = sessionMap.get(context.getPortDescriptor());
            if (session == null) {
                log.warn("未找到ECG会话: 端口={}", context.getPortDescriptor());
                return false;
            }
            
            // 检查数据类型并处理
            if (rawData.startsWith("ECG:START")) {
                return handleStartCommand(context, session, rawData);
            } else if (rawData.startsWith("ECG:DATA")) {
                return handleDataPacket(context, session, rawData);
            } else if (rawData.startsWith("ECG:END")) {
                return handleEndCommand(context, session, rawData);
            } else if (rawData.startsWith("HR:")) {
                return handleHeartRateData(context, session, rawData);
            } else if (rawData.contains("LEAD_")) {
                return handleLeadData(context, session, rawData);
            } else {
                // 处理原始波形数据
                return handleWaveformData(context, session, rawData);
            }
            
        } catch (Exception e) {
            log.error("ECG数据处理失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendCommand(DeviceContext context, String command) {
        return sendCommand(context.getClient(), context.getPortDescriptor(), command, context.getSessionId());
    }

    // 保持向后兼容的方法
    public boolean sendCommand(SocketIOClient client, String portDescriptor, String command, String userId) {
        try {
            log.info("发送ECG设备指令: 端口={}, 指令={}", portDescriptor, command);
            
            // 构建ECG设备指令
            String deviceCommand = buildECGCommand(command);
            
            // 发送串口命令事件
            client.sendEvent("sendSerialCommand", createSerialCommand(portDescriptor, deviceCommand));
            
            return true;
            
        } catch (Exception e) {
            log.error("发送ECG设备指令失败", e);
            return false;
        }
    }
    
    @Override
    public String getDeviceStatus(DeviceContext context) {
        return getDeviceStatus(context.getPortDescriptor());
    }

    // 保持向后兼容的方法
    public String getDeviceStatus(String portDescriptor) {
        return deviceStatusMap.getOrDefault(portDescriptor, "UNKNOWN");
    }
    
    @Override
    public boolean resetDevice(SocketIOClient client, String portDescriptor, String userId) {
        return sendCommand(client, portDescriptor, "ECG:RESET", userId);
    }
    
    @Override
    public boolean calibrateDevice(SocketIOClient client, String portDescriptor, String userId) {
        return sendCommand(client, portDescriptor, "ECG:CALIBRATE", userId);
    }
    
    @Override
    public boolean supportsFeature(String feature) {
        switch (feature.toUpperCase()) {
            case "MULTI_LEAD":
            case "HEART_RATE":
            case "WAVEFORM_DATA":
            case "REAL_TIME_ANALYSIS":
                return true;
            default:
                return false;
        }
    }
    
    // =================== 私有方法 ===================
    
    /**
     * 处理患者关联
     */
    private void handlePatientAssociation(SocketIOClient client, String portDescriptor, String userId, DeviceSession deviceSession) {
        try {
            // ECG设备的患者关联逻辑
            // 从客户端请求中获取患者信息（这里简化处理，实际应该从请求上下文获取）
            log.debug("处理ECG设备患者关联: 端口={}, 会话ID={}", portDescriptor, deviceSession.getSessionId());

            // 这里可以添加ECG设备特有的患者关联逻辑
            // 例如：发送患者ID到ECG设备、设置患者参数等

            // 发送患者关联成功事件
            client.sendEvent("ecgPatientAssociated", createPatientAssociationResponse(true,
                "ECG设备患者关联成功", portDescriptor));

        } catch (Exception e) {
            log.error("ECG设备患者关联失败", e);
            client.sendEvent("ecgPatientAssociated", createPatientAssociationResponse(false,
                "ECG设备患者关联失败: " + e.getMessage(), portDescriptor));
        }
    }
    
    /**
     * 处理开始指令
     */
    private boolean handleStartCommand(DeviceContext context, ECGSession session, String data) {
        session.setStatus("RECORDING");
        context.getClient().sendEvent("ecgRecordingStarted", createRecordingResponse(session, "开始ECG记录"));
        return true;
    }
    
    /**
     * 处理数据包
     */
    private boolean handleDataPacket(DeviceContext context, ECGSession session, String data) {
        // 解析数据包：ECG:DATA:LEAD_I:1024,1025,1026...
        String[] parts = data.split(":", 4);
        if (parts.length >= 4) {
            String leadType = parts[2];
            String values = parts[3];
            
            // 添加到会话数据
            session.addLeadData(leadType, values);
            
            // 实时发送波形数据给前端
            context.getClient().sendEvent("ecgWaveformData", createWaveformResponse(session, leadType, values));
            
            return true;
        }
        return false;
    }
    
    /**
     * 处理结束指令
     */
    private boolean handleEndCommand(DeviceContext context, ECGSession session, String data) {
        session.setStatus("COMPLETED");
        
        // 进行ECG分析
        performECGAnalysis(context, session);
        
        return true;
    }
    
    /**
     * 处理心率数据
     */
    private boolean handleHeartRateData(DeviceContext context, ECGSession session, String data) {
        // 解析心率：HR:75
        String hrValue = data.substring(3);
        session.setHeartRate(Integer.parseInt(hrValue));
        
        // 实时发送心率给前端
        context.getClient().sendEvent("ecgHeartRate", createHeartRateResponse(session, hrValue));
        
        return true;
    }
    
    /**
     * 处理导联数据
     */
    private boolean handleLeadData(DeviceContext context, ECGSession session, String data) {
        log.debug("处理导联数据: {}", data);
        return true;
    }
    
    /**
     * 处理波形数据
     */
    private boolean handleWaveformData(DeviceContext context, ECGSession session, String data) {
        session.addRawWaveformData(data);
        return true;
    }
    
    /**
     * 执行ECG分析
     */
    private void performECGAnalysis(DeviceContext context, ECGSession session) {
        try {
            // 简化的ECG分析逻辑
            ECGAnalysisResult result = new ECGAnalysisResult();
            result.setHeartRate(session.getHeartRate());
            result.setRhythm("正常窦性心律");
            result.setConclusion("心电图正常");
            
            // 发送分析结果
            context.getClient().sendEvent("ecgAnalysisResult", result);
            
        } catch (Exception e) {
            log.error("ECG分析失败", e);
        }
    }
    
    /**
     * 构建ECG设备指令
     */
    private String buildECGCommand(String command) {
        // 根据指令类型构建设备特定的指令格式
        switch (command.toUpperCase()) {
            case "START_MEASUREMENT":
                return "ECG:START";
            case "STOP_MEASUREMENT":
                return "ECG:STOP";
            case "ECG:INIT":
                return "ECG:INIT";
            case "ECG:RESET":
                return "ECG:RESET";
            case "ECG:CALIBRATE":
                return "ECG:CAL";
            default:
                return command;
        }
    }
    
    // =================== 响应对象创建方法 ===================
    
    private Object createConnectionResponse(boolean success, String message, String portDescriptor, String userId) {
        return new Object() {
            public final boolean isSuccess = success;
            public final String statusMessage = message;
            public final String portDescriptorValue = portDescriptor;
            public final String userIdValue = userId;
            public final long timestamp = System.currentTimeMillis();
        };
    }
    
    private Object createPatientAssociationResponse(boolean success, String message, String portDescriptor) {
        return new Object() {
            public final boolean isSuccess = success;
            public final String statusMessage = message;
            public final String portDescriptorValue = portDescriptor;
            public final long timestamp = System.currentTimeMillis();
        };
    }
    
    private Object createRecordingResponse(ECGSession session, String message) {
        return new Object() {
            public final String sessionId = session.getSessionId();
            public final String status = session.getStatus();
            public final String statusMessage = message;
            public final long timestamp = System.currentTimeMillis();
        };
    }
    
    private Object createWaveformResponse(ECGSession session, String leadType, String values) {
        return new Object() {
            public final String sessionId = session.getSessionId();
            public final String leadTypeValue = leadType;
            public final String waveformData = values;
            public final long timestamp = System.currentTimeMillis();
        };
    }
    
    private Object createHeartRateResponse(ECGSession session, String heartRate) {
        return new Object() {
            public final String sessionId = session.getSessionId();
            public final String heartRateValue = heartRate;
            public final long timestamp = System.currentTimeMillis();
        };
    }
    
    private Object createSerialCommand(String portDescriptor, String command) {
        return new Object() {
            public final String port = portDescriptor;
            public final String data = command;
        };
    }
    
    // =================== 内部类 ===================
    
    /**
     * ECG会话
     */
    public static class ECGSession {
        private String sessionId;
        private String deviceSessionId;  // 设备会话ID
        private String portDescriptor;
        private String userId;
        private String status = "IDLE";
        private int heartRate = 0;
        private ConcurrentMap<String, String> leadData = new ConcurrentHashMap<>();
        private StringBuilder rawWaveformData = new StringBuilder();

        public ECGSession(String portDescriptor, String userId) {
            this.sessionId = "ECG_" + System.currentTimeMillis();
            this.portDescriptor = portDescriptor;
            this.userId = userId;
        }
        
        // Getters and Setters
        public String getSessionId() { return sessionId; }
        public String getDeviceSessionId() { return deviceSessionId; }
        public void setDeviceSessionId(String deviceSessionId) { this.deviceSessionId = deviceSessionId; }
        public String getPortDescriptor() { return portDescriptor; }
        public String getUserId() { return userId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public int getHeartRate() { return heartRate; }
        public void setHeartRate(int heartRate) { this.heartRate = heartRate; }
        
        public void addLeadData(String leadType, String data) {
            leadData.put(leadType, data);
        }
        
        public void addRawWaveformData(String data) {
            rawWaveformData.append(data);
        }
    }
    
    /**
     * ECG分析结果
     */
    public static class ECGAnalysisResult {
        private int heartRate;
        private String rhythm;
        private String conclusion;
        
        // Getters and Setters
        public int getHeartRate() { return heartRate; }
        public void setHeartRate(int heartRate) { this.heartRate = heartRate; }
        public String getRhythm() { return rhythm; }
        public void setRhythm(String rhythm) { this.rhythm = rhythm; }
        public String getConclusion() { return conclusion; }
        public void setConclusion(String conclusion) { this.conclusion = conclusion; }
    }
}
