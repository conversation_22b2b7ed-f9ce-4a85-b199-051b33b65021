package org.bj.device.processors;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.extern.slf4j.Slf4j;
import org.bj.constants.EventConstants;
import org.bj.constants.event.UserSwitchEvent;
import org.bj.device.DeviceProcessor;
import org.bj.device.util.DeviceResponseBuilder;
import org.bj.device.util.SerialDeviceResponseAdapter;
import org.bj.device.entity.CommonDeviceResponse;
import org.bj.device.entity.DeviceContext;
import org.bj.device.entity.DeviceProperties;
import org.bj.device.entity.SerialPortData;
import org.bj.device.util.DeviceStatusSender;
import org.bj.device.serialport.SerialPortResourceManager;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.List;

/**
 * 通用设备处理器
 * 提供基础的串口设备处理功能，作为所有专有设备处理器的基础实现
 *
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
public class GenericDeviceProcessor implements DeviceProcessor {

    @Autowired
    private SerialPortResourceManager serialPortResourceManager;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 设备状态映射
     */
    private final ConcurrentMap<String, String> deviceStatusMap = new ConcurrentHashMap<>();

    /**
     * 设备连接时间映射
     */
    private final ConcurrentMap<String, Long> connectionTimeMap = new ConcurrentHashMap<>();

    /**
     * 数据缓冲区映射
     */
    private final ConcurrentMap<String, StringBuilder> dataBufferMap = new ConcurrentHashMap<>();

    /**
     * 缓冲超时映射
     */
    private final ConcurrentMap<String, Long> bufferTimeoutMap = new ConcurrentHashMap<>();

    @Override
    public String getDeviceModel() {
        return "普通串口";
    }

    @Override
    public String getDescription() {
        return "通用串口设备处理器 - 支持基础的串口通信功能";
    }

    /**
     * 获取设备类型
     */
    public String getDeviceType() {
        return "GENERIC_SERIAL";
    }

    @Override
    public boolean handleConnection(DeviceContext context) {
        SocketIOClient client = context.getClient();
        String portDescriptor = context.getPortDescriptor();
        String sessionId = context.getSessionId();
        try {
            log.info("通用串口设备连接处理开始: 端口={}, 用户={}", portDescriptor, sessionId);

            // 1. 更新设备状态
            deviceStatusMap.put(portDescriptor, "CONNECTING");
            connectionTimeMap.put(portDescriptor, System.currentTimeMillis());

            // 2. 初始化数据缓冲区
            dataBufferMap.put(portDescriptor, new StringBuilder());
            bufferTimeoutMap.put(portDescriptor, System.currentTimeMillis());

            // 3. 实际打开串口 - 这是关键！
            boolean portOpened = openSerialPortForGeneric(context);
            if (!portOpened) {
                log.error("串口打开失败: 端口={}", portDescriptor);
                deviceStatusMap.put(portDescriptor, "ERROR");
                throw new RuntimeException("串口打开失败");
            }

            // 4. 发送连接成功事件
            deviceStatusMap.put(portDescriptor, "CONNECTED");
            DeviceStatusSender.sendConnected(client, context);

            log.info("通用设备连接成功: 端口={}, 用户={}", portDescriptor, sessionId);
            return true;
        } catch (Exception e) {
            log.error("通用设备连接失败: 端口={}, 用户={}", portDescriptor, sessionId, e);
            deviceStatusMap.put(portDescriptor, "ERROR");

            // 发送连接失败事件
            DeviceStatusSender.sendConnectionFailed(client, context, "连接失败: " + e.getMessage(), "CONNECTION_ERROR");
            return false;
        }
    }

    /**
     * 为通用设备打开串口（使用设备属性配置）
     */
    private boolean openSerialPortForGeneric(DeviceContext context) {
        try {
            String portDescriptor = context.getPortDescriptor();
            SocketIOClient client = context.getClient();
            String sessionId = context.getSessionId();
            DeviceProperties deviceProperties = context.getDeviceProperties();

            log.info("开始打开串口: 端口={}, 用户={}", portDescriptor, sessionId);

            // 1. 清理无效的串口会话
            serialPortResourceManager.cleanupInvalidSessions();

            // 2. 检查串口是否被占用
            if (serialPortResourceManager.isPortOccupied(portDescriptor)) {
                String patientId = context.getPatientInfo() != null ? context.getPatientInfo().getExamNo() : null;
                String patientName = context.getPatientInfo() != null ? context.getPatientInfo().getName() : null;

                // 检查是否为同一会话（同一用户+同一患者）
                if (serialPortResourceManager.isSameSession(portDescriptor, sessionId, patientId)) {
                    log.debug("串口已被当前会话占用，验证连接状态: 端口={}, 用户={}, 患者={}", portDescriptor, sessionId, patientName);

                    // 验证串口是否真的可用
                    com.fazecast.jSerialComm.SerialPort existingPort = serialPortResourceManager.getPortInstance(portDescriptor);
                    if (existingPort != null && existingPort.isOpen()) {
                        // 为当前上下文添加数据监听器
                        addDataListenerForGeneric(existingPort, context);
                        log.info("复用现有串口连接: 端口={}, 用户={}, 患者={}", portDescriptor, sessionId, patientName);
                        return true;
                    } else {
                        log.warn("同一会话但串口已关闭，需要重新建立连接: 端口={}", portDescriptor);
                        // 清理无效的会话信息
                        serialPortResourceManager.forceReleasePort(portDescriptor);
                        // 继续执行新连接逻辑
                    }
                } else {
                    // 不同会话，需要执行切换
                    SerialPortResourceManager.SessionInfo occupierSession = serialPortResourceManager.getPortOccupierSession(portDescriptor);
                    String occupierDescription = occupierSession != null ? occupierSession.getSessionDescription() : "未知会话";

                    log.info("串口被其他会话占用，执行会话切换: 端口={}, 原会话={}, 新会话={}:{}", portDescriptor, occupierDescription, sessionId, patientName);

                    // 执行会话切换：踢掉原会话，让新会话使用
                    boolean switchSuccess = switchPortSessionForGeneric(portDescriptor, occupierSession, sessionId, patientId, patientName, context);
                    if (!switchSuccess) {
                        log.error("会话切换失败: 端口={}, 原会话={}, 新会话={}:{}", portDescriptor, occupierDescription, sessionId, patientName);
                        return false;
                    }

                    log.info("会话切换成功: 端口={}, 原会话={}, 新会话={}:{}", portDescriptor, occupierDescription, sessionId, patientName);
                    return true;
                }
            }

            // 3. 创建新的串口连接
            log.debug("开始创建新的串口连接: 端口={}, 用户={}, 患者={}", portDescriptor, sessionId, context.getPatientInfo() != null ? context.getPatientInfo().getName() : "未知");

            com.fazecast.jSerialComm.SerialPort serialPort = com.fazecast.jSerialComm.SerialPort.getCommPort(portDescriptor);

            // 从设备属性中获取串口参数，如果没有则使用默认值
            int baudRate = getSerialBaudRate(deviceProperties);
            int dataBits = getSerialDataBits(deviceProperties);
            int stopBits = getSerialStopBits(deviceProperties);
            int parity = getSerialParity(deviceProperties);

            // 设置串口参数
            serialPort.setBaudRate(baudRate);
            serialPort.setNumDataBits(dataBits);
            serialPort.setNumStopBits(stopBits);
            serialPort.setParity(parity);

            log.info("串口参数配置: 端口={}, 波特率={}, 数据位={}, 停止位={}, 校验位={}", portDescriptor, baudRate, dataBits, stopBits, getParityName(parity));

            // 4. 打开串口
            if (serialPort.openPort()) {
                log.info("串口打开成功: 端口={}", portDescriptor);

                // 5. 注册串口使用（包含患者信息）
                String patientId = context.getPatientInfo() != null ? context.getPatientInfo().getExamNo() : null;
                String patientName = context.getPatientInfo() != null ? context.getPatientInfo().getName() : null;

                boolean registered = serialPortResourceManager.registerPortUsage(portDescriptor, sessionId, patientId, patientName, serialPort);
                if (!registered) {
                    log.error("注册串口使用失败: 端口={}, 用户={}, 患者={}", portDescriptor, sessionId, patientName);
                    serialPort.closePort();
                    return false;
                }

                // 6. 添加数据监听器
                addDataListenerForGeneric(serialPort, context);

                log.info("通用设备串口连接完成: 端口={}, 用户={}, 患者={}", portDescriptor, sessionId, patientName);
                return true;
            } else {
                log.error("串口打开失败: 端口={}", portDescriptor);
                return false;
            }

        } catch (Exception e) {
            log.error("打开串口异常: 端口={}", context.getPortDescriptor(), e);
            return false;
        }
    }

    /**
     * 获取串口波特率
     */
    private int getSerialBaudRate(DeviceProperties deviceProperties) {
        if (deviceProperties != null && deviceProperties.getBaudRate() != null) {
            return deviceProperties.getBaudRate();
        }
        return 9600; // 默认波特率
    }

    /**
     * 获取串口数据位
     */
    private int getSerialDataBits(DeviceProperties deviceProperties) {
        if (deviceProperties != null && deviceProperties.getDataBits() != null) {
            return deviceProperties.getDataBits();
        }
        return 8; // 默认数据位
    }

    /**
     * 获取串口停止位
     */
    private int getSerialStopBits(DeviceProperties deviceProperties) {
        if (deviceProperties != null && deviceProperties.getStopBits() != null) {
            return deviceProperties.getStopBits();
        }
        return 1; // 默认停止位
    }

    /**
     * 获取串口校验位
     */
    private int getSerialParity(DeviceProperties deviceProperties) {
        if (deviceProperties != null && deviceProperties.getParity() != null) {
            int parity = deviceProperties.getParity();
            switch (parity) {
                case 1:
                    return com.fazecast.jSerialComm.SerialPort.ODD_PARITY;  // 奇校验
                case 2:
                    return com.fazecast.jSerialComm.SerialPort.EVEN_PARITY; // 偶校验
                case 0:
                default:
                    return com.fazecast.jSerialComm.SerialPort.NO_PARITY;   // 无校验
            }
        }
        return com.fazecast.jSerialComm.SerialPort.NO_PARITY; // 默认无校验
    }

    /**
     * 获取校验位名称（用于日志）
     */
    private String getParityName(int parity) {
        switch (parity) {
            case com.fazecast.jSerialComm.SerialPort.EVEN_PARITY:
                return "EVEN(偶校验)";
            case com.fazecast.jSerialComm.SerialPort.ODD_PARITY:
                return "ODD(奇校验)";
            case com.fazecast.jSerialComm.SerialPort.MARK_PARITY:
                return "MARK";
            case com.fazecast.jSerialComm.SerialPort.SPACE_PARITY:
                return "SPACE";
            case com.fazecast.jSerialComm.SerialPort.NO_PARITY:
            default:
                return "NONE(无校验)";
        }
    }

    /**
     * 为通用设备添加数据监听器
     */
    private void addDataListenerForGeneric(com.fazecast.jSerialComm.SerialPort serialPort, DeviceContext context) {
        String portDescriptor = context.getPortDescriptor();
        SocketIOClient client = context.getClient();
        String userId = context.getSessionId();
        serialPort.addDataListener(new com.fazecast.jSerialComm.SerialPortDataListener() {
            @Override
            public int getListeningEvents() {
                return com.fazecast.jSerialComm.SerialPort.LISTENING_EVENT_DATA_AVAILABLE;
            }

            @Override
            public void serialEvent(com.fazecast.jSerialComm.SerialPortEvent event) {
                if (event.getEventType() == com.fazecast.jSerialComm.SerialPort.LISTENING_EVENT_DATA_AVAILABLE) {
                    try {
                        // 读取串口数据
                        byte[] readBuffer = new byte[2048];
                        int numRead = serialPort.readBytes(readBuffer, readBuffer.length);
                        String rawData = new String(readBuffer, 0, numRead);

                        log.info("=== 通用设备串口数据接收 ===");
                        log.info("端口: {}", portDescriptor);
                        log.info("用户: {}", userId);
                        log.info("数据长度: {} 字节", numRead);
                        log.info("原始数据: {}", rawData);
                        log.info("==========================");

                        // 记录数据接收（通过日志）
                        log.debug("记录数据接收: 端口={}, 数据长度={}", portDescriptor, rawData.length());

                        // ✅ 修复：通过handleData方法处理数据，确保经过缓存逻辑
                        handleData(context, rawData);

                    } catch (Exception e) {
                        log.error("通用设备数据处理异常: 端口={}", portDescriptor, e);
                        log.error("记录错误: 端口={}, 错误={}", portDescriptor, "数据处理异常: " + e.getMessage());
                    }
                }
            }
        });

        // 记录监听器添加（通过日志）
        log.debug("记录监听器添加: 端口={}", portDescriptor);
        log.info("添加通用设备数据监听器成功: 端口={}", portDescriptor);
    }

    @Override
    public boolean handleDisconnection(DeviceContext context) {
        SocketIOClient client = context.getClient();
        String portDescriptor = context.getPortDescriptor();
        String userId = context.getSessionId();
        try {
            log.info("通用设备断开处理开始: 端口={}, 用户={}", portDescriptor, userId);

            // 1. 更新设备状态
            deviceStatusMap.put(portDescriptor, "DISCONNECTING");

            // 2. 清理串口资源管理器中的资源
            String patientId = context.getPatientInfo() != null ? context.getPatientInfo().getExamNo() : null;
            serialPortResourceManager.unregisterPortUsage(portDescriptor, userId, patientId);

            // 3. 清理本地资源
            cleanupDeviceResources(portDescriptor);

            // 4. 发送断开连接事件
            DeviceStatusSender.sendDisconnected(client, context);

            // 4. 移除设备状态
            deviceStatusMap.remove(portDescriptor);

            log.info("通用设备断开成功: 端口={}, 用户={}", portDescriptor, userId);
            return true;

        } catch (Exception e) {
            log.error("通用设备断开失败: 端口={}, 用户={}", portDescriptor, userId, e);
            return false;
        }
    }

    @Override
    public boolean handleData(DeviceContext context, String rawData) {
        try {
            String portDescriptor = context.getPortDescriptor();
            log.debug("通用设备数据处理开始: 端口={}, 数据长度={}", portDescriptor, rawData != null ? rawData.length() : 0);

            if (rawData == null || rawData.trim().isEmpty()) {
                log.debug("接收到空数据，忽略处理");
                return true;
            }

            // 1. 检查是否需要数据缓冲
            if (context.getDeviceNeedsBuffering()) {
                return handleBufferedData(context, rawData);
            } else {
                return handleDirectData(context, rawData);
            }

        } catch (Exception e) {
            log.error("通用设备数据处理失败: 上下文={}", context.getContextId(), e);
            return false;
        }
    }

    /**
     * 处理需要缓冲的数据
     */
    private boolean handleBufferedData(DeviceContext context, String rawData) {
        String portDescriptor = context.getPortDescriptor();
        StringBuilder buffer = dataBufferMap.get(portDescriptor);

        if (buffer == null) {
            buffer = new StringBuilder();
            dataBufferMap.put(portDescriptor, buffer);
            log.debug("为端口 {} 创建新的数据缓冲区", portDescriptor);
        }

        // 记录接收到的原始数据
        log.info("=== 数据缓冲处理 ===");
        log.info("端口: {}", portDescriptor);
        log.info("接收数据: {}", rawData);
        log.info("缓冲区当前内容: {}", buffer.toString());

        // 添加数据到缓冲区
        buffer.append(rawData);
        bufferTimeoutMap.put(portDescriptor, System.currentTimeMillis());

        log.info("缓冲区更新后内容: {}", buffer.toString());
        log.info("缓冲区大小: {} 字符", buffer.length());

        // 检查是否达到完整数据条件
        String completeData = checkForCompleteData(context, buffer.toString());
        if (completeData != null) {
            log.info("检测到完整数据，开始处理: {}", completeData);

            // 处理完整数据
            boolean result = processCompleteData(context, completeData);

            // 清空缓冲区
            buffer.setLength(0);
            log.info("缓冲区已清空");
            log.info("==================");
            return result;
        }

        log.info("数据不完整，继续缓冲");
        log.info("==================");

        // 检查缓冲区是否超时或超大小
        checkBufferLimits(context, buffer);

        return true;
    }

    /**
     * 处理直接数据（无需缓冲）
     */
    private boolean handleDirectData(DeviceContext context, String rawData) {
        log.debug("直接处理数据: 上下文={}", context.getContextId());
        return processCompleteData(context, rawData);
    }

    /**
     * 检查是否为完整数据
     */
    private String checkForCompleteData(DeviceContext context, String bufferedData) {
        log.debug("检查数据完整性: 缓冲区大小={}, 数据={}", bufferedData.length(), bufferedData);

        // 1. 检查是否包含配置的结束标记
        List<String> endMarkers = getDataEndMarkers(context);
        if (endMarkers != null && !endMarkers.isEmpty()) {
            for (String endMarker : endMarkers) {
                if (bufferedData.contains(endMarker)) {
                    int endIndex = bufferedData.indexOf(endMarker);
                    // 提取不包含结束标记的数据
                    String completeData = bufferedData.substring(0, endIndex);
                    log.debug("发现结束标记 '{}', 提取完整数据（已移除结束标记）: {}", endMarker, completeData);
                    return completeData;
                }
            }
            log.debug("未发现任何结束标记，数据不完整");
            return null;
        }

        // 2. 默认检查：以换行符结尾的数据认为是完整的
        if (bufferedData.endsWith("\n") || bufferedData.endsWith("\r\n")) {
            log.debug("发现换行符结束标记，数据完整");
            return bufferedData.trim();
        }

        log.debug("数据不完整，继续缓冲");
        return null;
    }

    /**
     * 获取数据结束标记列表
     */
    private List<String> getDataEndMarkers(DeviceContext context) {
        try {
            DeviceProperties deviceProperties = context.getDeviceProperties();
            if (deviceProperties == null || deviceProperties.getDataEndMarkers() == null) {
                log.debug("设备属性或结束标记配置为空");
                return null;
            }

            String endMarkersJson = deviceProperties.getDataEndMarkers();
            log.debug("解析结束标记配置: {}", endMarkersJson);

            return parseJsonArray(endMarkersJson);

        } catch (Exception e) {
            log.error("获取数据结束标记失败", e);
            return null;
        }
    }

    /**
     * 解析JSON数组字符串
     */
    private List<String> parseJsonArray(String jsonArray) {
        try {
            if (jsonArray == null || jsonArray.trim().isEmpty()) {
                return null;
            }

            // 使用Jackson ObjectMapper解析JSON数组
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.core.type.TypeReference<List<String>> typeRef = new com.fasterxml.jackson.core.type.TypeReference<List<String>>() {
            };
            List<String> result = objectMapper.readValue(jsonArray, typeRef);

            log.debug("成功解析JSON数组: {} -> {}", jsonArray, result);
            return result;

        } catch (Exception e) {
            log.error("解析JSON数组失败: {}", jsonArray, e);
            return null;
        }
    }

    /**
     * 检查缓冲区限制
     */
    private void checkBufferLimits(DeviceContext context, StringBuilder buffer) {
        String portDescriptor = context.getPortDescriptor();
        Long lastBufferTime = bufferTimeoutMap.get(portDescriptor);

        // 检查超时
        if (lastBufferTime != null) {
            long timeout = context.getDeviceBufferTimeout();
            if (System.currentTimeMillis() - lastBufferTime > timeout) {
                log.warn("缓冲区超时，强制处理数据: 端口={}, 超时={}ms", portDescriptor, timeout);
                processCompleteData(context, buffer.toString());
                buffer.setLength(0);
                return;
            }
        }

        // 检查大小限制
        int maxSize = context.getDeviceMaxBufferSize();
        if (buffer.length() > maxSize) {
            log.warn("缓冲区超大小，强制处理数据: 端口={}, 大小={}", portDescriptor, buffer.length());
            processCompleteData(context, buffer.toString());
            buffer.setLength(0);
        }
    }

    /**
     * 处理完整的数据
     */
    private boolean processCompleteData(DeviceContext context, String data) {
        try {
            log.debug("处理完整数据: 上下文={}, 数据={}", context.getContextId(), data);

            // 1. 数据验证
            if (!validateData(context, data)) {
                log.warn("数据验证失败: 上下文={}, 数据={}", context.getContextId(), data);
                return false;
            }

            // 2. 创建增强的串口数据对象
            SerialPortData serialData = createEnhancedSerialPortData(context, data);

            // 3. 发送统一格式的设备响应
            sendUnifiedDeviceResponse(context, serialData);

            // 4. 更新上下文活跃时间
            context.updateActiveTime();

            log.debug("数据处理完成: 上下文={}", context.getContextId());
            return true;

        } catch (Exception e) {
            log.error("处理完整数据失败: 上下文={}", context.getContextId(), e);
            return false;
        }
    }

    /**
     * 验证数据
     */
    private boolean validateData(DeviceContext context, String data) {
        if (data == null || data.trim().isEmpty()) {
            return false;
        }

        // 可以根据设备属性添加更多验证规则
        return true;
    }

    /**
     * 创建增强的串口数据对象
     */
    private SerialPortData createEnhancedSerialPortData(DeviceContext context, String data) {
        SerialPortData serialData = new SerialPortData();

        // 基本信息
        serialData.setPort(context.getPortDescriptor());
        serialData.setData(data);
        serialData.setTimestamp(System.currentTimeMillis());
        serialData.setSessionId(context.getSessionId());
        serialData.setDeviceSessionId(context.getDeviceIdentifier());
        serialData.setDeviceModel(getDeviceModel());
        serialData.setDeviceType(getDeviceType());
        serialData.setDeviceName(context.getDeviceProperties().getDeviceName());
        serialData.setDeviceCode(context.getDeviceProperties().getDeviceCode());

        // 患者信息
        if (context.getPatientInfo() != null) {
            serialData.setPatientId(context.getPatientInfo().getExamNo());
            serialData.setPatientName(context.getPatientInfo().getName());
        }

        // 体检项目信息
        if (context.getExamItemInfo() != null) {
            serialData.setExamItemId(context.getExamItemInfo().getItemId());
            serialData.setExamItemName(context.getExamItemInfo().getItemName());
        }

        // 测量数据（通用串口设备直接使用原始数据）
        serialData.setValue(data.trim());

        // 数据状态
        serialData.setDataStatus("PROCESSED");
        serialData.setHasError(false);
        serialData.setIsComplete(true);

        // 操作信息
        serialData.setOperatorId(context.getSessionId());
        if (context.getInstitutionId() != null) {
            serialData.setInstitutionId(context.getInstitutionId());
        }
        if (context.getExamBatchNo() != null) {
            serialData.setExamBatchNo(context.getExamBatchNo());
        }

        return serialData;
    }

    /**
     * 发送统一格式的设备响应
     */
    private void sendUnifiedDeviceResponse(DeviceContext context, SerialPortData serialData) {
        try {
            // 计算测量耗时
            Long duration = calculateMeasurementDuration(context);

            // 使用适配器转换为统一响应格式
            CommonDeviceResponse deviceResponse = SerialDeviceResponseAdapter.adaptSerialPortData(serialData, context);

            // 设置耗时
            deviceResponse.setDuration(duration);

            // 发送统一格式的事件
            context.getClient().sendEvent(EventConstants.TEST_RESULT, deviceResponse.toMap());

            log.debug("发送统一设备响应: 设备={}, 用户={}, 数据={}", context.getDeviceModel(), context.getSessionId(),deviceResponse.toMap());
        } catch (Exception e) {
            log.error("发送统一设备响应失败", e);
        }
    }

    /**
     * 计算测量耗时
     */
    private Long calculateMeasurementDuration(DeviceContext context) {
        if (context.getMeasurementStartTime() != null) {
            return System.currentTimeMillis() - context.getMeasurementStartTime();
        }
        return 0L;
    }

    @Override
    public boolean sendCommand(DeviceContext context, String command) {
        SocketIOClient client = context.getClient();
        String portDescriptor = context.getPortDescriptor();
        String userId = context.getSessionId();
        try {
            log.info("通用设备发送指令开始: 端口={}, 指令={}, 用户={}", portDescriptor, command, userId);

            if (command == null || command.trim().isEmpty()) {
                log.warn("指令为空，忽略发送");
                return false;
            }

            // 1. 检查设备状态
            String deviceStatus = deviceStatusMap.get(portDescriptor);
            if (!"CONNECTED".equals(deviceStatus)) {
                log.warn("设备未连接，无法发送指令: 端口={}, 状态={}", portDescriptor, deviceStatus);
                return false;
            }

            // 2. 构建串口指令
            String serialCommand = buildSerialCommand(command);

            // 3. 发送串口指令事件
            java.util.Map<String, Object> commandData = new java.util.HashMap<>();
            commandData.put("port", portDescriptor);
            commandData.put("command", serialCommand);
            commandData.put("userId", userId);
            commandData.put("timestamp", System.currentTimeMillis());

            client.sendEvent("sendSerialCommand", commandData);

            log.info("通用设备指令发送成功: 端口={}, 指令={}", portDescriptor, serialCommand);
            return true;

        } catch (Exception e) {
            log.error("通用设备指令发送失败: 端口={}, 指令={}", portDescriptor, command, e);
            return false;
        }
    }

    /**
     * 构建串口指令
     */
    private String buildSerialCommand(String command) {
        // 通用的指令构建逻辑
        // 可以根据需要添加特定的格式化规则

        if (command.endsWith("\n") || command.endsWith("\r\n")) {
            return command;
        }

        // 默认添加换行符
        return command + "\r\n";
    }

    @Override
    public String getDeviceStatus(DeviceContext context) {
        String portDescriptor = context.getPortDescriptor();
        return deviceStatusMap.getOrDefault(portDescriptor, "UNKNOWN");
    }

    @Override
    public boolean resetDevice(DeviceContext context) {
        try {
            String portDescriptor = context.getPortDescriptor();
            String userId = context.getSessionId();
            log.info("重置通用设备: 端口={}, 用户={}", portDescriptor, userId);

            // 1. 清理设备资源
            cleanupDeviceResources(portDescriptor);

            // 2. 重新初始化
            deviceStatusMap.put(portDescriptor, "RESETTING");
            dataBufferMap.put(portDescriptor, new StringBuilder());
            bufferTimeoutMap.put(portDescriptor, System.currentTimeMillis());

            // 3. 发送重置指令
            boolean result = sendCommand(context, "RESET");

            if (result) {
                deviceStatusMap.put(portDescriptor, "CONNECTED");
                log.info("通用设备重置成功: 端口={}", portDescriptor);
            } else {
                deviceStatusMap.put(portDescriptor, "ERROR");
                log.error("通用设备重置失败: 端口={}", portDescriptor);
            }

            return result;

        } catch (Exception e) {
            log.error("重置通用设备失败: 端口={}", context.getPortDescriptor(), e);
            deviceStatusMap.put(context.getPortDescriptor(), "ERROR");
            return false;
        }
    }

    @Override
    public boolean calibrateDevice(DeviceContext context) {
        try {
            String portDescriptor = context.getPortDescriptor();
            String userId = context.getSessionId();
            log.info("校准通用设备: 端口={}, 用户={}", portDescriptor, userId);

            // 发送校准指令
            boolean result = sendCommand(context, "CALIBRATE");

            if (result) {
                log.info("通用设备校准指令发送成功: 端口={}", portDescriptor);
            } else {
                log.error("通用设备校准指令发送失败: 端口={}", portDescriptor);
            }

            return result;

        } catch (Exception e) {
            log.error("校准通用设备失败: 端口={}", context.getPortDescriptor(), e);
            return false;
        }
    }

    @Override
    public boolean supportsFeature(String feature) {
        // 通用设备处理器支持的功能
        return switch (feature) {
            case "DATA_BUFFERING" -> true;
            case "COMMAND_SENDING" -> true;
            case "STATUS_MONITORING" -> true;
            case "DEVICE_RESET" -> true;
            case "DEVICE_CALIBRATION" -> true;
            case "QUERY_RESULTS" -> false; // 通用处理器不支持查询功能，需要专有处理器实现
            default -> false;
        };
    }

    @Override
    public void initialize() {
        log.info("初始化通用设备处理器");
        // 清理所有映射
        deviceStatusMap.clear();
        connectionTimeMap.clear();
        dataBufferMap.clear();
        bufferTimeoutMap.clear();
    }

    @Override
    public void destroy() {
        log.info("销毁通用设备处理器");
        // 清理所有资源
        for (String portDescriptor : deviceStatusMap.keySet()) {
            cleanupDeviceResources(portDescriptor);
        }
        deviceStatusMap.clear();
        connectionTimeMap.clear();
        dataBufferMap.clear();
        bufferTimeoutMap.clear();
    }

    /**
     * 清理设备资源
     */
    private void cleanupDeviceResources(String portDescriptor) {
        try {
            log.debug("清理设备资源: 端口={}", portDescriptor);

            // 1. 强制关闭串口连接
            forceCloseSerialPort(portDescriptor);

            // 2. 清理数据缓冲区
            dataBufferMap.remove(portDescriptor);
            bufferTimeoutMap.remove(portDescriptor);
            connectionTimeMap.remove(portDescriptor);

            log.debug("设备资源清理完成: 端口={}", portDescriptor);

        } catch (Exception e) {
            log.error("清理设备资源失败: 端口={}", portDescriptor, e);
        }
    }

    /**
     * 执行会话切换：踢掉原会话，让新会话使用串口
     */
    private boolean switchPortSessionForGeneric(String portDescriptor, SerialPortResourceManager.SessionInfo oldSession, String newUserId, String newPatientId, String newPatientName, DeviceContext newContext) {
        try {
            String oldUserId = oldSession != null ? oldSession.getUserId() : "未知用户";
            String oldPatientName = oldSession != null ? oldSession.getPatientName() : "未知患者";

            log.info("开始执行通用设备会话切换: 端口={}, 原会话={}:{}, 新会话={}:{}", portDescriptor, oldUserId, oldPatientName, newUserId, newPatientName);

            // 1. 发布用户被踢掉事件
            eventPublisher.publishEvent(UserSwitchEvent.createUserKickedEvent(this, portDescriptor, oldUserId, oldPatientName, newUserId, newPatientName, String.format("患者切换: %s -> %s", oldPatientName, newPatientName)));

            // 2. 强制清理原用户的串口资源
            serialPortResourceManager.forceReleasePort(portDescriptor);

            // 3. 为新会话创建串口连接
            log.debug("为新会话创建串口连接: 端口={}, 新会话={}:{}", portDescriptor, newUserId, newPatientName);

            com.fazecast.jSerialComm.SerialPort serialPort = com.fazecast.jSerialComm.SerialPort.getCommPort(portDescriptor);

            // 从设备属性中获取串口参数
            DeviceProperties deviceProperties = newContext.getDeviceProperties();
            int baudRate = getSerialBaudRate(deviceProperties);
            int dataBits = getSerialDataBits(deviceProperties);
            int stopBits = getSerialStopBits(deviceProperties);
            int parity = getSerialParity(deviceProperties);

            // 设置串口参数
            serialPort.setBaudRate(baudRate);
            serialPort.setNumDataBits(dataBits);
            serialPort.setNumStopBits(stopBits);
            serialPort.setParity(parity);

            if (serialPort.openPort()) {
                log.debug("新会话串口打开成功: 端口={}", portDescriptor);

                // 4. 注册新会话的串口使用
                boolean registered = serialPortResourceManager.registerPortUsage(portDescriptor, newUserId, newPatientId, newPatientName, serialPort);
                if (!registered) {
                    log.error("注册新会话串口使用失败: 端口={}, 新会话={}:{}", portDescriptor, newUserId, newPatientName);
                    serialPort.closePort();
                    return false;
                }

                // 5. 添加数据监听器
                addDataListenerForGeneric(serialPort, newContext);

                // 6. 发布切换成功事件
                String switchReason = String.format("患者切换: %s -> %s", oldPatientName, newPatientName);
                eventPublisher.publishEvent(UserSwitchEvent.createSwitchSuccessEvent(this, portDescriptor, oldUserId, oldPatientName, newUserId, newPatientName, switchReason));

                // 7. 发布广播事件
                eventPublisher.publishEvent(UserSwitchEvent.createBroadcastEvent(this, portDescriptor, oldUserId, oldPatientName, newUserId, newPatientName, switchReason));

                log.info("通用设备会话切换完成: 端口={}, 原会话={}:{}, 新会话={}:{}", portDescriptor, oldUserId, oldPatientName, newUserId, newPatientName);
                return true;

            } else {
                log.error("新会话串口打开失败: 端口={}, 新会话={}:{}", portDescriptor, newUserId, newPatientName);
                return false;
            }

        } catch (Exception e) {
            String oldUserId = oldSession != null ? oldSession.getUserId() : "未知用户";
            String oldPatientName = oldSession != null ? oldSession.getPatientName() : "未知患者";
            log.error("通用设备会话切换失败: 端口={}, 原会话={}:{}, 新会话={}:{}", portDescriptor, oldUserId, oldPatientName, newUserId, newPatientName, e);
            return false;
        }
    }

    /**
     * 强制关闭串口连接
     */
    private void forceCloseSerialPort(String portDescriptor) {
        if (portDescriptor == null || portDescriptor.trim().isEmpty()) {
            return;
        }

        try {
            log.debug("强制关闭串口: 端口={}", portDescriptor);

            // 获取串口实例并关闭
            com.fazecast.jSerialComm.SerialPort serialPort = com.fazecast.jSerialComm.SerialPort.getCommPort(portDescriptor);
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.closePort();
                log.info("强制关闭串口成功: 端口={}", portDescriptor);
            } else {
                log.debug("串口未打开或不存在: 端口={}", portDescriptor);
            }

        } catch (Exception e) {
            log.warn("强制关闭串口失败: 端口={}", portDescriptor, e);
        }
    }


}
