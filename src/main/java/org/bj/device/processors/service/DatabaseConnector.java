package org.bj.device.processors.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.bj.config.DatabaseConfig;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 通用数据库连接器
 * 
 * 支持连接体检主数据库（MySQL）和设备数据库（Access等）
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
@Component
public class DatabaseConnector {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConnector.class);
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    @Autowired(required = false)
    @Qualifier("mainDataSource")
    private DataSource mainDataSource;
    
    /**
     * 获取体检主数据库连接
     * 
     * @return 主数据库连接
     * @throws SQLException 连接失败时抛出异常
     */
    public Connection getMainDatabaseConnection() throws SQLException {
        if (mainDataSource != null) {
            Connection connection = mainDataSource.getConnection();
            logger.debug("获取主数据库连接成功");
            return connection;
        } else {
            throw new SQLException("主数据库数据源未配置");
        }
    }
    
    /**
     * 获取Access设备数据库连接
     * 
     * @param databasePath 数据库文件路径
     * @return Access数据库连接
     * @throws SQLException 连接失败时抛出异常
     */
    public Connection getAccessDatabaseConnection(String databasePath) throws SQLException {
        return AccessDatabaseConnector.createConnection(databasePath);
    }
    
    /**
     * 测试主数据库连接
     * 
     * @return 连接是否成功
     */
    public boolean testMainDatabaseConnection() {
        try (Connection connection = getMainDatabaseConnection()) {
            boolean isValid = connection.isValid(5); // 5秒超时
            logger.info("主数据库连接测试: {}", isValid ? "成功" : "失败");
            return isValid;
        } catch (SQLException e) {
            logger.error("主数据库连接测试失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试Access数据库连接
     * 
     * @param databasePath 数据库文件路径
     * @return 连接是否成功
     */
    public boolean testAccessDatabaseConnection(String databasePath) {
        return AccessDatabaseConnector.testConnection(databasePath);
    }
    
    /**
     * 获取主数据库连接信息
     */
    public String getMainDatabaseInfo() {
        try (Connection connection = getMainDatabaseConnection()) {
            return String.format("数据库产品: %s, 版本: %s, URL: %s", 
                connection.getMetaData().getDatabaseProductName(),
                connection.getMetaData().getDatabaseProductVersion(),
                connection.getMetaData().getURL());
        } catch (SQLException e) {
            return "无法获取主数据库信息: " + e.getMessage();
        }
    }
    
    /**
     * 获取Access数据库连接信息
     */
    public String getAccessDatabaseInfo(String databasePath) {
        return AccessDatabaseConnector.getConnectionInfo(databasePath);
    }
    
    /**
     * 检查数据库配置
     */
    public boolean isDatabaseConfigured() {
        return databaseConfig != null && 
               databaseConfig.getMain() != null && 
               databaseConfig.getMain().isEnabled();
    }
    
    /**
     * 获取数据库配置
     */
    public DatabaseConfig getDatabaseConfig() {
        return databaseConfig;
    }
}
