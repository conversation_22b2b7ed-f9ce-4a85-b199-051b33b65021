package org.bj.device.processors.service;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.bj.common.concurrent.PatientLockManager;
import org.bj.config.AudiometryProperties;
import org.bj.device.audiometry.entity.ElectricalAudiometryResult;
import org.bj.device.audiometry.entity.FrequencyData;

import org.bj.device.entity.ExamItemInfo;
import org.bj.device.entity.PatientInfo;
import org.bj.device.util.SnowflakeIdGenerator;
import org.bj.service.AudiometryReportImageService;
import org.bj.service.HearingTestExpressionService;
import org.bj.util.FileUploadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

/**
 * 电测听数据保存服务
 * 保存电测听测量数据到主体检数据库
 *
 * <AUTHOR> Agent
 * @date 2025-07-09
 */
@Slf4j
@Service
@Profile("audiometry")
public class AudiometryService {

    @Autowired
    @Qualifier("mainJdbcTemplate")
    private JdbcTemplate mainJdbcTemplate;

    @Autowired
    private AudiometryProperties audiometryProperties;

    @Autowired(required = false)
    private FileUploadUtil fileUploadUtil;

    @Autowired
    private HearingTestExpressionService expressionService;

    @Autowired
    private PatientLockManager patientLockManager;

    @Autowired
    private AudiometryReportImageService reportImageService;

    /**
     * 保存电测听结果到主数据库
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAudiometryResult(PatientInfo patientInfo, ExamItemInfo examItemInfo,
                                        ElectricalAudiometryResult hearingTestResult, String deviceModel, String userId) throws JsonProcessingException {

        String examNo = patientInfo.getExamNo();
        log.info("开始保存电测听结果: 患者={}, 体检号={}", patientInfo.getName(), examNo);
        log.info("hearingTestResult={}", new ObjectMapper().writeValueAsString(hearingTestResult));
        // 使用患者级别的并发控制，防止死锁
        return patientLockManager.executeWithPatientLock(examNo, () -> {
            return saveAudiometryResultInternal(patientInfo, examItemInfo, hearingTestResult, deviceModel, userId);
        }, 60); // 60秒超时
    }

    /**
     * 内部保存方法，在患者锁保护下执行
     */
    private boolean saveAudiometryResultInternal(PatientInfo patientInfo, ExamItemInfo examItemInfo,
                                                 ElectricalAudiometryResult hearingTestResult, String deviceModel, String userId) {
        try {
            String examNo = patientInfo.getExamNo();
            log.info("开始保存电测听结果到主数据库: 患者={}, 体检号={}", patientInfo.getName(), examNo);

            // 1. 保存电测听测量结果
            boolean resultSaved = saveAudiometryItemResults(patientInfo, examItemInfo, hearingTestResult, deviceModel, userId);

            // 2. 更新项目组状态（不包含报告图片URL，将异步处理）
            updateCustomerRegItemGroupStatus(examItemInfo, null);

            // 3. 添加报告图片处理任务（异步处理）
            reportImageService.addReportImageTask(examNo, examItemInfo.getCustomerRegItemGroupId(),
                    patientInfo, examItemInfo, hearingTestResult, deviceModel);

            log.info("电测听结果保存到主数据库成功: 患者={}, 体检号={}, 报告图片将异步处理", patientInfo.getName(), examNo);
            return resultSaved;
        } catch (Exception e) {
            log.error("保存电测听结果到主数据库失败: 患者={}", patientInfo.getName(), e);
            throw new RuntimeException("保存电测听结果失败", e);
        }
    }

    /**
     * 保存原始TonePoint数据到audiometry_raw_data表
     * 供体检主程序生成PDF报告使用
     */
    public boolean saveRawAudiometryData(PatientInfo patientInfo, ExamItemInfo examItemInfo,
                                         ElectricalAudiometryResult rawData, String deviceModel, String userId) {
        try {
            String examNo = patientInfo.getExamNo();
            log.info("开始保存电测听原始数据: 患者={}, 体检号={}", patientInfo.getName(), examNo);

            // 先删除已有的原始数据
            String deleteSql = """
                    DELETE FROM audiometry_raw_data
                    WHERE exam_no = ? AND customer_reg_id = ?
                    """;
            int deletedRows = mainJdbcTemplate.update(deleteSql, examNo, patientInfo.getCustomerRegId());
            log.debug("删除已有电测听原始数据: 体检号={}, 删除行数={}", examNo, deletedRows);

            // 将原始数据转换为JSON格式
            //String rawDataJson = convertRawDataToJson(rawData);
            String rawDataJson = new ObjectMapper().writeValueAsString(rawData);

            // 插入新的原始数据
            String insertSql = """
                    INSERT INTO audiometry_raw_data (
                        id, exam_no, customer_reg_id, customer_reg_item_group_id,
                        patient_name, patient_id_card, device_model,
                        raw_data_json, test_date, create_time, update_time,
                        created_by, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """;

            LocalDateTime now = LocalDateTime.now();
            String recordId = UUID.randomUUID().toString();

            mainJdbcTemplate.update(insertSql,
                    recordId,                                    // id
                    examNo,                                      // exam_no
                    patientInfo.getCustomerRegId(),             // customer_reg_id
                    examItemInfo.getCustomerRegItemGroupId(),   // customer_reg_item_group_id
                    patientInfo.getName(),                      // patient_name
                    patientInfo.getIdCard(),                    // patient_id_card
                    deviceModel,                                // device_model
                    rawDataJson,                                // raw_data_json
                    now.toLocalDate(),                          // test_date
                    now,                                        // create_time
                    now,                                        // update_time
                    userId,                                     // created_by
                    "PENDING_PDF"                               // status (待生成PDF)
            );

            log.info("电测听原始数据保存成功: 患者={}, 体检号={}, 记录ID={}", patientInfo.getName(), examNo, recordId);
            return true;

        } catch (Exception e) {
            log.error("保存电测听原始数据失败: 患者={}", patientInfo.getName(), e);
            return false;
        }
    }

    /**
     * 将原始TonePoint数据转换为JSON格式
     */
    private String convertRawDataToJson(Map<String, List<org.bj.device.audiometry.entity.TonePoint>> rawData) {
        try {
            // 使用简单的JSON格式，避免复杂的序列化依赖
            StringBuilder json = new StringBuilder();
            json.append("{");

            boolean first = true;
            for (Map.Entry<String, List<org.bj.device.audiometry.entity.TonePoint>> entry : rawData.entrySet()) {
                if (!first) {
                    json.append(",");
                }
                first = false;

                json.append("\"").append(entry.getKey()).append("\":[");

                boolean firstPoint = true;
                for (org.bj.device.audiometry.entity.TonePoint point : entry.getValue()) {
                    if (!firstPoint) {
                        json.append(",");
                    }
                    firstPoint = false;

                    json.append("{");
                    json.append("\"frequency\":").append(point.getFrequency()).append(",");
                    json.append("\"intensityMT\":").append(point.getIntensityMT()).append(",");
                    json.append("\"intensityMTMasked\":").append(point.getIntensityMTMasked()).append(",");
                    json.append("\"statusMT\":\"").append(point.getStatusMT() != null ? point.getStatusMT() : "").append("\",");
                    json.append("\"intensityUT\":").append(point.getIntensityUT()).append(",");
                    json.append("\"statusUT\":\"").append(point.getStatusUT() != null ? point.getStatusUT() : "").append("\",");
                    json.append("\"comment\":\"").append(point.getComment() != null ? point.getComment() : "").append("\",");
                    json.append("\"transducer\":\"").append(point.getTransducer() != null ? point.getTransducer() : "").append("\"");
                    json.append("}");
                }

                json.append("]");
            }

            json.append("}");
            return json.toString();

        } catch (Exception e) {
            log.error("转换原始数据为JSON失败", e);
            return "{}";
        }
    }

    /**
     * 保存电测听测量结果详情
     */
    private boolean saveAudiometryItemResults(PatientInfo patientInfo, ExamItemInfo examItemInfo,
                                              ElectricalAudiometryResult hearingTestResult, String deviceModel, String userId) {
        try {
            String examNo = patientInfo.getExamNo();
            String checkBillNo = "AUDIOMETRY_" + examNo;
            LocalDateTime now = LocalDateTime.now();

            log.debug("开始保存电测听测量结果详情: 体检号={}", examNo);

            // 1. 先删除已有结果（减少锁持有时间）
            String deleteSql = """
                    DELETE FROM customer_reg_item_result 
                    WHERE customer_reg_id = ? AND item_group_id = ?
                    """;
            int deletedRows = mainJdbcTemplate.update(deleteSql, patientInfo.getCustomerRegId(), examItemInfo.getItemId());
            log.debug("删除已有电测听结果: 体检号={}, 删除行数={}", examNo, deletedRows);

            // 2. 准备批量插入数据
            String insertSql = """
                        INSERT INTO customer_reg_item_result (
                             id,archives_num, exam_no, id_card, customer_reg_id, check_bill_no, sort_no,item_group_id, item_group_name, group_his_code,
                             group_his_name,item_id, item_name, item_code, item_his_code, item_his_name,value, unit, value_type, abnormal_flag, 
                             abnormal_flag_desc, abnormal_symbol,value_ref_range, value_indicator, value_source,instrument, check_purpose, doctor_id, doctor_name,check_department_name, 
                             check_department_code, subject_class,check_conclusion, critical_flag, critical_degree, department_id,create_time, update_time
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                                  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                                  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                                  ?, ?, ?, ?, ?, ?, ?, ?);
                    """;

            // 3. 查询item_info表获取指标信息
            String itemQuerySql = """
                    SELECT
                        id,
                        name,
                        code,
                        his_code,
                        his_name,
                        unit,
                        judge_flag,
                        sort
                    FROM
                        item_info
                    WHERE
                        his_name IN (
                        '左耳500Hz（气导）',
                        '左耳1000Hz（气导）',
                        '左耳2000Hz（气导）',
                        '左耳3000Hz（气导）',
                        '左耳4000Hz（气导）',
                        '左耳6000Hz（气导）',
                        '左耳语频平均听阈（气导）',
                        '左耳500Hz（骨导）',
                        '左耳1000Hz（骨导）',
                        '左耳2000Hz（骨导）',
                        '左耳3000Hz（骨导）',
                        '左耳4000Hz（骨导）',
                        '左耳6000Hz（骨导）',
                        '左耳语频平均听阈（骨导）',
                        '右耳500Hz（气导）',
                        '右耳1000Hz（气导）',
                        '右耳2000Hz（气导）',
                        '右耳3000Hz（气导）',
                        '右耳4000Hz（气导）',
                        '右耳6000Hz（气导）',
                        '右耳语频平均听阈（气导）',
                        '右耳500Hz（骨导）',
                        '右耳1000Hz（骨导）',
                        '右耳2000Hz（骨导）',
                        '右耳3000Hz（骨导）',
                        '右耳4000Hz（骨导）',
                        '右耳6000Hz（骨导）',
                        '右耳语频平均听阈（骨导）',
                        '双耳高频平均听阈（骨导）',
                        '双耳语频平均听阈（骨导）',
                        '双耳高频平均听阈（气导）',
                        '双耳语频平均听阈（气导）')
                    ORDER BY
                        sort;
                    """;

            List<Map<String, Object>> itemInfoList = mainJdbcTemplate.queryForList(itemQuerySql);
            log.debug("查询到电测听指标信息: 数量={}", itemInfoList.size());

            // 4. 处理每个指标
            int sortNo = 1;
            for (Map<String, Object> itemInfo : itemInfoList) {
                String itemId = itemInfo.get("id").toString();
                String itemName = (String) itemInfo.get("name");
                String itemCode = (String) itemInfo.get("code");
                String itemHisCode = (String) itemInfo.get("his_code");
                String itemHisName = (String) itemInfo.get("his_name");
                String unit = (String) itemInfo.get("unit");
                String refRange = String.valueOf(itemInfo.get("judge_flag"));

                // 根据his_name匹配数据
                Object value = getValueByIndicatorName(itemHisName, hearingTestResult);
                if (value == null) {
                    log.debug("指标 {} 无对应数据，跳过", itemHisName);
                    continue;
                }

                // 计算异常标志和描述
                Map<String, Object> abnormalInfo = expressionService.evaluateAbnormalRules(
                        itemHisName, value, hearingTestResult, audiometryProperties.getHearingTest().getIndicators());

                String abnormalFlag = (String) abnormalInfo.get("flag");
                String abnormalDesc = (String) abnormalInfo.get("description");
                String abnormalSymbol = (String) abnormalInfo.get("symbol");
                String id = new SnowflakeIdGenerator(0,0).nextIdStr();
                try {
                    String sql = "SELECT id FROM customer_reg_item_result where customer_reg_id = ? AND item_group_id = ? and item_id = ? limit 1";
                    List<String> query = mainJdbcTemplate.query(sql, new BeanPropertyRowMapper<>(String.class), patientInfo.getCustomerRegId(), examItemInfo.getItemId(), itemId);
                    log.info("携带信息result：{}",new ObjectMapper().writeValueAsString(query));
                    if (CollectionUtil.isNotEmpty(query)) {
                        id = query.get(0);
                    }
                } catch (DataAccessException e) {
                    log.info("结果表中没有该细项需要新增数据主键：{}",id);
                }
                log.info("大项id：{}",examItemInfo.getCustomerRegItemGroupId());
                log.info("携带信息 abnormalInfo：{}",new ObjectMapper().writeValueAsString(examItemInfo));
                log.info("携带信息 patientInfo：{}",new ObjectMapper().writeValueAsString(patientInfo));
                // 保存到数据库
                mainJdbcTemplate.update(insertSql,
                        id,           // id
                        patientInfo.getArchivesNum(),        // archives_num
                        patientInfo.getExamNo(),             // exam_no
                        patientInfo.getIdCard(),             // id_card
                        patientInfo.getCustomerRegId(),                  // customer_reg_id
                        checkBillNo,                            // check_bill_no
                        sortNo,                                 // sort_no
                        examItemInfo.getItemId(),         // item_group_id
                        examItemInfo.getItemName(), // item_group_name
                        examItemInfo.getHisCode(),   // group_his_code
                        examItemInfo.getHisName(),   // group_his_name
                        itemId,                                 // item_id (from item_info table)
                        itemName,                               // item_name
                        itemCode,                               // item_code
                        itemHisCode,                            // item_his_code
                        itemHisName,                            // item_his_name
                        value.toString(),                       // value
                        unit,                                   // unit
                        "数值型",                                 // value_type
                        abnormalFlag,                           // abnormal_flag
                        abnormalDesc,                           // abnormal_flag_desc
                        abnormalSymbol,                         // abnormal_symbol
                        refRange,                               // value_ref_range
                        "",                                     // value_indicator
                        deviceModel,                            // value_source
                        deviceModel,                            // instrument
                        "电测听检查",                           // check_purpose
                        userId,                                 // doctor_id
                        "电测听检查医生",                       // doctor_name
                        "体检科",                               // check_department_name
                        "EXAM_DEPT",                            // check_department_code
                        "电测听",                               // subject_class
                        "",                                     // check_conclusion
                        null,                                    // critical_flag
                        "",                                     // critical_degree
                        "EXAM_DEPT_ID",                         // department_id
                        now,                                    // create_time
                        now                                // update_time
                );

                sortNo++;
            }

            log.info("电测听测量结果详情保存完成: 体检号={}, 保存指标数量={}", examNo, sortNo - 1);
            return true;

        } catch (Exception e) {
            log.error("保存电测听测量结果详情失败", e);
            return false;
        }
    }

    /**
     * 根据指标名称获取对应的数据值
     */
    private Object getValueByIndicatorName(String indicatorName, ElectricalAudiometryResult hearingTestResult) {
        return switch (indicatorName) {
            // 左耳气导相关
            case "左耳500Hz（气导）" -> getThreshold(hearingTestResult.getLeftEarAirData(), FrequencyData::getHearingThreshold500Hz);
            case "左耳1000Hz（气导）" -> getThreshold(hearingTestResult.getLeftEarAirData(), FrequencyData::getHearingThreshold1000Hz);
            case "左耳2000Hz（气导）" -> getThreshold(hearingTestResult.getLeftEarAirData(), FrequencyData::getHearingThreshold2000Hz);
            case "左耳3000Hz（气导）" -> getThreshold(hearingTestResult.getLeftEarAirData(), FrequencyData::getHearingThreshold3000Hz);
            case "左耳4000Hz（气导）" -> getThreshold(hearingTestResult.getLeftEarAirData(), FrequencyData::getHearingThreshold4000Hz);
            case "左耳6000Hz（气导）" -> getThreshold(hearingTestResult.getLeftEarAirData(), FrequencyData::getHearingThreshold6000Hz);
            case "左耳语频平均听阈（气导）" -> hearingTestResult.getLeftWhisperFrequencyAirAvg();

            // 左耳骨导相关
            case "左耳500Hz（骨导）" -> getThreshold(hearingTestResult.getLeftEarBoneData(), FrequencyData::getHearingThreshold500Hz);
            case "左耳1000Hz（骨导）" -> getThreshold(hearingTestResult.getLeftEarBoneData(), FrequencyData::getHearingThreshold1000Hz);
            case "左耳2000Hz（骨导）" -> getThreshold(hearingTestResult.getLeftEarBoneData(), FrequencyData::getHearingThreshold2000Hz);
            case "左耳3000Hz（骨导）" -> getThreshold(hearingTestResult.getLeftEarBoneData(), FrequencyData::getHearingThreshold3000Hz);
            case "左耳4000Hz（骨导）" -> getThreshold(hearingTestResult.getLeftEarBoneData(), FrequencyData::getHearingThreshold4000Hz);
            case "左耳6000Hz（骨导）" -> getThreshold(hearingTestResult.getLeftEarBoneData(), FrequencyData::getHearingThreshold6000Hz);
            case "左耳语频平均听阈（骨导）" -> hearingTestResult.getLeftWhisperFrequencyBoneAvg();

            // 右耳气导相关
            case "右耳500Hz（气导）" -> getThreshold(hearingTestResult.getRightEarAirData(), FrequencyData::getHearingThreshold500Hz);
            case "右耳1000Hz（气导）" -> getThreshold(hearingTestResult.getRightEarAirData(), FrequencyData::getHearingThreshold1000Hz);
            case "右耳2000Hz（气导）" -> getThreshold(hearingTestResult.getRightEarAirData(), FrequencyData::getHearingThreshold2000Hz);
            case "右耳3000Hz（气导）" -> getThreshold(hearingTestResult.getRightEarAirData(), FrequencyData::getHearingThreshold3000Hz);
            case "右耳4000Hz（气导）" -> getThreshold(hearingTestResult.getRightEarAirData(), FrequencyData::getHearingThreshold4000Hz);
            case "右耳6000Hz（气导）" -> getThreshold(hearingTestResult.getRightEarAirData(), FrequencyData::getHearingThreshold6000Hz);
            case "右耳语频平均听阈（气导）" -> hearingTestResult.getRightWhisperFrequencyAirAvg();

            // 右耳骨导相关（重点优化：通过通用方法处理空值）
            case "右耳500Hz（骨导）" -> getThreshold(hearingTestResult.getRightEarBoneData(), FrequencyData::getHearingThreshold500Hz);
            case "右耳1000Hz（骨导）" -> getThreshold(hearingTestResult.getRightEarBoneData(), FrequencyData::getHearingThreshold1000Hz);
            case "右耳2000Hz（骨导）" -> getThreshold(hearingTestResult.getRightEarBoneData(), FrequencyData::getHearingThreshold2000Hz);
            case "右耳3000Hz（骨导）" -> getThreshold(hearingTestResult.getRightEarBoneData(), FrequencyData::getHearingThreshold3000Hz);
            case "右耳4000Hz（骨导）" -> getThreshold(hearingTestResult.getRightEarBoneData(), FrequencyData::getHearingThreshold4000Hz);
            case "右耳6000Hz（骨导）" -> getThreshold(hearingTestResult.getRightEarBoneData(), FrequencyData::getHearingThreshold6000Hz);
            case "右耳语频平均听阈（骨导）" -> hearingTestResult.getRightWhisperFrequencyBoneAvg();

            // 双耳相关
            case "双耳高频平均听阈（骨导）" -> hearingTestResult.getBothHighFrequencyBoneAvg();
            case "双耳语频平均听阈（骨导）" -> hearingTestResult.getBothWhisperFrequencyBoneAvg();
            case "双耳高频平均听阈（气导）" -> hearingTestResult.getBothHighFrequencyAirAvg();
            case "双耳语频平均听阈（气导）" -> hearingTestResult.getBothWhisperFrequencyAirAvg();

            default -> null;
        };
    }

    /**
     * 通用工具方法：获取频率阈值（若数据对象为null则返回null）
     * @param data 频率数据对象（可能为null）
     * @param thresholdGetter 从数据对象中获取阈值的函数
     * @return 阈值（对象为null时返回null）
     */
    private Object getThreshold(FrequencyData data, Function<FrequencyData, Object> thresholdGetter) {
        // 如果数据对象为null，直接返回null；否则调用函数获取阈值
        return data != null ? thresholdGetter.apply(data) : null;
    }

    /**
     * 更新体检项目组状态
     */
    private void updateCustomerRegItemGroupStatus(ExamItemInfo examItemInfo, String reportImageUrl) {
        try {
            String customerRegItemGroupId = examItemInfo.getCustomerRegItemGroupId();
            LocalDateTime now = LocalDateTime.now();

            // 获取医生配置
            AudiometryProperties.DoctorConfig.Doctor checkDoctor = audiometryProperties.getDoctorConfig().getCheckDoctor();
            AudiometryProperties.DoctorConfig.Doctor reportDoctor = audiometryProperties.getDoctorConfig().getReportDoctor();
            AudiometryProperties.DoctorConfig.Doctor auditDoctor = audiometryProperties.getDoctorConfig().getAuditDoctor();

            // 统一的更新SQL，始终包含report_pics字段
            String updateSql = """
                    UPDATE customer_reg_item_group
                    SET check_status = '已检',
                        check_time = ?,
                        check_doctor_code = ?,
                        check_doctor_name = ?,
                        check_doctor_sign_pic = ?,
                        report_time = ?,
                        report_doctor_code = ?,
                        report_doctor_name = ?,
                        report_doctor_sign_pic = ?,
                        audit_time = ?,
                        audit_doctor_code = ?,
                        audit_doctor_name = ?,
                        audit_doctor_sign_pic = ?,
                        report_pics = CASE
                            WHEN ? IS NOT NULL AND ? != '' THEN JSON_ARRAY(?)
                            ELSE report_pics
                        END,
                        update_time = ?
                    WHERE id = ?
                    """;

            mainJdbcTemplate.update(updateSql, now, // check_time
                    checkDoctor.getCode(), // check_doctor_code
                    checkDoctor.getName(), // check_doctor_name
                    checkDoctor.getSignPic(), // check_doctor_sign_pic
                    now, // report_time
                    reportDoctor.getCode(), // report_doctor_code
                    reportDoctor.getName(), // report_doctor_name
                    reportDoctor.getSignPic(), // report_doctor_sign_pic
                    now, // audit_time
                    auditDoctor.getCode(), // audit_doctor_code
                    auditDoctor.getName(), // audit_doctor_name
                    auditDoctor.getSignPic(), // audit_doctor_sign_pic
                    reportImageUrl, // 第一个判断参数
                    reportImageUrl, // 第二个判断参数
                    reportImageUrl, // JSON_ARRAY中的值
                    now, // update_time
                    customerRegItemGroupId // WHERE条件
            );

            if (reportImageUrl != null && !reportImageUrl.isEmpty()) {
                log.info("体检项目组状态更新成功（含报告图片）: itemGroupId={}, URL={}", customerRegItemGroupId, reportImageUrl);
            } else {
                log.info("体检项目组状态更新成功（保持原有报告图片）: itemGroupId={}", customerRegItemGroupId);
            }

        } catch (Exception e) {
            log.error("更新体检项目组状态失败: itemGroupId={}", examItemInfo.getCustomerRegItemGroupId(), e);
        }
    }
}
