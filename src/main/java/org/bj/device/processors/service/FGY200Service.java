package org.bj.device.processors.service;

import lombok.extern.slf4j.Slf4j;
import org.bj.common.concurrent.PatientLockManager;
import org.bj.config.FGY200Properties;
import org.bj.device.entity.ExamItemInfo;
import org.bj.device.entity.PatientInfo;
import org.bj.service.FGY200ReportImageService;
import org.bj.service.LungFunctionExpressionService;
import org.bj.util.FileUploadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * FGY200 lung function data save service
 * Save lung function measurement data to main examination database
 *
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
@Slf4j
@Service
@Profile("fgy200")
public class FGY200Service {

    @Autowired
    @Qualifier("mainJdbcTemplate")
    private JdbcTemplate mainJdbcTemplate;



    @Autowired
    private FGY200Properties fgy200Properties;

    @Autowired(required = false)
    private FileUploadUtil fileUploadUtil;

    @Autowired
    private LungFunctionExpressionService expressionService;

    @Autowired
    private PatientLockManager patientLockManager;

    @Autowired
    private FGY200ReportImageService reportImageService;

    /**
     * Save lung function result to main examination database
     */
    public boolean saveLungFunctionResult(PatientInfo patientInfo, ExamItemInfo examItemInfo, Map<String, Object> lungFunctionResult, String deviceModel, String userId) {
        // Parameter validation
        if (patientInfo == null) {
            log.error("Lung function data save error: patient info is null");
            return false;
        }

        if (lungFunctionResult == null || lungFunctionResult.isEmpty()) {
            log.error("Lung function data save error: measurement result is empty");
            return false;
        }

        String examNo = patientInfo.getExamNo();
        if (examNo == null || examNo.trim().isEmpty()) {
            log.error("Lung function data save error: exam number is empty");
            return false;
        }

        // Use patient-level concurrency control to prevent deadlock
        return patientLockManager.executeWithPatientLock(examNo, () -> {
            return saveLungFunctionResultInternal(patientInfo, examItemInfo, lungFunctionResult, deviceModel, userId);
        }, 60); // 60 seconds timeout
    }

    /**
     * Internal save method, executed under patient lock protection
     */
    private boolean saveLungFunctionResultInternal(PatientInfo patientInfo, ExamItemInfo examItemInfo, Map<String, Object> lungFunctionResult, String deviceModel, String userId) {
        try {
            String examNo = patientInfo.getExamNo();
            log.info("Start saving lung function result to main database: patient={}, examNo={}", patientInfo.getName(), examNo);

            // 1. Save lung function measurement results
            boolean resultSaved = saveLungFunctionItemResults(patientInfo, examItemInfo, lungFunctionResult, deviceModel, userId);

            // 2. Update item group status (without report image URL, will be processed asynchronously)
            updateCustomerRegItemGroupStatus(examItemInfo, null);

            // 3. Add report image processing task (asynchronous processing)
            reportImageService.addReportImageTask(examNo, examItemInfo.getCustomerRegItemGroupId(), patientInfo, examItemInfo, lungFunctionResult, deviceModel);

            log.info("Lung function result saved to main database successfully: patient={}, examNo={}, report images will be processed asynchronously", patientInfo.getName(), examNo);
            return resultSaved;
        } catch (Exception e) {
            log.error("Failed to save lung function result to main database: patient={}", patientInfo.getName(), e);
            throw new RuntimeException("Failed to save lung function result", e);
        }
    }

    /**
     * Query patient registration info by exam number
     */
    public Map<String, Object> queryPatientRegByExamNo(String examNo) {
        try {
            String sql = "SELECT * FROM customer_reg WHERE exam_no = ?";
            List<Map<String, Object>> results = mainJdbcTemplate.queryForList(sql, examNo);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Failed to query patient registration info: examNo={}", examNo, e);
            return null;
        }
    }


    /**
     * Save lung function measurement result details
     */
    private boolean saveLungFunctionItemResults(PatientInfo patientInfo, ExamItemInfo examItemInfo, Map<String, Object> lungFunctionResult, String deviceModel, String userId) {
        try {
            String examNo = patientInfo.getExamNo();
            String checkBillNo = "LUNG_" + examNo;
            LocalDateTime now = LocalDateTime.now();

            log.debug("Start saving lung function measurement result details: examNo={}", examNo);

            // 1. Delete existing results first (reduce lock holding time)
            String deleteSql = """
                    DELETE FROM customer_reg_item_result 
                    WHERE customer_reg_id = ? AND item_group_id = ?
                    """;
            int deletedRows = mainJdbcTemplate.update(deleteSql, patientInfo.getCustomerRegId(), examItemInfo.getItemId());
            log.debug("Deleted existing lung function results: customer_reg_id={}, item_group_id={},deleted rows={}", patientInfo.getCustomerRegId(), examItemInfo.getItemId(), deletedRows);

            // 2. Prepare batch insert data
            String insertSql = """
                    INSERT INTO customer_reg_item_result (
                        id,
                        archives_num, exam_no, id_card, customer_reg_id, check_bill_no, sort_no,
                        item_group_id, item_group_name, group_his_code, group_his_name,
                        item_id, item_name, item_code, item_his_code, item_his_name,
                        value, unit, value_type, abnormal_flag, abnormal_flag_desc, abnormal_symbol,
                        value_ref_range, value_low, value_high, value_indicator, value_source,
                        instrument, check_purpose, doctor_id, doctor_name, 
                        check_department_name, check_department_code, subject_class,
                        check_conclusion, critical_flag, critical_degree, department_id,
                        create_time, update_time
                    ) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)
                    """;

            // 3. Collect all data to be inserted
            var indicators = fgy200Properties.getLungFunction().getIndicators();
            var sortedIndicators = indicators.stream().filter(FGY200Properties.LungFunction.Indicator::isEnabled).sorted((i1, i2) -> Integer.compare(i1.getSortOrder(), i2.getSortOrder())).toList();

            int sortNo = 1;
            int insertedCount = 0;

            // 4. Insert one by one (keep original logic but optimize error handling)
            for (var indicator : sortedIndicators) {
                Object value = lungFunctionResult.get(indicator.getDataKey());
                if (value != null && !value.toString().trim().isEmpty()) {
                    try {
                        saveLungFunctionItemWithExpression(insertSql, patientInfo, examItemInfo, checkBillNo, indicator, value, lungFunctionResult, sortNo, deviceModel, userId, now);
                        insertedCount++;
                    } catch (Exception e) {
                        log.warn("Failed to save lung function indicator, skip: indicator={}, examNo={}, error={}", indicator.getCode(), examNo, e.getMessage());
                    }
                }
                sortNo++;
            }

            log.info("Lung function measurement result details saved successfully: examNo={}, successfully inserted items={}/{}", examNo, insertedCount, sortedIndicators.size());
            return insertedCount > 0; // At least one record inserted to be considered successful

        } catch (Exception e) {
            log.error("Failed to save lung function measurement result details: examNo={}", patientInfo.getExamNo(), e);
            return false;
        }
    }

    /**
     * Query item_info table to get item information
     */
    private Map<String, Object> queryItemInfoByHisName(String hisName) {
        try {
            String sql = """
                    SELECT id, code, name, his_code, his_name, unit, item_type, normal_ref sort, department_id
                    FROM item_info 
                    WHERE his_name = ? 
                    LIMIT 1
                    """;

            List<Map<String, Object>> results = mainJdbcTemplate.queryForList(sql, hisName);
            if (!results.isEmpty()) {
                log.debug("Found item_info record: his_name={}, item_code={}", hisName, results.get(0).get("item_code"));
                return results.get(0);
            } else {
                log.debug("No item_info record found: his_name={}", hisName);
                return null;
            }

        } catch (Exception e) {
            log.warn("Failed to query item_info table: his_name={}, error={}", hisName, e.getMessage());
            return null;
        }
    }

    /**
     * Save single lung function indicator using expression calculation
     */
    private void saveLungFunctionItemWithExpression(String insertSql, PatientInfo patientInfo, ExamItemInfo examItemInfo, String checkBillNo, FGY200Properties.LungFunction.Indicator indicator, Object value, Map<String, Object> allValues, int sortNo, String deviceModel, String userId, LocalDateTime now) {
        try {
            String valueStr = value.toString();

            // Query item_info table to get item information
            Map<String, Object> itemInfo = queryItemInfoByHisName(indicator.getName());

            // Use item_info table values if exists, otherwise use configured default values
            String itemId = itemInfo != null ? (String) itemInfo.get("id") : null;
            String itemName = itemInfo != null ? (String) itemInfo.get("name") : indicator.getName();
            String itemCode = itemInfo != null ? (String) itemInfo.get("code") : indicator.getCode();
            String itemHisCode = itemInfo != null ? (String) itemInfo.get("his_code") : indicator.getCode();
            String itemHisName = itemInfo != null ? (String) itemInfo.get("his_name") : indicator.getName();
            String unit = itemInfo != null ? (String) itemInfo.get("unit") : indicator.getUnit();
            String valueType = itemInfo != null ? (String) itemInfo.get("item_type") : "数值型";
            String valueRefRange = itemInfo != null ? (String) itemInfo.get("normal_ref") : "";

            // Use expression service to calculate abnormal status (with patient info for predicted values)
            LungFunctionExpressionService.AbnormalResult abnormalResult = expressionService.calculateAbnormalStatus(indicator, value, allValues, patientInfo);

            String abnormalFlag = abnormalResult.getFlag();
            String abnormalFlagDesc = abnormalResult.getDescription();
            String abnormalSymbol = abnormalResult.getSymbol();

            if (itemInfo != null) {
                log.debug("Using item_info table data: his_name={}, item_code={}, item_name={}", itemHisName, itemCode, itemName);
            } else {
                log.debug("No item_info record found, using configured default values: his_name={}", indicator.getName());
            }

            log.debug("Indicator {} abnormal judgment result: flag={}, desc={}, symbol={}, rule={}", indicator.getCode(), abnormalFlag, abnormalFlagDesc, abnormalSymbol, abnormalResult.getRuleName());

            // Save to database
            mainJdbcTemplate.update(insertSql, UUID.randomUUID().toString(),           // id
                    patientInfo.getArchivesNum(),        // archives_num
                    patientInfo.getExamNo(),             // exam_no
                    patientInfo.getIdCard(),             // id_card
                    patientInfo.getCustomerRegId(),                  // customer_reg_id
                    checkBillNo,                            // check_bill_no
                    sortNo,                                 // sort_no
                    examItemInfo.getItemId(),         // item_group_id
                    examItemInfo.getItemName(), // item_group_name
                    examItemInfo.getHisCode(),   // group_his_code
                    examItemInfo.getHisName(),   // group_his_name
                    itemId,                                 // item_id (from item_info table)
                    itemName,                               // item_name (from item_info table)
                    itemCode,                               // item_code (from item_info table)
                    itemHisCode,                            // item_his_code (from item_info table)
                    itemHisName,                            // item_his_name (from item_info table)
                    valueStr,                               // value
                    unit,                                   // unit (from item_info table)
                    valueType,                              // value_type (from item_info table)
                    abnormalFlag,                           // abnormal_flag
                    abnormalFlagDesc,                       // abnormal_flag_desc
                    abnormalSymbol,                         // abnormal_symbol
                    valueRefRange,                          // value_ref_range (from item_info table)
                    "",                               // value_low (from item_info table)
                    "",                              // value_high (from item_info table)
                    "",                                     // value_indicator
                    "Device Detection",                             // value_source
                    deviceModel,                            // instrument
                    "Lung Function Test",                           // check_purpose
                    userId,                                 // doctor_id
                    "System Operator",                           // doctor_name
                    "Lung Function Department",                             // check_department_name
                    "LUNG_DEPT",                            // check_department_code
                    "Lung Function Test",                           // subject_class
                    abnormalFlagDesc, // check_conclusion
                    "",                                     // critical_flag
                    "",                                     // critical_degree
                    examItemInfo.getDepartmentId(),       // department_id
                    now,                                    // create_time
                    now                                     // update_time
            );

        } catch (Exception e) {
            log.error("Failed to save lung function indicator: {}", indicator.getCode(), e);
            throw e;
        }
    }

    /**
     * 更新体检项目组状态
     * <p>
     * 更新检查状态、医生信息，如果有报告图片URL则一并更新
     *
     * @param examItemInfo   项目组信息
     * @param reportImageUrl 报告图片URL，可以为null或空字符串
     */
    private void updateCustomerRegItemGroupStatus(ExamItemInfo examItemInfo, String reportImageUrl) {
        String customerRegItemGroupId = examItemInfo.getCustomerRegItemGroupId();
        try {

            LocalDateTime now = LocalDateTime.now();

            // 获取医生配置
            FGY200Properties.DoctorConfig.Doctor checkDoctor = fgy200Properties.getDoctorConfig().getCheckDoctor();
            FGY200Properties.DoctorConfig.Doctor reportDoctor = fgy200Properties.getDoctorConfig().getReportDoctor();
            FGY200Properties.DoctorConfig.Doctor auditDoctor = fgy200Properties.getDoctorConfig().getAuditDoctor();

            // 统一的更新SQL，始终包含report_pics字段
            String updateSql = """
                    UPDATE customer_reg_item_group
                    SET check_status = '已检',
                        check_time = ?,
                        check_doctor_code = ?,
                        check_doctor_name = ?,
                        check_doctor_sign_pic = ?,
                        report_time = ?,
                        report_doctor_code = ?,
                        report_doctor_name = ?,
                        report_doctor_sign_pic = ?,
                        audit_time = ?,
                        audit_doctor_code = ?,
                        audit_doctor_name = ?,
                        audit_doctor_sign_pic = ?,
                        report_pics = CASE
                            WHEN ? IS NOT NULL AND ? != '' THEN JSON_ARRAY(?)
                            ELSE report_pics
                        END,
                        update_time = ?
                    WHERE id = ?
                    """;

            mainJdbcTemplate.update(updateSql, now, // check_time
                    checkDoctor.getCode(), // check_doctor_code
                    checkDoctor.getName(), // check_doctor_name
                    checkDoctor.getSignPic(), // check_doctor_sign_pic
                    now, // report_time
                    reportDoctor.getCode(), // report_doctor_code
                    reportDoctor.getName(), // report_doctor_name
                    reportDoctor.getSignPic(), // report_doctor_sign_pic
                    now, // audit_time
                    auditDoctor.getCode(), // audit_doctor_code
                    auditDoctor.getName(), // audit_doctor_name
                    auditDoctor.getSignPic(), // audit_doctor_sign_pic
                    reportImageUrl, // 第一个判断参数
                    reportImageUrl, // 第二个判断参数
                    reportImageUrl, // JSON_ARRAY中的值
                    now, // update_time
                    customerRegItemGroupId // WHERE条件
            );

            if (reportImageUrl != null && !reportImageUrl.isEmpty()) {
                log.info("体检项目组状态更新成功（含报告图片）: itemGroupId={}, URL={}", customerRegItemGroupId, reportImageUrl);
            } else {
                log.info("体检项目组状态更新成功（保持原有报告图片）: itemGroupId={}", customerRegItemGroupId);
            }

        } catch (Exception e) {
            log.error("更新体检项目组状态失败: itemGroupId={}", customerRegItemGroupId, e);
        }
    }

    /**
     * Upload existing image files from specified directory to MinIO server
     * No longer dynamically generate images, only upload existing image files
     *
     * @return Image URL after successful upload, empty string on failure
     */
    private String generateAndUploadReportImage(PatientInfo patientInfo, Map<String, Object> lungFunctionResult, String deviceModel) {
        String examNo = patientInfo.getExamNo();

        try {
            // Build image file path (based on exam number)
            String imageDirectory = fgy200Properties.getReport().getImageDirectory();
            String imagePath = imageDirectory + "/" + examNo + ".jpg";

            // Check if image file exists
            File imageFile = new File(imagePath);
            if (!imageFile.exists()) {
                log.warn("Report image file does not exist: {}, skip upload", imagePath);
                return "";
            }

            // Upload to MinIO
            if (fileUploadUtil != null) {
                try {
                    String objectName = "lung-function/" + examNo + "/" + imageFile.getName();
                    Map<String, Object> uploadResult = fileUploadUtil.uploadToMinio(imageFile, objectName);

                    if (uploadResult != null && Boolean.TRUE.equals(uploadResult.get("success"))) {
                        String downloadUrl = (String) uploadResult.get("downloadUrl");
                        log.info("Lung function report image uploaded to MinIO successfully: examNo={}, objectName={}, fileSize={} bytes, URL={}", examNo, uploadResult.get("objectName"), imageFile.length(), downloadUrl);
                        return downloadUrl;
                    } else {
                        String errorMessage = uploadResult != null ? (String) uploadResult.get("message") : "Unknown error";
                        log.warn("Lung function report image upload to MinIO failed: examNo={}, error={}", examNo, errorMessage);
                        return "";
                    }

                } catch (Exception e) {
                    log.error("Exception occurred when uploading lung function report image to MinIO: examNo={}", examNo, e);
                    return "";
                }
            } else {
                log.warn("FileUploadUtil not configured, skip image upload");
                return "";
            }

        } catch (Exception e) {
            log.error("Exception occurred when processing lung function report image: examNo={}", examNo, e);
            return "";
        }
    }

    /**
     * Test main examination database connection
     */
    public boolean testMainDatabaseConnection() {
        try {
            mainJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            return true;

        } catch (Exception e) {
            log.error("Main examination database connection test failed", e);
            return false;
        }
    }
}
