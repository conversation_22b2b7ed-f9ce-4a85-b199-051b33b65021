package org.bj.device.processors.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.LogManager;

/**
 * Access数据库连接器
 * 
 * 专门用于连接Access数据库，并抑制UCanAccess的错误输出
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public class AccessDatabaseConnector {
    
    private static final Logger logger = LoggerFactory.getLogger(AccessDatabaseConnector.class);
    
    static {
        // 静态初始化时设置日志级别
        suppressUCanAccessLogging();
    }
    
    /**
     * 抑制UCanAccess相关的日志输出
     */
    private static void suppressUCanAccessLogging() {
        try {
            // 设置Java Util Logging级别
            java.util.logging.Logger.getLogger("net.ucanaccess").setLevel(Level.OFF);
            java.util.logging.Logger.getLogger("org.hsqldb").setLevel(Level.OFF);
            java.util.logging.Logger.getLogger("com.healthmarketscience.jackcess").setLevel(Level.OFF);
            
            // 禁用根日志记录器的某些级别
            LogManager.getLogManager().getLogger("").setLevel(Level.SEVERE);
            
        } catch (Exception e) {
            // 忽略日志配置错误
        }
    }
    
    /**
     * 创建Access数据库连接（抑制错误输出）
     * 
     * @param databasePath 数据库文件路径
     * @return 数据库连接
     * @throws SQLException 连接失败时抛出异常
     */
    public static Connection createConnection(String databasePath) throws SQLException {
        return createConnection(databasePath, true);
    }
    
    /**
     * 创建Access数据库连接
     * 
     * @param databasePath 数据库文件路径
     * @param suppressErrors 是否抑制错误输出
     * @return 数据库连接
     * @throws SQLException 连接失败时抛出异常
     */
    public static Connection createConnection(String databasePath, boolean suppressErrors) throws SQLException {
        String connectionUrl = buildConnectionUrl(databasePath);
        
        if (suppressErrors) {
            return createConnectionWithSuppressedOutput(connectionUrl);
        } else {
            return DriverManager.getConnection(connectionUrl);
        }
    }
    
    /**
     * 构建连接URL
     */
    private static String buildConnectionUrl(String databasePath) {
        return "jdbc:ucanaccess://" + databasePath + 
               ";memory=false" +
               ";lobScale=1" +
               ";showSchema=true" +
               ";sysSchema=true" +
               ";openExclusive=false" +
               ";ignoreCase=true";
    }
    
    /**
     * 创建连接并抑制所有输出
     */
    private static Connection createConnectionWithSuppressedOutput(String connectionUrl) throws SQLException {
        // 保存原始的System.err和System.out
        PrintStream originalErr = System.err;
        PrintStream originalOut = System.out;
        
        try {
            // 创建空的输出流来吞掉所有输出
            ByteArrayOutputStream suppressedOutput = new ByteArrayOutputStream();
            PrintStream suppressedStream = new PrintStream(suppressedOutput);
            
            // 重定向System.err和System.out
            System.setErr(suppressedStream);
            System.setOut(suppressedStream);
            
            // 抑制Java Util Logging
            suppressUCanAccessLogging();
            
            // 创建连接
            Connection connection = DriverManager.getConnection(connectionUrl);
            
            logger.info("Access数据库连接成功: {}", connectionUrl.substring(0, connectionUrl.indexOf(';')));
            
            return connection;
            
        } catch (SQLException e) {
            // 只有真正的连接错误才抛出异常
            if (!isUCanAccessFunctionError(e)) {
                throw e;
            }
            
            // 如果是函数加载错误，尝试重新连接
            try {
                Connection retryConnection = DriverManager.getConnection(connectionUrl);
                logger.info("Access数据库连接成功（忽略函数加载警告）");
                return retryConnection;
            } catch (SQLException retryException) {
                throw new SQLException("无法连接到Access数据库: " + retryException.getMessage(), retryException);
            }
            
        } finally {
            // 恢复原始的输出流
            System.setErr(originalErr);
            System.setOut(originalOut);
        }
    }
    
    /**
     * 判断是否是UCanAccess函数加载错误
     */
    private static boolean isUCanAccessFunctionError(SQLException e) {
        String message = e.getMessage();
        return message != null && (
            message.contains("net.ucanaccess.converters.Functions") ||
            message.contains("FunctionsAggregate") ||
            message.contains("user lacks privilege or object not found") ||
            message.contains("CLASSPATH:net.ucanaccess.converters")
        );
    }
    
    /**
     * 测试数据库连接
     * 
     * @param databasePath 数据库文件路径
     * @return 连接是否成功
     */
    public static boolean testConnection(String databasePath) {
        try (Connection connection = createConnection(databasePath)) {
            return !connection.isClosed();
        } catch (SQLException e) {
            logger.error("数据库连接测试失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取数据库连接信息
     */
    public static String getConnectionInfo(String databasePath) {
        try (Connection connection = createConnection(databasePath)) {
            return String.format("数据库产品: %s, 驱动版本: %s", 
                connection.getMetaData().getDatabaseProductName(),
                connection.getMetaData().getDriverVersion());
        } catch (SQLException e) {
            return "无法获取连接信息: " + e.getMessage();
        }
    }
}
