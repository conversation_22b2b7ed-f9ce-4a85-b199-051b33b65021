package org.bj.device.processors;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.DeviceProcessor;
import org.bj.device.entity.CommonDeviceResponse;
import org.bj.device.entity.DeviceContext;
import org.bj.device.entity.DeviceSession;
import org.bj.constants.EventConstants;
import org.bj.device.DeviceSessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * ST150肺功能设备处理器
 * 适配统一架构的ST150肺功能设备专有处理器
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
public class ST150DeviceProcessor implements DeviceProcessor {

    @Autowired
    private DeviceSessionManager deviceSessionManager;

    /** ST150常量 */
    private static final String DEVICE_TYPE = "ST150";
    private static final String TRANSMISSION_MODE_TX = "TX";
    private static final String TRANSMISSION_MODE_WTX = "WTX";

    /** 设备连接状态 */
    private final ConcurrentMap<String, Boolean> deviceConnections = new ConcurrentHashMap<>();

    /** 设备会话 */
    private final ConcurrentMap<String, ST150Session> sessionMap = new ConcurrentHashMap<>();

    /** 线程池 */
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    @Override
    public String getDeviceModel() {
        return "ST150肺功能";
    }
    
    @Override
    public String getDescription() {
        return "ST150肺功能设备处理器 - 支持肺功能测试和数据分析";
    }
    
    @Override
    public boolean handleConnection(DeviceContext context) {
        return handleConnection(context.getClient(), context.getPortDescriptor(), context.getSessionId());
    }

    // 保持向后兼容的方法
    public boolean handleConnection(SocketIOClient client, String portDescriptor, String userId) {
        try {
            log.info("ST150设备连接: 端口={}, 用户={}", portDescriptor, userId);

            // 1. 创建设备会话
            DeviceSession deviceSession = deviceSessionManager.createDeviceSession(
                client, userId, portDescriptor, "ST150");

            // 2. 处理患者关联
            handlePatientAssociation(client, portDescriptor, userId, deviceSession);

            // 3. 创建ST150会话
            ST150Session session = new ST150Session(portDescriptor, userId);
            session.setDeviceSessionId(deviceSession.getSessionId());
            sessionMap.put(portDescriptor, session);

            // 4. 异步连接设备
            CompletableFuture.runAsync(() -> {
                try {
                    // 模拟设备连接过程
                    boolean connected = connectToST150Device(portDescriptor);

                    if (connected) {
                        deviceConnections.put(portDescriptor, true);
                        session.setStatus("CONNECTED");

                        // 发送连接成功事件
                        client.sendEvent("st150DeviceConnected", createConnectionResponse(true,
                            "ST150肺功能设备连接成功", portDescriptor, userId));

                        log.info("ST150设备连接成功: 端口={}, 会话ID={}", portDescriptor, deviceSession.getSessionId());
                    } else {
                        session.setStatus("ERROR");
                        client.sendEvent("st150DeviceConnected", createConnectionResponse(false,
                            "ST150肺功能设备连接失败", portDescriptor, userId));
                    }

                } catch (Exception e) {
                    log.error("ST150设备异步连接失败", e);
                    session.setStatus("ERROR");
                    client.sendEvent("st150DeviceConnected", createConnectionResponse(false,
                        "ST150设备连接异常: " + e.getMessage(), portDescriptor, userId));
                }
            }, executorService);

            return true; // 异步连接，立即返回成功

        } catch (Exception e) {
            log.error("ST150设备连接失败", e);
            client.sendEvent("st150DeviceConnected", createConnectionResponse(false,
                "ST150设备连接异常: " + e.getMessage(), portDescriptor, userId));
            return false;
        }
    }
    
    @Override
    public boolean handleDisconnection(DeviceContext context) {
        return handleDisconnection(context.getClient(), context.getPortDescriptor(), context.getSessionId());
    }

    // 保持向后兼容的方法
    public boolean handleDisconnection(SocketIOClient client, String portDescriptor, String userId) {
        try {
            log.info("ST150设备断开: 端口={}, 用户={}", portDescriptor, userId);
            
            // 清理资源
            deviceConnections.remove(portDescriptor);
            sessionMap.remove(portDescriptor);
            
            // 发送断开事件
            client.sendEvent("st150DeviceDisconnected", createConnectionResponse(true, 
                "ST150设备已断开", portDescriptor, userId));
            
            return true;
            
        } catch (Exception e) {
            log.error("ST150设备断开失败", e);
            return false;
        }
    }
    
    @Override
    public boolean handleData(DeviceContext context, String rawData) {
        try {
            log.debug("处理ST150数据: 端口={}, 数据={}", context.getPortDescriptor(), rawData);
            
            // ST150特有的数据处理逻辑（控制码协议）
            ST150MeasurementData measurementData = parseST150Data(rawData);
            if (measurementData != null) {
                // 获取会话并更新数据
                ST150Session session = sessionMap.get(context.getPortDescriptor());
                if (session != null) {
                    session.setLastMeasurement(measurementData);
                }
                
                // 发送统一的测试结果事件
                sendUnifiedTestResult(context, measurementData);
                
                log.info("ST150测量完成: 端口={}, 结果={}", 
                    context.getPortDescriptor(), measurementData.getFormattedResult());
                
                return true;
            }
            
            return true; // 即使解析失败也返回true，避免中断数据流
            
        } catch (Exception e) {
            log.error("ST150数据处理失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendCommand(DeviceContext context, String command) {
        return sendCommand(context.getClient(), context.getPortDescriptor(), command, context.getSessionId());
    }

    // 保持向后兼容的方法
    public boolean sendCommand(SocketIOClient client, String portDescriptor, String command, String userId) {
        try {
            log.info("发送ST150设备指令: 端口={}, 指令={}", portDescriptor, command);
            
            // 构建ST150设备指令（控制码协议）
            String deviceCommand = buildST150Command(command);
            
            // 发送串口命令事件
            client.sendEvent("sendSerialCommand", createSerialCommand(portDescriptor, deviceCommand));
            
            return true;
            
        } catch (Exception e) {
            log.error("发送ST150设备指令失败", e);
            return false;
        }
    }
    
    @Override
    public String getDeviceStatus(DeviceContext context) {
        return getDeviceStatus(context.getPortDescriptor());
    }

    // 保持向后兼容的方法
    public String getDeviceStatus(String portDescriptor) {
        Boolean connected = deviceConnections.get(portDescriptor);
        return connected != null && connected ? "CONNECTED" : "DISCONNECTED";
    }
    
    @Override
    public boolean resetDevice(DeviceContext context) {
        // ST150肺功能设备重置：发送重置指令
        return sendCommand(context, "RESET");
    }
    
    @Override
    public boolean supportsFeature(String feature) {
        switch (feature.toUpperCase()) {
            case "LUNG_FUNCTION":
            case "CONTROL_CODE_PROTOCOL":
            case "ASYNC_CONNECTION":
                return true;
            default:
                return false;
        }
    }

    @Override
    public void initialize() {
        log.info("初始化ST150设备处理器");
        deviceConnections.clear();
        sessionMap.clear();
    }

    @Override
    public void destroy() {
        log.info("销毁ST150设备处理器");
        deviceConnections.clear();
        sessionMap.clear();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
    
    // =================== 私有方法 ===================
    
    /**
     * 处理患者关联
     */
    private void handlePatientAssociation(SocketIOClient client, String portDescriptor, String userId, DeviceSession deviceSession) {
        try {
            // ST150设备的患者关联逻辑
            log.debug("处理ST150设备患者关联: 端口={}, 会话ID={}", portDescriptor, deviceSession.getSessionId());

            // 这里可以添加ST150设备特有的患者关联逻辑
            // 例如：发送患者信息到肺功能仪、设置测试参数等

            // 发送患者关联成功事件
            client.sendEvent("st150PatientAssociated", createPatientAssociationResponse(true,
                "ST150设备患者关联成功", portDescriptor));

        } catch (Exception e) {
            log.error("ST150设备患者关联失败", e);
            client.sendEvent("st150PatientAssociated", createPatientAssociationResponse(false,
                "ST150设备患者关联失败: " + e.getMessage(), portDescriptor));
        }
    }
    
    /**
     * 连接到ST150设备
     */
    private boolean connectToST150Device(String portDescriptor) {
        try {
            log.debug("连接到ST150设备: 端口={}", portDescriptor);
            
            // 模拟设备连接过程
            Thread.sleep(2000); // 模拟连接延迟
            
            // 这里可以添加实际的ST150设备连接逻辑
            // 例如：发送初始化指令、检查设备响应等
            
            log.debug("ST150设备连接成功: 端口={}", portDescriptor);
            return true;
            
        } catch (Exception e) {
            log.error("连接ST150设备失败: 端口={}", portDescriptor, e);
            return false;
        }
    }
    
    /**
     * 解析ST150数据
     */
    private ST150MeasurementData parseST150Data(String data) {
        try {
            // ST150数据格式示例（控制码协议）
            if (data != null && data.trim().length() > 0) {
                ST150MeasurementData measurement = new ST150MeasurementData();
                measurement.setRawData(data);
                measurement.setTimestamp(System.currentTimeMillis());
                
                // 这里可以添加具体的ST150数据解析逻辑
                // 例如：解析肺活量、呼气流速等参数
                
                // 简单示例：假设数据包含肺活量信息
                if (data.contains("VC:")) {
                    String vcStr = data.substring(data.indexOf("VC:") + 3);
                    try {
                        double vitalCapacity = Double.parseDouble(vcStr.split(",")[0]);
                        measurement.setVitalCapacity(vitalCapacity);
                    } catch (Exception e) {
                        log.warn("解析肺活量数据失败: {}", data);
                    }
                }
                
                return measurement;
            }
            
        } catch (Exception e) {
            log.error("解析ST150数据失败: {}", data, e);
        }
        
        return null;
    }
    
    /**
     * 构建ST150设备指令（控制码协议）
     */
    private String buildST150Command(String command) {
        switch (command.toUpperCase()) {
            case "START_TEST":
                return convertToControlCode("START");
            case "STOP_TEST":
                return convertToControlCode("STOP");
            case "RESET":
                return convertToControlCode("RESET");
            case "GET_DATA":
                return convertToControlCode("DATA");
            default:
                return convertToControlCode(command);
        }
    }
    
    /**
     * 转换为ST150控制码格式
     */
    private String convertToControlCode(String command) {
        // 转换为ST150控制码格式
        return "ST150_CTRL:" + command;
    }

    // =================== 响应对象创建方法 ===================

    private Object createConnectionResponse(boolean success, String message, String portDescriptor, String userId) {
        return new Object() {
            public final boolean isSuccess = success;
            public final String statusMessage = message;
            public final String portDescriptorValue = portDescriptor;
            public final String userIdValue = userId;
            public final String deviceType = DEVICE_TYPE;
            public final long timestamp = System.currentTimeMillis();
        };
    }

    private Object createPatientAssociationResponse(boolean success, String message, String portDescriptor) {
        return new Object() {
            public final boolean isSuccess = success;
            public final String statusMessage = message;
            public final String portDescriptorValue = portDescriptor;
            public final String deviceType = DEVICE_TYPE;
            public final long timestamp = System.currentTimeMillis();
        };
    }

    private Object createSerialCommand(String portDescriptor, String command) {
        return new Object() {
            public final String port = portDescriptor;
            public final String data = command;
        };
    }

    // =================== 内部类 ===================

    /**
     * ST150会话
     */
    public static class ST150Session {
        private String sessionId;
        private String deviceSessionId;  // 设备会话ID
        private String portDescriptor;
        private String userId;
        private String status = "IDLE";
        private ST150MeasurementData lastMeasurement;

        public ST150Session(String portDescriptor, String userId) {
            this.sessionId = "ST150_" + System.currentTimeMillis();
            this.portDescriptor = portDescriptor;
            this.userId = userId;
        }

        // Getters and Setters
        public String getSessionId() { return sessionId; }
        public String getDeviceSessionId() { return deviceSessionId; }
        public void setDeviceSessionId(String deviceSessionId) { this.deviceSessionId = deviceSessionId; }
        public String getPortDescriptor() { return portDescriptor; }
        public String getUserId() { return userId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public ST150MeasurementData getLastMeasurement() { return lastMeasurement; }
        public void setLastMeasurement(ST150MeasurementData lastMeasurement) { this.lastMeasurement = lastMeasurement; }
    }

    /**
     * ST150测量数据
     */
    public static class ST150MeasurementData {
        private double vitalCapacity;    // 肺活量
        private double fev1;            // 第一秒用力呼气量
        private double peakFlow;        // 呼气峰流速
        private String assessment;      // 评估结果
        private String rawData;         // 原始数据
        private long timestamp;         // 时间戳

        // Getters and Setters
        public double getVitalCapacity() { return vitalCapacity; }
        public void setVitalCapacity(double vitalCapacity) { this.vitalCapacity = vitalCapacity; }
        public double getFev1() { return fev1; }
        public void setFev1(double fev1) { this.fev1 = fev1; }
        public double getPeakFlow() { return peakFlow; }
        public void setPeakFlow(double peakFlow) { this.peakFlow = peakFlow; }
        public String getAssessment() { return assessment; }
        public void setAssessment(String assessment) { this.assessment = assessment; }
        public String getRawData() { return rawData; }
        public void setRawData(String rawData) { this.rawData = rawData; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

        public String getFormattedResult() {
            return String.format("肺活量: %.2f L, FEV1: %.2f L, 峰流速: %.2f L/s",
                vitalCapacity, fev1, peakFlow);
        }
    }

    /**
     * 发送统一的测试结果事件
     */
    private void sendUnifiedTestResult(DeviceContext context, ST150MeasurementData measurementData) {
        try {
            // 计算测量耗时
            Long duration = 0L;
            if (context.getMeasurementStartTime() != null) {
                duration = System.currentTimeMillis() - context.getMeasurementStartTime();
            }

            // 创建统一的设备响应数据
            CommonDeviceResponse deviceResponse = CommonDeviceResponse.builder()
                .data(measurementData)
                .sessionId(context.getSessionId())
                .deviceId(context.getPortDescriptor())
                .deviceModel("ST150肺功能")
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(context.getExamItemInfo())
                .responseType(org.bj.constants.ResponseTypeConstants.MEASUREMENT_RESULT)
                .dataStatus("PROCESSED")
                .hasError(false)
                .isComplete(true)
                .build();

            // 添加患者信息
            if (context.getPatientInfo() != null) {
                deviceResponse.withPatientInfo(context.getPatientInfo());
            }

            // 添加时间信息
            if (context.getMeasurementStartTime() != null) {
                deviceResponse.withTimeInfo(context.getMeasurementStartTime(), System.currentTimeMillis());
            }

            // 添加扩展属性
            deviceResponse.addExtendedProperty("resultType", "LUNG_FUNCTION_TEST");
            deviceResponse.addExtendedProperty("formattedResult", measurementData.getFormattedResult());

            // 发送统一的testResult事件
            context.getClient().sendEvent(EventConstants.TEST_RESULT, deviceResponse.toMap());

            log.info("ST150测试结果已发送: 端口={}", context.getPortDescriptor());

        } catch (Exception e) {
            log.error("发送ST150测试结果失败", e);
        }
    }
}
