package org.bj.device.entity;

import lombok.Data;

/**
 * 查询测量结果请求
 * 用于直接查询设备的测量结果，不同设备处理器根据其支持能力进行相应处理
 *
 * <AUTHOR> Agent
 * @date 2025-07-06
 */
@Data
public class QueryResultsRequest {
    
    /** 患者信息 - 必填 */
    private PatientInfo patientInfo;
    
    /** 设备型号 - 可选，如果不指定则查询所有设备 */
    private String deviceModel;
    
    /** 设备ID - 可选，用于指定特定设备 */
    private String deviceId;
    
    /** 查询开始时间 - 可选，格式：yyyy-MM-dd HH:mm:ss */
    private String startTime;
    
    /** 查询结束时间 - 可选，格式：yyyy-MM-dd HH:mm:ss */
    private String endTime;
    
    /** 查询类型 - 可选：latest(最新结果), all(所有结果), range(时间范围) */
    private String queryType = "latest";
    
    /** 最大返回结果数 - 可选，默认10 */
    private Integer maxResults = 10;
    
    /** 是否包含详细数据 - 可选，默认true */
    private Boolean includeDetails = true;
    
    /** 请求时间戳 */
    private Long requestTimestamp = System.currentTimeMillis();
    
    /** 超时时间（毫秒） */
    private Long timeoutMs = 30000L;
    
    /** 备注信息 */
    private String remark;
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return patientInfo != null && 
               patientInfo.getExamNo() != null && 
               !patientInfo.getExamNo().trim().isEmpty();
    }
    
    /**
     * 获取患者ID
     */
    public String getPatientId() {
        return patientInfo != null ? patientInfo.getExamNo() : null;
    }
    
    /**
     * 获取患者姓名
     */
    public String getPatientName() {
        return patientInfo != null ? patientInfo.getName() : null;
    }
}
