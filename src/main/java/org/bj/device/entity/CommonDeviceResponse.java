package org.bj.device.entity;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.bj.constants.DataStorageType;
import org.bj.constants.DataStorageConstants;
import org.bj.constants.ResponseType;
import org.bj.constants.ResponseTypeConstants;
import org.bj.constants.DataStatusConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一设备响应数据实现类
 * 
 * 实现DeviceResponse接口，提供串口设备和非串口设备的统一数据结构
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonDeviceResponse implements DeviceResponse {
    
    /** 测量数据 - 必需字段 */
    private Object data;
    
    /** 用户ID - 必需字段 */
    private String sessionId;
    
    /** 设备ID - 必需字段 */
    private String deviceId;

    /** 设备代码 - 必需字段 */
    private String deviceCode;

    /** 设备名称 - 必需字段 */
    private String deviceName;
    
    /** 设备型号 - 必需字段 */
    private String deviceModel;
    
    /** 测量时间戳 - 必需字段 */
    private Long timestamp;
    
    /** 测量耗时 - 必需字段 */
    private Long duration;
    
    /** 体检项目信息 - 必需字段 */
    private ExamItemInfo examItemInfo;
    
    /** 响应类型 */
    @Builder.Default
    private String responseType = ResponseTypeConstants.MEASUREMENT_RESULT;
    
    /** 数据状态 */
    @Builder.Default
    private String dataStatus = DataStatusConstants.PROCESSED;
    
    /** 数据来源 */
    @Builder.Default
    private String dataSource = "DEVICE";
    
    /** 错误标志 */
    @Builder.Default
    private Boolean hasError = false;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 扩展属性 */
    @Builder.Default
    private Map<String, Object> extraProperties = new HashMap<>();
    
    /** 患者信息 - 可选字段 */
    private PatientInfo patientInfo;
    
    /** 原始数据 - 可选字段，用于调试和追溯 */
    private String rawData;
    
    /** 数据完整性标志 */
    @Builder.Default
    private Boolean isComplete = true;
    
    /** 测量开始时间 */
    private Long startTime;
    
    /** 测量结束时间 */
    private Long endTime;
    
    /** 操作员信息 */
    private String operatorId;
    
    /** 体检机构ID */
    private String institutionId;
    
    /** 体检批次号 */
    private String examBatchNo;

    /** 数据存储方式 */
    @Builder.Default
    private String dataStorageType = DataStorageConstants.TRANSFER;  // transfer, db
    
    @Override
    public Boolean hasError() {
        return this.hasError;
    }
    
    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        
        // 必需字段
        result.put("data", data);
        result.put("userId", sessionId);
        result.put("deviceId", deviceId);
        result.put("deviceModel", deviceModel);
        result.put("deviceCode", deviceCode);
        result.put("deviceName", deviceName);
        result.put("timestamp", timestamp);
        result.put("duration", duration);
        result.put("examItemInfo", examItemInfo);
        
        // 基本字段
        result.put("responseType", responseType);
        result.put("dataStatus", dataStatus);
        result.put("dataSource", dataSource);
        result.put("dataStorageType", dataStorageType);
        result.put("hasError", hasError);
        result.put("errorMessage", errorMessage);
        result.put("isComplete", isComplete);
        
        // 可选字段
        if (patientInfo != null) {
            result.put("patientInfo", patientInfo);
        }
        if (rawData != null) {
            result.put("rawData", rawData);
        }
        if (startTime != null) {
            result.put("startTime", startTime);
        }
        if (endTime != null) {
            result.put("endTime", endTime);
        }
        if (operatorId != null) {
            result.put("operatorId", operatorId);
        }
        if (institutionId != null) {
            result.put("institutionId", institutionId);
        }
        if (examBatchNo != null) {
            result.put("examBatchNo", examBatchNo);
        }

        // 扩展属性
        if (extraProperties != null && !extraProperties.isEmpty()) {
            result.put("extendedProperties", extraProperties);
        }
        
        return result;
    }
    
    /**
     * 创建成功响应
     */
    public static CommonDeviceResponse success(Object measurementData, String userId, String deviceId,
                                               String deviceModel, String deviceCode, String deviceName,
                                               ExamItemInfo examItemInfo, Long duration) {
        return CommonDeviceResponse.builder()
                .data(measurementData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(examItemInfo)
                .responseType(ResponseTypeConstants.MEASUREMENT_RESULT)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }

    /**
     * 创建成功响应（指定存储方式）
     */
    public static CommonDeviceResponse success(Object measurementData, String userId, String deviceId,
                                               String deviceModel,String deviceCode, String deviceName,
                                               ExamItemInfo examItemInfo, Long duration,
                                               DataStorageType storageType) {
        return CommonDeviceResponse.builder()
                .data(measurementData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(examItemInfo)
                .responseType(ResponseTypeConstants.MEASUREMENT_RESULT)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(storageType != null ? storageType.getValue() : DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }

    /**
     * 创建成功响应（指定响应类型）
     */
    public static CommonDeviceResponse success(Object measurementData, String userId, String deviceId,
                                               String deviceModel, String deviceCode, String deviceName,
                                              ExamItemInfo examItemInfo, Long duration,
                                               ResponseType responseType) {
        return CommonDeviceResponse.builder()
                .data(measurementData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(examItemInfo)
                .responseType(responseType != null ? responseType.getValue() : ResponseTypeConstants.MEASUREMENT_RESULT)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }

    /**
     * 创建成功响应（指定存储方式和响应类型）
     */
    public static CommonDeviceResponse success(Object measurementData, String userId, String deviceId,
                                               String deviceModel,String deviceCode, String deviceName,
                                               ExamItemInfo examItemInfo, Long duration,
                                               DataStorageType storageType, ResponseType responseType) {
        return CommonDeviceResponse.builder()
                .data(measurementData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(examItemInfo)
                .responseType(responseType != null ? responseType.getValue() : ResponseTypeConstants.MEASUREMENT_RESULT)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(storageType != null ? storageType.getValue() : DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }
    
    /**
     * 创建错误响应
     */
    public static CommonDeviceResponse error(String userId, String deviceId, String deviceModel,String deviceCode, String deviceName,
                                             String errorMessage, ExamItemInfo examItemInfo) {
        return CommonDeviceResponse.builder()
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(0L)
                .examItemInfo(examItemInfo)
                .responseType(ResponseTypeConstants.ERROR)
                .dataStatus(DataStatusConstants.ERROR)
                .dataStorageType(DataStorageConstants.TRANSFER)
                .hasError(true)
                .errorMessage(errorMessage)
                .isComplete(false)
                .build();
    }

    /**
     * 创建状态更新响应
     */
    public static CommonDeviceResponse statusUpdate(String userId, String deviceId, String deviceModel,String deviceCode, String deviceName,
                                                    Object statusData, ExamItemInfo examItemInfo) {
        return CommonDeviceResponse.builder()
                .data(statusData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(0L)
                .examItemInfo(examItemInfo)
                .responseType(ResponseTypeConstants.STATUS_UPDATE)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }

    /**
     * 创建设备信息响应
     */
    public static CommonDeviceResponse deviceInfo(String userId, String deviceId, String deviceModel,String deviceCode, String deviceName,
                                                  Object deviceInfoData, ExamItemInfo examItemInfo) {
        return CommonDeviceResponse.builder()
                .data(deviceInfoData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(0L)
                .examItemInfo(examItemInfo)
                .responseType(ResponseTypeConstants.DEVICE_INFO)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }

    /**
     * 创建校准结果响应
     */
    public static CommonDeviceResponse calibrationResult(String userId, String deviceId, String deviceModel,String deviceCode, String deviceName,
                                                         Object calibrationData, ExamItemInfo examItemInfo, Long duration) {
        return CommonDeviceResponse.builder()
                .data(calibrationData)
                .sessionId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .deviceCode(deviceCode)
                .deviceName(deviceName)
                .timestamp(System.currentTimeMillis())
                .duration(duration)
                .examItemInfo(examItemInfo)
                .responseType(ResponseTypeConstants.CALIBRATION_RESULT)
                .dataStatus(DataStatusConstants.PROCESSED)
                .dataStorageType(DataStorageConstants.TRANSFER)
                .hasError(false)
                .isComplete(true)
                .build();
    }
    
    /**
     * 从SerialPortData转换
     * @deprecated 请使用 SerialDeviceResponseAdapter.adaptSerialPortData() 方法
     */
    @Deprecated
    public static CommonDeviceResponse fromSerialPortData(SerialPortData serialData, Long duration) {
        // 创建基本的体检项目信息（如果serialData中有的话）
        ExamItemInfo examItem = null;
        if (serialData.getExamItemId() != null || serialData.getExamItemName() != null) {
            examItem = new ExamItemInfo();
            examItem.setItemId(serialData.getExamItemId());
            examItem.setItemName(serialData.getExamItemName());
            examItem.setValue(serialData.getValue());
            examItem.setUnit(serialData.getUnit());
            examItem.setValueRefRange(serialData.getReferenceRange());
            examItem.setAbnormalFlag(serialData.getResultStatus());
        }

        return CommonDeviceResponse.builder()
                .data(serialData.getValue())
                .sessionId(serialData.getSessionId())
                .deviceId(serialData.getDeviceSessionId())
                .deviceModel(serialData.getDeviceModel())
                .deviceCode(serialData.getDeviceCode())
                .deviceName(serialData.getDeviceName())
                .timestamp(serialData.getTimestamp())
                .duration(duration)
                .examItemInfo(examItem)
                .responseType(ResponseTypeConstants.MEASUREMENT_RESULT)
                .dataStatus(serialData.getDataStatus())
                .dataSource("SERIAL")
                .hasError(serialData.getHasError())
                .errorMessage(serialData.getErrorMessage())
                .rawData(serialData.getData())
                .isComplete(serialData.getIsComplete())
                .operatorId(serialData.getOperatorId())
                .institutionId(serialData.getInstitutionId())
                .examBatchNo(serialData.getExamBatchNo())
                .build();
    }
    
    /**
     * 添加扩展属性
     */
    public CommonDeviceResponse addExtendedProperty(String key, Object value) {
        if (this.extraProperties == null) {
            this.extraProperties = new HashMap<>();
        }
        this.extraProperties.put(key, value);
        return this;
    }
    
    /**
     * 设置患者信息
     */
    public CommonDeviceResponse withPatientInfo(PatientInfo patientInfo) {
        this.patientInfo = patientInfo;
        return this;
    }
    
    /**
     * 设置时间信息
     */
    public CommonDeviceResponse withTimeInfo(Long startTime, Long endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
        return this;
    }

    /**
     * 设置数据存储方式（使用枚举）
     *
     * @param storageType 数据存储方式枚举
     * @return 当前对象
     */
    public CommonDeviceResponse withStorageType(DataStorageType storageType) {
        this.dataStorageType = storageType != null ? storageType.getValue() : DataStorageConstants.DEFAULT;
        return this;
    }

    /**
     * 设置数据存储方式（使用字符串）
     *
     * @param storageType 数据存储方式字符串
     * @return 当前对象
     */
    public CommonDeviceResponse withStorageType(String storageType) {
        this.dataStorageType = DataStorageConstants.getValidOrDefault(storageType);
        return this;
    }

    /**
     * 设置为直接传输模式
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asTransferMode() {
        this.dataStorageType = DataStorageConstants.TRANSFER;
        return this;
    }

    /**
     * 设置为数据库存储模式
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asDatabaseMode() {
        this.dataStorageType = DataStorageConstants.DB;
        return this;
    }

    /**
     * 检查是否为直接传输模式
     *
     * @return 是否为直接传输模式
     */
    public boolean isTransferMode() {
        return DataStorageConstants.isTransfer(this.dataStorageType);
    }

    /**
     * 检查是否为数据库存储模式
     *
     * @return 是否为数据库存储模式
     */
    public boolean isDatabaseMode() {
        return DataStorageConstants.isDatabase(this.dataStorageType);
    }

    /**
     * 获取数据存储方式枚举
     *
     * @return 数据存储方式枚举
     */
    public DataStorageType getStorageTypeEnum() {
        return DataStorageType.fromValueOrDefault(this.dataStorageType, DataStorageType.getDefault());
    }

    // ==================== 响应类型相关方法 ====================

    /**
     * 设置响应类型（使用枚举）
     *
     * @param responseType 响应类型枚举
     * @return 当前对象
     */
    public CommonDeviceResponse withResponseType(ResponseType responseType) {
        this.responseType = responseType != null ? responseType.getValue() : ResponseTypeConstants.DEFAULT;
        return this;
    }

    /**
     * 设置响应类型（使用字符串）
     *
     * @param responseType 响应类型字符串
     * @return 当前对象
     */
    public CommonDeviceResponse withResponseType(String responseType) {
        this.responseType = ResponseTypeConstants.getValidOrDefault(responseType);
        return this;
    }

    /**
     * 设置为测量结果类型
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asMeasurementResult() {
        this.responseType = ResponseTypeConstants.MEASUREMENT_RESULT;
        return this;
    }

    /**
     * 设置为错误响应类型
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asError() {
        this.responseType = ResponseTypeConstants.ERROR;
        return this;
    }

    /**
     * 设置为状态更新类型
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asStatusUpdate() {
        this.responseType = ResponseTypeConstants.STATUS_UPDATE;
        return this;
    }

    /**
     * 设置为校准结果类型
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asCalibrationResult() {
        this.responseType = ResponseTypeConstants.CALIBRATION_RESULT;
        return this;
    }

    /**
     * 设置为设备信息类型
     *
     * @return 当前对象
     */
    public CommonDeviceResponse asDeviceInfo() {
        this.responseType = ResponseTypeConstants.DEVICE_INFO;
        return this;
    }

    /**
     * 检查是否为测量结果类型
     *
     * @return 是否为测量结果类型
     */
    public boolean isMeasurementResult() {
        return ResponseTypeConstants.isMeasurementResult(this.responseType);
    }

    /**
     * 检查是否为错误类型
     *
     * @return 是否为错误类型
     */
    public boolean isError() {
        return ResponseTypeConstants.isError(this.responseType);
    }

    /**
     * 检查是否为状态更新类型
     *
     * @return 是否为状态更新类型
     */
    public boolean isStatusUpdate() {
        return ResponseTypeConstants.isStatusUpdate(this.responseType);
    }

    /**
     * 检查是否为连接相关类型
     *
     * @return 是否为连接相关类型
     */
    public boolean isConnectionRelated() {
        return ResponseTypeConstants.isConnectionRelated(this.responseType);
    }

    /**
     * 检查是否为数据相关类型
     *
     * @return 是否为数据相关类型
     */
    public boolean isDataRelated() {
        return ResponseTypeConstants.isDataRelated(this.responseType);
    }

    /**
     * 检查是否为设备管理相关类型
     *
     * @return 是否为设备管理相关类型
     */
    public boolean isDeviceManagementRelated() {
        return ResponseTypeConstants.isDeviceManagementRelated(this.responseType);
    }

    /**
     * 获取响应类型枚举
     *
     * @return 响应类型枚举
     */
    public ResponseType getResponseTypeEnum() {
        return ResponseType.fromValueOrDefault(this.responseType, ResponseType.getDefault());
    }
}
