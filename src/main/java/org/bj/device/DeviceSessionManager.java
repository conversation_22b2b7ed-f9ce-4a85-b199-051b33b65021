package org.bj.device;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.entity.DeviceCommand;
import org.bj.device.entity.DeviceSession;
import org.bj.device.entity.SerialPortData;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.List;
import java.util.ArrayList;

/**
 * 设备会话管理器
 * 负责管理多设备并发连接和数据关联
 */
@Slf4j
@Service
public class DeviceSessionManager {

    /**
     * 设备会话映射 - key: sessionId, value: DeviceSession
     */
    private final ConcurrentHashMap<String, DeviceSession> deviceSessions = new ConcurrentHashMap<>();

    /**
     * 用户设备映射 - key: userId, value: List<sessionId>
     */
    private final ConcurrentHashMap<String, ConcurrentLinkedQueue<String>> userDeviceMapping = new ConcurrentHashMap<>();

    /**
     * 端口设备映射 - key: portDescriptor, value: sessionId
     */
    private final ConcurrentHashMap<String, String> portDeviceMapping = new ConcurrentHashMap<>();

    /**
     * 患者设备映射 - key: patientId, value: sessionId
     */
    private final ConcurrentHashMap<String, String> patientDeviceMapping = new ConcurrentHashMap<>();

    /**
     * 待处理指令队列 - key: sessionId, value: 指令队列
     */
    private final ConcurrentHashMap<String, ConcurrentLinkedQueue<DeviceCommand>> pendingCommands = new ConcurrentHashMap<>();

    /**
     * 指令序列号生成器
     */
    private final AtomicLong globalCommandSequence = new AtomicLong(0);

    /**
     * 定时任务执行器
     */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    /**
     * 会话超时时间 (24小时)
     */
    private static final long SESSION_TIMEOUT_MS = 24 * 60 * 60 * 1000;

    /**
     * 指令超时检查间隔 (30秒)
     */
    private static final long COMMAND_CHECK_INTERVAL_MS = 30 * 1000;

    public DeviceSessionManager() {
        // 启动定时清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredSessions, SESSION_TIMEOUT_MS, SESSION_TIMEOUT_MS, TimeUnit.MILLISECONDS);

        scheduler.scheduleAtFixedRate(this::cleanupTimeoutCommands, COMMAND_CHECK_INTERVAL_MS, COMMAND_CHECK_INTERVAL_MS, TimeUnit.MILLISECONDS);

        log.info("设备会话管理器已启动");
    }

    /**
     * 创建设备会话
     */
    public DeviceSession createDeviceSession(SocketIOClient client, String userId, String portDescriptor, String deviceType) {

        // 检查端口是否已被占用
        String existingSessionId = portDeviceMapping.get(portDescriptor);
        if (existingSessionId != null) {
            removeDeviceSession(existingSessionId);
        }

        DeviceSession session = new DeviceSession(client, userId, portDescriptor, deviceType);
        String sessionId = session.getSessionId();

        // 注册会话
        deviceSessions.put(sessionId, session);
        portDeviceMapping.put(portDescriptor, sessionId);

        // 用户设备关联
        userDeviceMapping.computeIfAbsent(userId, k -> new ConcurrentLinkedQueue<>()).add(sessionId);

        // 初始化指令队列
        pendingCommands.put(sessionId, new ConcurrentLinkedQueue<>());

        log.info("创建设备会话: {} (用户: {}, 端口: {}, 设备类型: {})", sessionId, userId, portDescriptor, deviceType);

        return session;
    }

    /**
     * 移除设备会话
     */
    public void removeDeviceSession(String sessionId) {
        DeviceSession session = deviceSessions.remove(sessionId);
        if (session == null) {
            return;
        }

        // 清理各种映射关系
        portDeviceMapping.remove(session.getPortDescriptor());

        if (session.getCurrentPatientId() != null) {
            patientDeviceMapping.remove(session.getCurrentPatientId());
        }

        ConcurrentLinkedQueue<String> userSessions = userDeviceMapping.get(session.getUserId());
        if (userSessions != null) {
            userSessions.remove(sessionId);
            if (userSessions.isEmpty()) {
                userDeviceMapping.remove(session.getUserId());
            }
        }

        // 清理待处理指令
        pendingCommands.remove(sessionId);

        log.info("移除设备会话: {} (用户: {}, 端口: {})", sessionId, session.getUserId(), session.getPortDescriptor());
    }

    /**
     * 关联患者到设备会话
     */
    public boolean associatePatientToDevice(String sessionId, String patientId) {
        DeviceSession session = deviceSessions.get(sessionId);
        if (session == null) {
            log.warn("设备会话不存在: {}", sessionId);
            return false;
        }

        // 检查患者是否已关联到其他设备
        String existingSessionId = patientDeviceMapping.get(patientId);
        if (existingSessionId != null && !existingSessionId.equals(sessionId)) {
            DeviceSession existingSession = deviceSessions.get(existingSessionId);
            if (existingSession != null && existingSession.isClientConnected()) {
                log.warn("患者 {} 已关联到设备会话 {}", patientId, existingSessionId);
                return false;
            } else {
                // 清理无效关联
                patientDeviceMapping.remove(patientId);
            }
        }

        // 清除当前会话的旧患者关联
        if (session.getCurrentPatientId() != null) {
            patientDeviceMapping.remove(session.getCurrentPatientId());
        }

        // 建立新关联
        session.associatePatient(patientId);
        patientDeviceMapping.put(patientId, sessionId);

        log.info("患者 {} 关联到设备会话 {}", patientId, sessionId);
        return true;
    }

    /**
     * 清除患者设备关联
     */
    public void clearPatientAssociation(String sessionId) {
        DeviceSession session = deviceSessions.get(sessionId);
        if (session != null && session.getCurrentPatientId() != null) {
            patientDeviceMapping.remove(session.getCurrentPatientId());
            session.clearPatientAssociation();
        }
    }

    /**
     * 根据端口获取设备会话
     */
    public DeviceSession getSessionByPort(String portDescriptor) {
        String sessionId = portDeviceMapping.get(portDescriptor);
        return sessionId != null ? deviceSessions.get(sessionId) : null;
    }

    /**
     * 根据用户ID获取设备会话列表
     */
    public java.util.List<DeviceSession> getSessionsByUser(String userId) {
        ConcurrentLinkedQueue<String> sessionIds = userDeviceMapping.get(userId);
        if (sessionIds == null) {
            return java.util.Collections.emptyList();
        }

        return sessionIds.stream().map(deviceSessions::get).filter(java.util.Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 根据患者ID获取设备会话
     */
    public DeviceSession getSessionByPatient(String patientId) {
        String sessionId = patientDeviceMapping.get(patientId);
        return sessionId != null ? deviceSessions.get(sessionId) : null;
    }

    /**
     * 增强串口数据，添加关联信息
     */
    public SerialPortData enhanceSerialPortData(String portDescriptor, String rawData) {
        DeviceSession session = getSessionByPort(portDescriptor);
        if (session == null) {
            log.warn("未找到端口 {} 对应的设备会话", portDescriptor);
            return createBasicSerialPortData(portDescriptor, rawData);
        }

        session.updateLastActiveTime();

        SerialPortData data = new SerialPortData();
        data.setPort(portDescriptor);
        data.setSessionId(session.getUserId());
        data.setData(rawData);

        // 添加增强信息
        data.setDeviceSessionId(session.getSessionId());
        data.setDeviceType(session.getDeviceType());
        data.setPatientId(session.getCurrentPatientId());
        data.setTimestamp(System.currentTimeMillis());
        data.setCommandSequence(session.getCommandSequence().get());

        log.debug("增强串口数据: 端口={}, 用户={}, 患者={}, 会话={}", portDescriptor, session.getUserId(), session.getCurrentPatientId(), session.getSessionId());

        return data;
    }

    /**
     * 创建基础串口数据（兜底方案）
     */
    private SerialPortData createBasicSerialPortData(String portDescriptor, String rawData) {
        SerialPortData data = new SerialPortData();
        data.setPort(portDescriptor);
        data.setData(rawData);
        data.setTimestamp(System.currentTimeMillis());
        data.setSessionId("unknown");
        return data;
    }

    /**
     * 添加待处理指令
     */
    public DeviceCommand addPendingCommand(String sessionId, DeviceCommand.CommandType commandType, String commandData, String patientId) {
        DeviceSession session = deviceSessions.get(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("设备会话不存在: " + sessionId);
        }

        DeviceCommand command = new DeviceCommand(sessionId, session.getUserId(), patientId, session.getPortDescriptor(), commandType, commandData);
        command.setCommandSequence(globalCommandSequence.incrementAndGet());

        ConcurrentLinkedQueue<DeviceCommand> queue = pendingCommands.get(sessionId);
        if (queue != null) {
            queue.offer(command);
            log.debug("添加待处理指令: {} (会话: {}, 序列号: {})", commandType, sessionId, command.getCommandSequence());
        }

        return command;
    }

    /**
     * 获取设备状态统计
     */
    public java.util.Map<String, Object> getDeviceStatistics() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalSessions", deviceSessions.size());
        stats.put("activeUsers", userDeviceMapping.size());
        stats.put("connectedPorts", portDeviceMapping.size());
        stats.put("associatedPatients", patientDeviceMapping.size());
        stats.put("pendingCommands", pendingCommands.values().stream().mapToInt(java.util.Collection::size).sum());

        return stats;
    }

    /**
     * 清理超时会话
     */
    private void cleanupExpiredSessions() {
        long now = System.currentTimeMillis();

        deviceSessions.entrySet().removeIf(entry -> {
            DeviceSession session = entry.getValue();
            boolean expired = (now - session.getLastActiveTime().get()) > SESSION_TIMEOUT_MS;

            if (expired || !session.isClientConnected()) {
                log.info("清理过期设备会话: {} (用户: {})", entry.getKey(), session.getUserId());
                removeDeviceSession(entry.getKey());
                return true;
            }
            return false;
        });
    }

    /**
     * 清理超时指令
     */
    private void cleanupTimeoutCommands() {
        pendingCommands.forEach((sessionId, queue) -> {
            queue.removeIf(command -> {
                if (command.isTimeout()) {
                    command.setStatus(DeviceCommand.CommandStatus.TIMEOUT);
                    log.warn("指令超时: {} (会话: {}, 序列号: {})", command.getCommandType(), sessionId, command.getCommandSequence());
                    return true;
                }
                return false;
            });
        });
    }

    /**
     * 清理指定用户的所有会话
     * 在用户断开连接时调用
     */
    public void cleanupUserSessions(String userId) {
        log.info("开始清理用户会话: 用户={}", userId);

        try {
            // 1. 获取用户的所有会话ID
            ConcurrentLinkedQueue<String> userSessions = userDeviceMapping.get(userId);
            if (userSessions == null || userSessions.isEmpty()) {
                log.debug("用户{}没有活跃会话", userId);
                return;
            }

            // 2. 逐个移除用户的会话
            List<String> sessionIds = new ArrayList<>(userSessions);
            for (String sessionId : sessionIds) {
                try {
                    removeDeviceSession(sessionId);
                    log.debug("移除用户会话: 用户={}, 会话ID={}", userId, sessionId);
                } catch (Exception e) {
                    log.error("移除用户会话失败: 用户={}, 会话ID={}", userId, sessionId, e);
                }
            }

            // 3. 清理用户映射
            userDeviceMapping.remove(userId);

            log.info("用户会话清理完成: 用户={}, 清理会话数量={}", userId, sessionIds.size());

        } catch (Exception e) {
            log.error("清理用户会话失败: 用户={}", userId, e);
        }
    }

    @PreDestroy
    public void shutdown() {
        scheduler.shutdown();
        deviceSessions.clear();
        userDeviceMapping.clear();
        portDeviceMapping.clear();
        patientDeviceMapping.clear();
        pendingCommands.clear();
        log.info("设备会话管理器已关闭");
    }
} 