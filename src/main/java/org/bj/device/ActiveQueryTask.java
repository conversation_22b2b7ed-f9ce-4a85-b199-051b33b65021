package org.bj.device;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.entity.DeviceContext;
import org.bj.device.entity.StageQueryConfig;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 主动查询任务
 * 用于需要主动发送指令获取测量结果的设备
 * 
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@Data
public class ActiveQueryTask implements Runnable {
    
    /** 串口上下文 */
    private final DeviceContext context;
    
    /** 查询指令 */
    private final String queryCommand;
    
    /** 查询间隔时间（毫秒） */
    private final Long queryIntervalMs;
    
    /** 最大查询次数 */
    private final Integer maxQueryAttempts;
    
    /** 查询超时时间（毫秒） */
    private final Long queryTimeoutMs;
    
    /** 当前查询次数 */
    private final AtomicInteger currentAttempt = new AtomicInteger(0);
    
    /** 是否已停止 */
    private final AtomicBoolean stopped = new AtomicBoolean(false);
    
    /** 是否已获得结果 */
    private final AtomicBoolean resultReceived = new AtomicBoolean(false);
    
    /** 任务开始时间 */
    private final Long startTime = System.currentTimeMillis();

    /** 阶段查询配置 */
    private final StageQueryConfig stageQueryConfig;

    /** 是否为多阶段查询 */
    private final boolean isMultiStageQuery;
    
    /**
     * 构造函数 - 简单查询
     */
    public ActiveQueryTask(DeviceContext context, String queryCommand,
                           Long queryIntervalMs, Integer maxQueryAttempts, Long queryTimeoutMs) {
        this.context = context;
        this.queryCommand = queryCommand;
        this.queryIntervalMs = queryIntervalMs;
        this.maxQueryAttempts = maxQueryAttempts;
        this.queryTimeoutMs = queryTimeoutMs;
        this.stageQueryConfig = null;
        this.isMultiStageQuery = false;
    }

    /**
     * 构造函数 - 多阶段查询
     */
    public ActiveQueryTask(DeviceContext context, StageQueryConfig stageQueryConfig, Long queryTimeoutMs) {
        this.context = context;
        this.stageQueryConfig = stageQueryConfig;
        this.queryTimeoutMs = queryTimeoutMs;
        this.isMultiStageQuery = true;

        // 从第一个阶段获取初始配置
        StageQueryConfig.StageQuery firstStage = stageQueryConfig.getFirstStage();
        if (firstStage != null) {
            this.queryCommand = firstStage.getQueryCommandTemplate();
            this.queryIntervalMs = firstStage.getQueryInterval() != null ?
                firstStage.getQueryInterval() : stageQueryConfig.getDefaultQueryInterval();
            this.maxQueryAttempts = firstStage.getMaxAttempts() != null ?
                firstStage.getMaxAttempts() : stageQueryConfig.getDefaultMaxAttempts();
        } else {
            this.queryCommand = "";
            this.queryIntervalMs = stageQueryConfig.getDefaultQueryInterval();
            this.maxQueryAttempts = stageQueryConfig.getDefaultMaxAttempts();
        }
    }
    
    @Override
    public void run() {
        try {
            log.info("开始主动查询任务: 上下文={}, 指令={}, 间隔={}ms, 最大次数={}", 
                context.getContextId(), queryCommand, queryIntervalMs, maxQueryAttempts);
            
            if (isMultiStageQuery) {
                executeMultiStageQuery();
            } else {
                executeSingleStageQuery();
            }
            
            // 检查结束原因
            checkCompletionReason();
            
        } catch (Exception e) {
            log.error("主动查询任务执行失败: 上下文={}", context.getContextId(), e);
        } finally {
            cleanup();
        }
    }

    /**
     * 执行单阶段查询
     */
    private void executeSingleStageQuery() {
        while (!stopped.get() && !resultReceived.get() &&
               currentAttempt.get() < maxQueryAttempts && !isTimeout()) {

            try {
                // 发送查询指令
                sendQueryCommand(queryCommand);

                // 增加查询次数
                int attempt = currentAttempt.incrementAndGet();

                log.debug("发送查询指令: 上下文={}, 第{}次查询, 指令={}",
                    context.getContextId(), attempt, queryCommand);

                // 等待查询间隔
                Thread.sleep(queryIntervalMs);

            } catch (InterruptedException e) {
                log.info("查询任务被中断: 上下文={}", context.getContextId());
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("发送查询指令失败: 上下文={}, 第{}次查询",
                    context.getContextId(), currentAttempt.get(), e);
            }
        }
    }

    /**
     * 执行多阶段查询
     */
    private void executeMultiStageQuery() {
        try {
            // 进入第一个阶段
            StageQueryConfig.StageQuery firstStage = stageQueryConfig.getFirstStage();
            if (firstStage != null) {
                context.enterStage(firstStage.getStageName());
                executeStageQuery(firstStage);
            } else {
                log.error("多阶段查询配置中没有找到第一个阶段: 上下文={}", context.getContextId());
            }
        } catch (Exception e) {
            log.error("执行多阶段查询失败: 上下文={}", context.getContextId(), e);
        }
    }

    /**
     * 执行阶段查询
     */
    private void executeStageQuery(StageQueryConfig.StageQuery stageQuery) {
        try {
            log.info("开始执行阶段查询: 上下文={}, 阶段={}",
                context.getContextId(), stageQuery.getStageName());

            // 获取阶段配置
            Long stageQueryInterval = stageQuery.getQueryInterval() != null ?
                stageQuery.getQueryInterval() : stageQueryConfig.getDefaultQueryInterval();
            Integer stageMaxAttempts = stageQuery.getMaxAttempts() != null ?
                stageQuery.getMaxAttempts() : stageQueryConfig.getDefaultMaxAttempts();

            int stageAttempts = 0;
            boolean stageCompleted = false;

            while (!stopped.get() && !resultReceived.get() && !isTimeout() &&
                   stageAttempts < stageMaxAttempts && !stageCompleted) {

                try {
                    // 构建查询指令（使用上下文中的阶段数据）
                    String actualCommand = stageQueryConfig.buildQueryCommand(stageQuery, context.getAllStageData());

                    // 发送查询指令
                    sendQueryCommand(actualCommand);

                    // 增加查询次数
                    stageAttempts++;
                    currentAttempt.incrementAndGet();

                    log.debug("发送阶段查询指令: 上下文={}, 阶段={}, 第{}次查询, 指令={}",
                        context.getContextId(), stageQuery.getStageName(), stageAttempts, actualCommand);

                    // 等待查询间隔
                    Thread.sleep(stageQueryInterval);

                    // 检查是否需要进入下一阶段
                    if (shouldTransitionToNextStage(stageQuery)) {
                        stageCompleted = true;
                        transitionToNextStage(stageQuery);
                    }

                } catch (InterruptedException e) {
                    log.info("阶段查询任务被中断: 上下文={}, 阶段={}",
                        context.getContextId(), stageQuery.getStageName());
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("发送阶段查询指令失败: 上下文={}, 阶段={}, 第{}次查询",
                        context.getContextId(), stageQuery.getStageName(), stageAttempts, e);
                }
            }

            if (!stageCompleted && !stopped.get() && !resultReceived.get()) {
                log.warn("阶段查询未完成: 上下文={}, 阶段={}, 查询次数={}",
                    context.getContextId(), stageQuery.getStageName(), stageAttempts);
            }

        } catch (Exception e) {
            log.error("执行阶段查询失败: 上下文={}, 阶段={}",
                context.getContextId(), stageQuery.getStageName(), e);
        }
    }

    /**
     * 检查是否应该转换到下一阶段
     */
    private boolean shouldTransitionToNextStage(StageQueryConfig.StageQuery stageQuery) {
        // 如果是最终阶段，不需要转换
        if (stageQuery.getIsFinalStage() != null && stageQuery.getIsFinalStage()) {
            return false;
        }

        // 检查转换条件
        StageQueryConfig.StageTransition transition = stageQuery.getNextStageCondition();
        if (transition != null) {
            // 这里可以实现更复杂的转换条件判断
            // 简化实现：如果收到了数据就转换
            return "DATA_RECEIVED".equals(transition.getTransitionType());
        }

        return false;
    }

    /**
     * 转换到下一阶段
     */
    private void transitionToNextStage(StageQueryConfig.StageQuery currentStage) {
        try {
            StageQueryConfig.StageTransition transition = currentStage.getNextStageCondition();
            if (transition != null && transition.getNextStageName() != null) {

                // 获取下一阶段配置
                StageQueryConfig.StageQuery nextStage = stageQueryConfig.getStageQuery(transition.getNextStageName());
                if (nextStage != null) {
                    log.info("转换到下一阶段: 上下文={}, 当前阶段={}, 下一阶段={}",
                        context.getContextId(), currentStage.getStageName(), nextStage.getStageName());

                    // 进入下一阶段
                    context.enterStage(nextStage.getStageName());

                    // 执行下一阶段查询
                    executeStageQuery(nextStage);
                } else {
                    log.error("未找到下一阶段配置: 上下文={}, 阶段名称={}",
                        context.getContextId(), transition.getNextStageName());
                }
            }
        } catch (Exception e) {
            log.error("转换到下一阶段失败: 上下文={}, 当前阶段={}",
                context.getContextId(), currentStage.getStageName(), e);
        }
    }

    /**
     * 发送查询指令
     */
    private void sendQueryCommand(String command) {
        try {
            // 这里需要实际发送串口指令
            // 由于当前架构中串口对象在其他地方管理，这里先记录日志
            // 实际实现需要获取串口对象并发送指令
            
            log.debug("发送查询指令: 端口={}, 指令={}, 上下文={}",
                context.getPortDescriptor(), command, context.getContextId());
            
            // TODO: 实际发送串口指令的逻辑
            // SerialPort serialPort = getSerialPort(context.getPortDescriptor());
            // if (serialPort != null && serialPort.isOpen()) {
            //     byte[] commandBytes = command.getBytes();
            //     int bytesWritten = serialPort.writeBytes(commandBytes, commandBytes.length);
            //     if (bytesWritten <= 0) {
            //         throw new RuntimeException("发送指令失败");
            //     }
            // } else {
            //     throw new RuntimeException("串口未打开");
            // }
            
            // 更新上下文活跃时间
            context.updateActiveTime();
            
        } catch (Exception e) {
            log.error("发送查询指令异常: 指令={}", command, e);
            throw e;
        }
    }
    
    /**
     * 检查是否超时
     */
    private boolean isTimeout() {
        return System.currentTimeMillis() - startTime > queryTimeoutMs;
    }
    
    /**
     * 检查完成原因
     */
    private void checkCompletionReason() {
        if (resultReceived.get()) {
            log.info("查询任务完成 - 已获得结果: 上下文={}, 查询次数={}", 
                context.getContextId(), currentAttempt.get());
        } else if (stopped.get()) {
            log.info("查询任务完成 - 手动停止: 上下文={}, 查询次数={}", 
                context.getContextId(), currentAttempt.get());
        } else if (currentAttempt.get() >= maxQueryAttempts) {
            log.warn("查询任务完成 - 达到最大查询次数: 上下文={}, 查询次数={}", 
                context.getContextId(), currentAttempt.get());
            
            // 发送查询超时事件
            sendQueryTimeoutEvent();
        } else if (isTimeout()) {
            log.warn("查询任务完成 - 查询超时: 上下文={}, 查询次数={}, 耗时={}ms", 
                context.getContextId(), currentAttempt.get(), 
                System.currentTimeMillis() - startTime);
            
            // 发送查询超时事件
            sendQueryTimeoutEvent();
        }
    }
    
    /**
     * 发送查询超时事件
     */
    private void sendQueryTimeoutEvent() {
        try {
            QueryTimeoutEvent event = new QueryTimeoutEvent();
            event.setContextId(context.getContextId());
            event.setPortDescriptor(context.getPortDescriptor());
            event.setDeviceType(context.getDeviceType());
            event.setQueryCommand(queryCommand);
            event.setQueryAttempts(currentAttempt.get());
            event.setMaxAttempts(maxQueryAttempts);
            event.setElapsedTime(System.currentTimeMillis() - startTime);
            event.setTimestamp(System.currentTimeMillis());
            
            // 添加体检信息
            if (context.hasPhysicalExamInfo()) {
                if (context.getPatientInfo() != null) {
                    event.setPatientId(context.getPatientInfo().getExamNo());
                    event.setPatientName(context.getPatientInfo().getName());
                }
                if (context.getExamItemInfo() != null) {
                    event.setExamItemId(context.getExamItemInfo().getItemId());
                    event.setExamItemName(context.getExamItemInfo().getItemName());
                }
            }
            
            // 发送事件
            context.getClient().sendEvent("queryTimeout", event);
            
            log.debug("发送查询超时事件: 上下文={}", context.getContextId());
            
        } catch (Exception e) {
            log.error("发送查询超时事件失败", e);
        }
    }
    
    /**
     * 停止查询任务
     */
    public void stop() {
        stopped.set(true);
        log.info("停止查询任务: 上下文={}", context.getContextId());
    }
    
    /**
     * 标记已收到结果
     */
    public void markResultReceived() {
        resultReceived.set(true);
        log.info("标记查询任务已收到结果: 上下文={}, 查询次数={}", 
            context.getContextId(), currentAttempt.get());
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        log.debug("清理查询任务资源: 上下文={}", context.getContextId());
        // 这里可以添加资源清理逻辑
    }
    
    /**
     * 获取查询状态信息
     */
    public String getStatusInfo() {
        return String.format("查询任务状态: 上下文=%s, 查询次数=%d/%d, 已停止=%s, 已收到结果=%s, 耗时=%dms",
            context.getContextId(), 
            currentAttempt.get(), 
            maxQueryAttempts,
            stopped.get(),
            resultReceived.get(),
            System.currentTimeMillis() - startTime);
    }
    
    /**
     * 查询超时事件
     */
    @Data
    public static class QueryTimeoutEvent {
        private String contextId;
        private String portDescriptor;
        private String deviceType;
        private String queryCommand;
        private Integer queryAttempts;
        private Integer maxAttempts;
        private Long elapsedTime;
        private Long timestamp;
        
        // 体检相关信息
        private String patientId;
        private String patientName;
        private String examItemId;
        private String examItemName;
    }
}
