package org.bj.device;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.extern.slf4j.Slf4j;
import org.bj.constants.EventConstants;
import org.bj.device.entity.*;
import org.bj.device.util.DeviceStatusSender;
import org.bj.exceptions.DeviceExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 统一串口处理器
 * 提供统一的串口连接、数据处理和异常处理接口
 * 根据上下文智能选择通用或专有处理逻辑
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@Service
public class DeviceHandler {

    @Autowired
    private DeviceProcessorManager deviceProcessorManager;

    @Autowired
    private DeviceSessionManager deviceSessionManager;

    @Autowired
    private DeviceExceptionHandler exceptionHandler;

    @Autowired
    private ApplicationEventPublisher eventPublisher;


    /**
     * 活跃的串口上下文映射
     */
    private final ConcurrentMap<String, DeviceContext> activeContexts = new ConcurrentHashMap<>();

    /**
     * 端口到上下文的映射（向后兼容）
     */
    private final ConcurrentMap<String, String> portToContextMapping = new ConcurrentHashMap<>();

    /**
     * 设备标识符到上下文的映射（通用）
     */
    private final ConcurrentMap<String, String> deviceToContextMapping = new ConcurrentHashMap<>();


    /**
     * 处理连接 - 简化版本
     */
    public void handleConnect(SocketIOClient client, ConnectRequest request, String userId) {
        DeviceContext context = null;
        try {
            log.info("处理连接: 用户={},  设备={}", userId, request.getDeviceProperties().getDeviceModel());

            // 1. 初始化连接上下文
            context = initializeConnectionContext(client, userId, request);

            // 2. 执行连接策略
            executeConnectionStrategy(context, request);

            // 3. 完成连接设置
            finalizeConnection(context, request.getDeviceProperties());

            log.info("连接成功: {}", context.getContextDescription());
        } catch (Exception e) {
            log.error("连接失败", e);
            DeviceStatusSender.sendConnectionFailed(client, context, e.getMessage(), "CONNECTION_ERROR");
        }
    }

    /**
     * 初始化连接上下文
     */
    private DeviceContext initializeConnectionContext(SocketIOClient client, String userId, ConnectRequest request) {
        DeviceContext context = new DeviceContext(client, userId, request);
        context.setStatus(DeviceContext.ContextStatus.CONNECTING);
        registerContext(context);

        // 检查是否有专有设备处理器，如果有则直接设置到上下文中
        String deviceModel = request.getDeviceProperties().getDeviceModel();
        //校验deviceModel是否为空
        if (deviceModel == null) {
            throw new IllegalArgumentException("设备型号不能为空");
        }


        // 对于已知的专有处理器设备，必须找到对应的处理器
        DeviceProcessor processor = deviceProcessorManager.getDeviceProcessor(deviceModel);
        if (processor == null) {
            // 注销已注册的上下文
            unregisterContext(context.getContextId());
            throw new IllegalStateException("未找到专有设备处理器: 设备型号=" + deviceModel + ", 用户=" + userId + ", 上下文=" + context.getContextId());
        }
        context.setDeviceProcessor(processor);
        log.info("设置专有设备处理器: 设备型号={}, 处理器={}", deviceModel, processor.getClass().getSimpleName());

        return context;
    }


    /**
     * 执行连接策略
     */
    private void executeConnectionStrategy(DeviceContext context, ConnectRequest request) {
        boolean connectionSuccessful;
        DeviceProperties deviceProperties = request.getDeviceProperties();
        String deviceModel = deviceProperties.getDeviceModel();

        if (context.hasDeviceProcessor()) {
            // 使用专有处理器处理串口设备连接
            DeviceProcessor processor = context.getDeviceProcessor();
            connectionSuccessful = processor.handleConnection(context);
            log.info("使用专有处理器处理连接: 设备类型={}, 处理器={}, 结果={}", deviceProperties.getActualDeviceModel(), processor.getClass().getSimpleName(), connectionSuccessful);
        } else {
            //直接抛出异常
            throw new RuntimeException("不支持的设备类型: " + deviceProperties.getDeviceModel());
        }

        if (!connectionSuccessful) {
            throw new RuntimeException("连接失败: " + deviceProperties.getDeviceModel());
        }
    }

    /**
     * 完成连接设置
     */
    private void finalizeConnection(DeviceContext context, DeviceProperties deviceProperties) {
        // 更新上下文状态
        context.setStatus(DeviceContext.ContextStatus.CONNECTED);
        context.setConnectionStartTime(System.currentTimeMillis());

        // 发送连接状态
        DeviceStatusSender.sendConnected(context.getClient(), context);

        // 自动开始测量（如果配置了）
        if (Boolean.TRUE.equals(deviceProperties.getAutoStartMeasurement())) {
            startMeasurement(context);
        }
    }


    /**
     * 开始测量
     */
    private void startMeasurement(DeviceContext context) {
        try {
            context.setStatus(DeviceContext.ContextStatus.MEASURING);
            context.setMeasurementStartTime(System.currentTimeMillis());

            DeviceProcessor processor = context.getDeviceProcessor();
            boolean measurementStarted = processor.sendCommand(context, "START_MEASUREMENT");

            // 3. 发送测量开始事件
            if (measurementStarted) {
                DeviceStatusSender.sendMeasurementStarted(context.getClient(), context);
                log.info("测量开始成功: {}", context.getContextDescription());
            } else {
                throw new RuntimeException("无法启动测量");
            }
        } catch (Exception e) {
            log.error("开始测量失败", e);
            context.setStatus(DeviceContext.ContextStatus.ERROR);
            context.incrementErrorCount("开始测量失败: " + e.getMessage());
            DeviceStatusSender.sendOperationStatus(context.getClient(), context, "START_MEASUREMENT", false, e.getMessage(), "MEASUREMENT_START_FAILED");
        }
    }


    /**
     * 处理开始测量请求
     */
    public void handleStartMeasurement(SocketIOClient client, Object requestObj, String userId) {
        try {
            // 解析请求
            String contextId = extractContextId(requestObj);
            if (contextId == null) {
                log.error("开始测量请求中缺少上下文ID: 用户={}", userId);
                return;
            }

            // 获取上下文
            DeviceContext context = activeContexts.get(contextId);
            if (context == null) {
                log.error("未找到上下文: 上下文ID={}, 用户={}", contextId, userId);
                return;
            }

            // 开始测量
            startMeasurement(context);

        } catch (Exception e) {
            log.error("处理开始测量失败: 用户={}", userId, e);
        }
    }

    /**
     * 处理停止测量请求
     */
    public void handleStopMeasurement(SocketIOClient client, Object requestObj, String userId) {
        try {
            // 解析请求
            String contextId = extractContextId(requestObj);
            if (contextId == null) {
                log.error("停止测量请求中缺少上下文ID: 用户={}", userId);
                return;
            }

            // 获取上下文
            DeviceContext context = activeContexts.get(contextId);
            if (context == null) {
                log.error("未找到上下文: 上下文ID={}, 用户={}", contextId, userId);
                return;
            }

            // 停止测量
            stopMeasurement(context);

        } catch (Exception e) {
            log.error("处理停止测量失败: 用户={}", userId, e);
        }
    }


    /**
     * 从请求对象中提取上下文ID
     */
    private String extractContextId(Object requestObj) {
        try {
            if (requestObj instanceof java.util.Map) {
                @SuppressWarnings("unchecked") java.util.Map<String, Object> map = (java.util.Map<String, Object>) requestObj;
                Object contextId = map.get("contextId");
                return contextId != null ? contextId.toString() : null;
            }
            return null;
        } catch (Exception e) {
            log.error("提取上下文ID失败", e);
            return null;
        }
    }


    /**
     * 停止测量
     */
    private void stopMeasurement(DeviceContext context) {
        try {
            context.setStatus(DeviceContext.ContextStatus.CONNECTED);
            DeviceProcessor processor = context.getDeviceProcessor();
            processor.sendCommand(context, "STOP_MEASUREMENT");
            log.debug("专有处理器停止测量: 处理器={}", processor.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("停止测量失败", e);
        }
    }


    /**
     * 注册上下文（通用版本）
     */
    private void registerContext(DeviceContext context) {
        activeContexts.put(context.getContextId(), context);

        // 生成通用设备标识符
        String deviceIdentifier = generateDeviceIdentifier(context);
        deviceToContextMapping.put(deviceIdentifier, context.getContextId());

        // 向后兼容：如果是串口设备，同时注册到端口映射
        if (context.getPortDescriptor() != null) {
            portToContextMapping.put(context.getPortDescriptor(), context.getContextId());
        }

        log.debug("注册设备上下文: 设备标识符={}, 上下文={}", deviceIdentifier, context.getContextDescription());
    }

    /**
     * 注销上下文（通用版本）
     */
    public void unregisterContext(String contextId) {
        DeviceContext context = activeContexts.remove(contextId);
        if (context != null) {
            // 生成设备标识符并从映射中移除
            String deviceIdentifier = generateDeviceIdentifier(context);
            deviceToContextMapping.remove(deviceIdentifier);

            // 向后兼容：如果是串口设备，同时从端口映射中移除
            if (context.getPortDescriptor() != null) {
                portToContextMapping.remove(context.getPortDescriptor());
            }

            log.debug("注销设备上下文: 设备标识符={}, 上下文={}", deviceIdentifier, context.getContextDescription());
        }
    }

    /**
     * 根据端口获取上下文（向后兼容）
     */
    public DeviceContext getContextByPort(String portDescriptor) {
        String contextId = portToContextMapping.get(portDescriptor);
        return contextId != null ? activeContexts.get(contextId) : null;
    }


    // =================== 设备标识符生成方法 ===================

    /**
     * 生成通用设备标识符（基于上下文）
     * 根据设备类型和通信方式生成唯一标识符
     */
    private String generateDeviceIdentifier(DeviceContext context) {
        // 优先检查是否有专有处理器
        String deviceModel = context.getDeviceProperties() != null ? context.getDeviceProperties().getDeviceModel() : context.getDeviceModel();
        if (deviceModel != null) {
            return "SPECIALIZED:" + deviceModel;
        }

        // 最后兜底：使用上下文ID
        return "CONTEXT:" + context.getContextId();
    }


    /**
     * 清理指定用户的所有串口资源
     * 在用户断开连接时自动调用，防止串口占用
     */
    public void cleanupUserResources(String userId) {
        log.info("开始清理用户资源: 用户={}", userId);

        try {
            // 1. 查找该用户的所有设备上下文
            List<DeviceContext> userContexts = activeContexts.values().stream().filter(context -> userId.equals(context.getSessionId())).collect(java.util.stream.Collectors.toList());

            log.info("找到用户{}的设备上下文数量: {}", userId, userContexts.size());

            // 2. 逐个清理设备上下文
            for (DeviceContext context : userContexts) {
                try {
                    String contextId = context.getContextId();
                    String portDescriptor = context.getPortDescriptor();

                    log.info("清理设备上下文: 上下文ID={}, 端口={}", contextId, portDescriptor);

                    // 使用设备处理器清理连接
                    if (context.getDeviceProcessor() != null) {
                        try {
                            context.getDeviceProcessor().handleDisconnection(context);
                            log.debug("设备处理器清理完成: 端口={}", portDescriptor);
                        } catch (Exception e) {
                            log.warn("设备处理器清理失败: 端口={}", portDescriptor, e);
                        }
                    }

                    // 强制关闭串口（如果存在）
                    forceCloseSerialPort(portDescriptor);

                    // 更新状态
                    context.setStatus(DeviceContext.ContextStatus.DISCONNECTED);

                    // 注销上下文
                    unregisterContext(contextId);

                    log.info("设备上下文清理完成: 上下文ID={}, 端口={}", contextId, portDescriptor);

                } catch (Exception e) {
                    log.error("清理设备上下文失败: 上下文ID={}", context.getContextId(), e);
                }
            }

            // 3. 清理设备会话管理器中的用户会话
            if (deviceSessionManager != null) {
                try {
                    deviceSessionManager.cleanupUserSessions(userId);
                    log.debug("设备会话管理器清理完成: 用户={}", userId);
                } catch (Exception e) {
                    log.warn("设备会话管理器清理失败: 用户={}", userId, e);
                }
            }

            log.info("用户资源清理完成: 用户={}", userId);

        } catch (Exception e) {
            log.error("清理用户资源失败: 用户={}", userId, e);
        }
    }

    /**
     * 处理断开端口连接请求
     */
    public void handleDisconnectPort(SocketIOClient client, Object requestObj, String userId) {
        try {
            DeviceContext context = null;
            String contextId = extractContextId(requestObj);


            context = activeContexts.get(contextId);


            if (context == null) {
                log.error("断开连接请求中未找到对应的设备上下文: 用户={}, 请求={}", userId, requestObj);
                throw new IllegalArgumentException("未找到设备上下文");
            }


            DeviceProcessor processor = context.getDeviceProcessor();
            boolean disconnected = processor.handleDisconnection(context);
            log.info("设备处理器断开连接: 处理器={}, 结果={}", processor.getClass().getSimpleName(), disconnected);


            // 更新状态
            context.setStatus(DeviceContext.ContextStatus.DISCONNECTED);

            // 注销上下文
            unregisterContext(context.getContextId());

            log.info("设备断开连接: 设备标识符={}, 用户={}", generateDeviceIdentifier(context), userId);

            // 发送断开状态
            DeviceStatusSender.sendDisconnected(client, context);
        } catch (Exception e) {
            exceptionHandler.handleGenericException(client, userId, e);
        }
    }

    /**
     * 处理发送命令请求
     */
    public void handleSendCommand(SocketIOClient client, SerialPortData message, String userId) {
        try {
            String portDescriptor = message.getPort();
            String command = message.getData();

            // 获取上下文
            DeviceContext context = getContextByPort(portDescriptor);


            // 使用设备处理器发送命令
            boolean success = false;
            if (context.hasDeviceProcessor()) {
                DeviceProcessor processor = context.getDeviceProcessor();
                success = processor.sendCommand(context, command);
                log.debug("设备处理器发送命令: 处理器={}, 命令={}, 结果={}", processor.getClass().getSimpleName(), command, success);
            } else {
                log.error("未找到设备处理器: 端口={}", portDescriptor);
            }

            // 发送命令状态

            DeviceStatusSender.sendCommandStatus(client, context, success, success ? "命令发送成功" : "命令发送失败");

            log.info("发送串口命令: 端口={}, 命令={}, 结果={}, 用户={}", portDescriptor, command, success, userId);
        } catch (Exception e) {
            log.error("处理发送命令失败: 用户={}", userId, e);
            exceptionHandler.handleGenericException(client, userId, e);
        }
    }

    /**
     * 处理重置设备请求
     */
    public void handleResetDevice(SocketIOClient client, Object requestObj, String userId) {
        try {
            // 解析请求
            String contextId = extractContextId(requestObj);

            DeviceContext context = null;
            if (contextId != null) {
                context = activeContexts.get(contextId);
            }
            if (context == null) {
                throw new IllegalArgumentException("没有找到设备上下文");
            }

            // 使用设备处理器重置设备
            boolean success = false;
            if (context.hasDeviceProcessor()) {
                DeviceProcessor processor = context.getDeviceProcessor();
                success = processor.sendCommand(context, "RESET_DEVICE");
                log.info("设备处理器重置设备: 处理器={}, 结果={}", processor.getClass().getSimpleName(), success);
            } else {
                log.error("未找到设备处理器进行重置");
            }

            DeviceStatusSender.sendResetStatus(client, context, success, success ? "设备重置成功" : "设备重置失败");
        } catch (Exception e) {
            log.error("处理重置设备失败: 用户={}", userId, e);
            exceptionHandler.handleGenericException(client, userId, e);
        }
    }

    /**
     * 处理校准设备请求
     */
    public void handleCalibrateDevice(SocketIOClient client, Object requestObj, String userId) {
        try {
            // 解析请求
            String contextId = extractContextId(requestObj);

            DeviceContext context = null;
            if (contextId != null) {
                context = activeContexts.get(contextId);
            }

            if (context == null) {
                throw new IllegalArgumentException("没有找到设备上下文");
            }

            // 使用设备处理器校准设备
            boolean success = false;
            if (context.hasDeviceProcessor()) {
                DeviceProcessor processor = context.getDeviceProcessor();
                success = processor.sendCommand(context, "CALIBRATE_DEVICE");
                log.info("设备处理器校准设备: 处理器={}, 结果={}", processor.getClass().getSimpleName(), success);
            } else {
                log.error("未找到设备处理器进行校准");
            }

            DeviceStatusSender.sendCalibrateStatus(client, context, success, success ? "设备校准成功" : "设备校准失败");
        } catch (Exception e) {
            exceptionHandler.handleGenericException(client, userId, e);
        }
    }

    /**
     * 处理查询测量结果请求
     */
    public void handleQueryResults(SocketIOClient client, ConnectRequest request, String userId) {
        DeviceContext context = null;
        try {
            log.info("处理查询测量结果请求: 用户={}, 设备={}", userId,
                request.getDeviceProperties() != null ? request.getDeviceProperties().getDeviceModel() : "未知");

            // 1. 创建查询上下文（临时上下文，不需要实际连接设备）
            context = createQueryContext(client, userId, request);

            // 2. 查找设备处理器
            String deviceModel = request.getDeviceProperties().getDeviceModel();
            if (deviceModel == null) {
                throw new IllegalArgumentException("设备型号不能为空");
            }

            DeviceProcessor processor = deviceProcessorManager.getDeviceProcessor(deviceModel);
            if (processor == null) {
                throw new IllegalStateException("未找到设备处理器: 设备型号=" + deviceModel);
            }
            context.setDeviceProcessor(processor);

            // 3. 校验设备是否支持查询功能
            if (!processor.supportsFeature("QUERY_RESULTS")) {
                throw new UnsupportedOperationException("设备不支持查询功能: " + deviceModel);
            }

            // 4. 发送查询开始状态
            DeviceStatusSender.sendQueryStarted(client, context);

            // 5. 发送QUERY_RESULTS指令，由设备处理器执行查询并发送数据
            boolean querySuccess = processor.sendCommand(context, "QUERY_RESULTS");

            if (querySuccess) {
                log.info("查询测量结果成功: 设备={}, 用户={}", deviceModel, userId);
                DeviceStatusSender.sendQueryStatus(client, context, true, "查询执行成功");
            } else {
                log.warn("查询测量结果失败: 设备={}, 用户={}", deviceModel, userId);
                DeviceStatusSender.sendQueryStatus(client, context, false, "查询执行失败");
            }
        } catch (Exception e) {
            log.error("处理查询测量结果失败: 用户={}", userId, e);
            if (context != null) {
                DeviceStatusSender.sendQueryStatus(client, context, false, "查询失败: " + e.getMessage());
            }
            exceptionHandler.handleGenericException(client, userId, e);
        }
    }

    /**
     * 创建查询上下文
     * 用于查询操作的临时上下文，不需要实际设备连接
     */
    private DeviceContext createQueryContext(SocketIOClient client, String userId, ConnectRequest request) {
        DeviceContext context = new DeviceContext(client, userId, request);
        context.setStatus(DeviceContext.ContextStatus.QUERYING);

        // 为查询操作生成临时上下文ID
        String queryContextId = "QUERY_" + System.currentTimeMillis() + "_" + userId;
        context.setContextId(queryContextId);

        log.debug("创建查询上下文: 上下文ID={}, 设备={}", queryContextId,
            request.getDeviceProperties() != null ? request.getDeviceProperties().getDeviceModel() : "未知");

        return context;
    }

    /**
     * 强制关闭指定端口的串口连接
     */
    private void forceCloseSerialPort(String portDescriptor) {
        if (portDescriptor == null || portDescriptor.trim().isEmpty()) {
            return;
        }

        try {
            log.debug("强制关闭串口: 端口={}", portDescriptor);

            // 获取串口实例并关闭
            com.fazecast.jSerialComm.SerialPort serialPort = com.fazecast.jSerialComm.SerialPort.getCommPort(portDescriptor);
            if (serialPort.isOpen()) {
                serialPort.closePort();
                log.info("强制关闭串口成功: 端口={}", portDescriptor);

                // 发布端口关闭事件
                eventPublisher.publishEvent(org.bj.debug.SerialPortDebugEvent.portClosed(this, portDescriptor));
            } else {
                log.debug("串口未打开或不存在: 端口={}", portDescriptor);
            }

        } catch (Exception e) {
            log.warn("强制关闭串口失败: 端口={}", portDescriptor, e);
        }
    }

}


