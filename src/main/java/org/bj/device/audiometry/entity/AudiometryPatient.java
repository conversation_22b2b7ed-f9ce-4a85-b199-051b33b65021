package org.bj.device.audiometry.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.bj.device.entity.PatientInfo;

/**
 * 电测听患者信息实体
 * 用于与OtoAccess ADI Server通信的患者数据结构
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Data
public class AudiometryPatient {
    
    /** 患者GUID - 固定使用UUID格式 */
    @JsonProperty("Guid")
    private String guid = "f0ac351b-3cc7-4484-8a1c-ac6dccc73dba";
    
    /** 患者ID */
    @JsonProperty("PatientId")
    private String patientId;
    
    /** 患者名字 */
    @JsonProperty("FirstName")
    private String firstName;
    
    /** 患者姓氏 */
    @JsonProperty("LastName")
    private String lastName;
    
    /** 出生日期 (格式: yyyy-MM-dd) */
    @JsonProperty("BirthDate")
    private String birthDate;
    
    /** 会话信息 - 通常为null */
    @JsonProperty("Sessions")
    private Object sessions = null;
    
    /**
     * 创建默认的电测听患者
     */
    public static AudiometryPatient createDefault() {
        AudiometryPatient patient = new AudiometryPatient();
        patient.setPatientId("0001");
        patient.setFirstName("测听");
        patient.setLastName("电");
        patient.setBirthDate("2024-01-01");
        return patient;
    }
    
    /**
     * 从体检患者信息创建电测听患者
     */
    public static AudiometryPatient fromPatientInfo(PatientInfo patientInfo) {
        AudiometryPatient patient = new AudiometryPatient();
        
        if (patientInfo != null) {
            patient.setPatientId(patientInfo.getExamNo() != null ? patientInfo.getExamNo() : "0001");
            
            // 处理姓名分割
            String fullName = patientInfo.getName();
            if (fullName != null && !fullName.trim().isEmpty()) {
                if (fullName.length() == 1) {
                    patient.setFirstName(fullName);
                    patient.setLastName("");
                } else if (fullName.length() == 2) {
                    patient.setFirstName(fullName.substring(1));
                    patient.setLastName(fullName.substring(0, 1));
                } else {
                    patient.setFirstName(fullName.substring(1));
                    patient.setLastName(fullName.substring(0, 1));
                }
            } else {
                patient.setFirstName("测听");
                patient.setLastName("电");
            }
            
            // 处理出生日期
            patient.setBirthDate(patientInfo.getBirthDate() != null ? 
                patientInfo.getBirthDate() : "2024-01-01");
        } else {
            // 使用默认值
            patient.setPatientId("0001");
            patient.setFirstName("测听");
            patient.setLastName("电");
            patient.setBirthDate("2024-01-01");
        }
        
        return patient;
    }
    
    /**
     * 获取完整姓名
     */
    public String getFullName() {
        if (lastName != null && firstName != null) {
            return lastName + firstName;
        } else if (firstName != null) {
            return firstName;
        } else if (lastName != null) {
            return lastName;
        } else {
            return "电测听";
        }
    }
}
