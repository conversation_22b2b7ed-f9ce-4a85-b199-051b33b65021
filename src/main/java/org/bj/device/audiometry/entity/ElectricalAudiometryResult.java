package org.bj.device.audiometry.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/12 18:03
 */
@Data
public class ElectricalAudiometryResult {

    //左耳气导基础数据
    private FrequencyData leftEarAirData;

    //左耳骨导基础数据
    private FrequencyData leftEarBoneData;

    //右耳气导基础数据
    private FrequencyData rightEarAirData;

    //右耳骨导基础数据
    private FrequencyData rightEarBoneData;

    // 左耳语频平均气导数据
    private Double leftWhisperFrequencyAirAvg;

    // 左耳高频平均气导数据
    private Double leftHighFrequencyAirAvg;

    // 左耳语频平均骨导数据
    private Double leftWhisperFrequencyBoneAvg;

    // 左耳高频平均骨导数据
    private Double leftHighFrequencyBoneAvg;

    // 右耳语频平均气导数据
    private Double rightWhisperFrequencyAirAvg;

    // 右耳高频平均气导数据
    private Double rightHighFrequencyAirAvg;

    // 右耳语频平均气导数据
    private Double rightWhisperFrequencyBoneAvg;

    // 右耳高频平均骨导数据
    private Double rightHighFrequencyBoneAvg;

    // 双耳语频平均听阈（气导）
    private Double bothWhisperFrequencyAirAvg;

    // 双耳高频平均听阈（气导）
    private Double bothHighFrequencyAirAvg;

    // 双耳语频平均听阈（骨导）
    private Double bothWhisperFrequencyBoneAvg;


    // 双耳高频平均听阈（骨导）
    private Double bothHighFrequencyBoneAvg;

}
