package org.bj.device.audiometry.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 电测听会话信息实体
 * 表示设备上的测试会话
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Data
public class AudiometrySession {

    /** 会话索引 */
    @JsonProperty("Index")
    private Integer index;

    /** 创建日期 */
    @JsonProperty("CreateDate")
    private String createDate;

    /** 模块名称 */
    @JsonProperty("ModuleName")
    private String moduleName;

    /** 患者唯一标识 */
    @JsonProperty("PatientGuid")
    private String patientGuid;

    /** 模块唯一标识（新增） */
    @JsonProperty("ModuleGuid")
    private String moduleGuid;

    /** 应用唯一标识（新增） */
    @JsonProperty("ApplicationGuid")
    private String applicationGuid;

    /** 版本号（新增） */
    @JsonProperty("Version")
    private String version;
    
    /**
     * 检查是否为听力测试会话
     */
    public boolean isAudiologySession() {
        return "AUD".equals(moduleName);
    }
    
    /**
     * 获取会话描述
     */
    public String getDescription() {
        return String.format("会话%d - %s (%s)", index, moduleName, createDate);
    }
}
