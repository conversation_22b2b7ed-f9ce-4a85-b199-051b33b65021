package org.bj.device.audiometry.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 电测听测试结果实体
 * 表示单个测试的结果数据
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Data
public class AudiometryTestResult {
    
    /** Base64编码的测试数据 */
    @JsonProperty("Data")
    private String data;
    
    /** 数据格式 */
    @JsonProperty("DataFormat")
    private String dataFormat;
    
    /** 测试名称 */
    @JsonProperty("Name")
    private String name;
    
    /** 测试侧别 */
    @JsonProperty("Side")
    private String side;
    
    /**
     * 检查是否为纯音测试
     */
    public boolean isToneTest() {
        return "Tone".equals(name);
    }
    
    /**
     * 获取测试描述
     */
    public String getDescription() {
        return String.format("%s测试 - %s", name, side);
    }
}
