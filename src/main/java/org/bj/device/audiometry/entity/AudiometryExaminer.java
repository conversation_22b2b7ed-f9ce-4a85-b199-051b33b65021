package org.bj.device.audiometry.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.bj.device.entity.OperatorInfo;

/**
 * 电测听检查员信息实体
 * 用于设置OtoAccess ADI Server的检查员信息
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Data
public class AudiometryExaminer {
    
    /** 检查员姓名 */
    @JsonProperty("Name")
    private String name;
    
    /** 检查员姓名缩写 */
    @JsonProperty("Initials")
    private String initials;
    
    /**
     * 创建默认的检查员信息
     */
    public static AudiometryExaminer createDefault() {
        AudiometryExaminer examiner = new AudiometryExaminer();
        examiner.setName("Name of Test Examiner");
        examiner.setInitials("Initials of Test Examiner");
        return examiner;
    }
    
    /**
     * 从操作员信息创建检查员
     */
    public static AudiometryExaminer fromOperatorInfo(OperatorInfo operatorInfo) {
        AudiometryExaminer examiner = new AudiometryExaminer();
        
        if (operatorInfo != null && operatorInfo.getName() != null) {
            examiner.setName(operatorInfo.getName());
            // 生成姓名缩写
            String name = operatorInfo.getName();
            if (name.length() >= 2) {
                examiner.setInitials(name.substring(0, 2));
            } else {
                examiner.setInitials(name);
            }
        } else {
            examiner.setName("Name of Test Examiner");
            examiner.setInitials("Initials of Test Examiner");
        }
        
        return examiner;
    }
}
