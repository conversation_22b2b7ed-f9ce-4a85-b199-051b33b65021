package org.bj.device.audiometry.entity;

import lombok.Data;

/**
 * 纯音测听数据点实体
 * 表示特定频率的听力测试结果
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Data
public class TonePoint {
    
    /** 频率 (Hz) */
    private Integer frequency;
    
    /** 强度MT */
    private Integer intensityMT;
    
    /** 强度MT掩蔽 */
    private Integer intensityMTMasked;
    
    /** 状态MT */
    private String statusMT;
    
    /** 强度UT */
    private Integer intensityUT;
    
    /** 状态UT */
    private String statusUT;
    
    /** 备注 */
    private String comment;
    
    /** 换能器 */
    private String transducer;

    private String description;

    private String heard;

    private String effectiveThreshold;

    private String valid;

    
    /**
     * 检查是否听到声音
     */
    public boolean isHeard() {
        return "Heard".equals(statusUT);
    }
    
    /**
     * 获取有效的听力阈值
     * 优先使用intensityUT，如果无效则使用intensityMT
     */
    public Integer getEffectiveThreshold() {
        if (intensityUT != null && intensityUT != -2147483648) {
            return intensityUT;
        } else if (intensityMT != null && intensityMT != -2147483648) {
            return intensityMT;
        }
        return null;
    }
    
    /**
     * 获取数据点描述
     */
    public String getDescription() {
        Integer threshold = getEffectiveThreshold();
        return String.format("%dHz: %s (%s)", 
            frequency, 
            threshold != null ? threshold + "dB" : "未测试",
            isHeard() ? "听到" : "未听到");
    }
    
    /**
     * 检查数据是否有效
     */
    public boolean isValid() {
        return frequency != null && frequency > 0 && getEffectiveThreshold() != null;
    }
}
