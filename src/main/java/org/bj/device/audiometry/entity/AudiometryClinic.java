package org.bj.device.audiometry.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 电测听诊所信息实体
 * 用于设置OtoAccess ADI Server的诊所信息
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Data
public class AudiometryClinic {
    
    /** 诊所名称 */
    @JsonProperty("Name")
    private String name;
    
    /** 诊所地址 */
    @JsonProperty("Address")
    private String address;
    
    /** 诊所地址2 */
    @JsonProperty("Address2")
    private String address2;
    
    /** 城市 */
    @JsonProperty("City")
    private String city;
    
    /** 省份/州 */
    @JsonProperty("State")
    private String state;
    
    /** 邮政编码 */
    @JsonProperty("ZipCode")
    private String zipCode;
    
    /** 国家 */
    @JsonProperty("Country")
    private String country;
    
    /** 电话 */
    @JsonProperty("Telephone")
    private String telephone;
    
    /** 传真 */
    @JsonProperty("Fax")
    private String fax;
    
    /** 邮箱 */
    @JsonProperty("Email")
    private String email;
    
    /** 税号 */
    @JsonProperty("VatNo")
    private String vatNo;
    
    /**
     * 创建默认的诊所信息
     */
    public static AudiometryClinic createDefault() {
        AudiometryClinic clinic = new AudiometryClinic();
        clinic.setName("Name of Test clinic");
        clinic.setAddress("Address of Test clinic");
        clinic.setAddress2("Address2 of Test clinic");
        clinic.setCity("City of Test clinic");
        clinic.setState("State of Test clinic");
        clinic.setZipCode("123-456");
        clinic.setCountry("Country of Test clinic");
        clinic.setTelephone("123456789");
        clinic.setFax("Fax of Test clinic");
        clinic.setEmail("<EMAIL>");
        clinic.setVatNo("12-34-56-78-90");
        return clinic;
    }
}
