package org.bj.device.audiometry.parser;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.w3c.dom.*;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.*;
import java.util.Base64;

import org.bj.device.audiometry.entity.TonePoint;

/**
 * 电测听数据解析器
 * 负责解析Base64编码的XML测试结果数据
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
public class AudiometryDataParser {
    
    /**
     * 解析Base64编码的测试数据
     * @param base64Data Base64编码的XML数据
     * @return 解析后的听力测试结果
     */
    public Map<String, List<TonePoint>> parseTestData(String base64Data) {
        try {
            // 1. Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
            String xmlData = new String(decodedBytes, "UTF-8");
            
            log.info("解码后的XML数据长度: {}", xmlData.length());
            
            // 2. 解析XML
            return parseXmlData(xmlData);
            
        } catch (Exception e) {
            log.error("解析测试数据失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 解析XML数据
     * @param xmlData XML字符串
     * @return 按耳侧和传导类型分组的听力数据
     */
    private Map<String, List<TonePoint>> parseXmlData(String xmlData) throws Exception {
        Map<String, List<TonePoint>> results = new HashMap<>();
        
        // 创建XML解析器
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(xmlData)));
        
        // 获取所有Measured节点
        NodeList measuredNodes = document.getElementsByTagName("Measured");
        
        for (int i = 0; i < measuredNodes.getLength(); i++) {
            Element measuredElement = (Element) measuredNodes.item(i);
            parseMeasuredElement(measuredElement, results);
        }
        
        log.info("解析完成，共获得{}组数据", results.size());
        return results;
    }
    
    /**
     * 解析单个Measured元素
     */
    private void parseMeasuredElement(Element measuredElement, Map<String, List<TonePoint>> results) {
        NodeList toneNodes = measuredElement.getElementsByTagName("Tone");
        
        for (int i = 0; i < toneNodes.getLength(); i++) {
            Element toneElement = (Element) toneNodes.item(i);
            parseToneElement(toneElement, results);
        }
    }
    
    /**
     * 解析单个Tone元素
     */
    private void parseToneElement(Element toneElement, Map<String, List<TonePoint>> results) {
        try {
            // 获取耳侧信息
            String earside = getElementText(toneElement, "Earside");
            if (earside == null || earside.trim().isEmpty()) {
                log.warn("未找到耳侧信息，跳过此Tone元素");
                return;
            }
            
            // 获取传导类型
            String conductionType = getElementText(toneElement, "ConductionTypes");
            if (conductionType == null || conductionType.trim().isEmpty()) {
                log.warn("未找到传导类型信息，跳过此Tone元素");
                return;
            }
            
            // 生成结果键
            String resultKey = earside + "_" + conductionType;
            
            // 解析TonePoint数据
            List<TonePoint> tonePoints = parseTonePoints(toneElement);
            
            if (!tonePoints.isEmpty()) {
                results.put(resultKey, tonePoints);
                log.debug("解析到{}数据: {}个频率点", resultKey, tonePoints.size());
            }
            
        } catch (Exception e) {
            log.error("解析Tone元素失败", e);
        }
    }
    
    /**
     * 解析TonePoint数据点
     */
    private List<TonePoint> parseTonePoints(Element toneElement) {
        List<TonePoint> tonePoints = new ArrayList<>();
        
        NodeList tonePointNodes = toneElement.getElementsByTagName("TonePoint");
        
        for (int i = 0; i < tonePointNodes.getLength(); i++) {
            Element tonePointElement = (Element) tonePointNodes.item(i);
            TonePoint tonePoint = parseSingleTonePoint(tonePointElement);
            
            if (tonePoint != null && tonePoint.isValid()) {
                tonePoints.add(tonePoint);
            }
        }
        
        // 按频率排序
        tonePoints.sort(Comparator.comparing(TonePoint::getFrequency));
        
        return tonePoints;
    }
    
    /**
     * 解析单个TonePoint
     */
    private TonePoint parseSingleTonePoint(Element tonePointElement) {
        try {
            TonePoint tonePoint = new TonePoint();
            
            // 解析各个字段
            tonePoint.setFrequency(parseIntegerValue(getElementText(tonePointElement, "Frequency")));
            tonePoint.setIntensityMT(parseIntegerValue(getElementText(tonePointElement, "IntensityMT")));
            tonePoint.setIntensityMTMasked(parseIntegerValue(getElementText(tonePointElement, "IntensityMTMasked")));
            tonePoint.setStatusMT(getElementText(tonePointElement, "StatusMT"));
            tonePoint.setIntensityUT(parseIntegerValue(getElementText(tonePointElement, "IntensityUT")));
            tonePoint.setStatusUT(getElementText(tonePointElement, "StatusUT"));
            tonePoint.setComment(getElementText(tonePointElement, "Comment"));
            tonePoint.setTransducer(getElementText(tonePointElement, "Transducer"));
            
            return tonePoint;
            
        } catch (Exception e) {
            log.error("解析TonePoint失败", e);
            return null;
        }
    }
    
    /**
     * 获取元素文本内容
     */
    private String getElementText(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            return node.getTextContent();
        }
        return null;
    }
    
    /**
     * 解析整数值，处理特殊值
     */
    private Integer parseIntegerValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        try {
            int intValue = Integer.parseInt(value.trim());
            // 处理特殊的无效值
            if (intValue == -2147483648) {
                return null;
            }
            return intValue;
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 格式化解析结果为可读字符串
     */
    public String formatResults(Map<String, List<TonePoint>> results) {
        StringBuilder sb = new StringBuilder();
        sb.append("电测听结果:\n");
        
        for (Map.Entry<String, List<TonePoint>> entry : results.entrySet()) {
            String key = entry.getKey();
            List<TonePoint> points = entry.getValue();
            
            sb.append(String.format("\n%s:\n", formatResultKey(key)));
            
            for (TonePoint point : points) {
                sb.append(String.format("  %s\n", point.getDescription()));
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化结果键为中文描述
     */
    private String formatResultKey(String key) {
        String[] parts = key.split("_");
        if (parts.length != 2) {
            return key;
        }
        
        String earside = "Left".equals(parts[0]) ? "左耳" : "Right".equals(parts[0]) ? "右耳" : parts[0];
        String conduction = "AC".equals(parts[1]) ? "气导" : "BC".equals(parts[1]) ? "骨导" : parts[1];
        
        return earside + conduction;
    }
    
    /**
     * 获取标准频率的听力阈值
     * @param results 解析结果
     * @param earside 耳侧 (Left/Right)
     * @param conductionType 传导类型 (AC/BC)
     * @param frequency 频率
     * @return 听力阈值，如果未找到返回null
     */
    public Integer getThresholdValue(Map<String, List<TonePoint>> results, 
                                   String earside, String conductionType, int frequency) {
        String key = earside + "_" + conductionType;
        List<TonePoint> points = results.get(key);
        
        if (points == null) {
            return null;
        }
        
        for (TonePoint point : points) {
            if (point.getFrequency() != null && point.getFrequency() == frequency) {
                return point.getEffectiveThreshold();
            }
        }
        
        return null;
    }
}
