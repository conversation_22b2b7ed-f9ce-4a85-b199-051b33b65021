package org.bj.device.audiometry.client;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.audiometry.entity.*;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 电测听设备HTTP客户端
 * 负责与OtoAccess ADI Server进行HTTP通信
 * 
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
public class AudiometryHttpClient {
    
    /** 默认服务器地址 */
    private static final String DEFAULT_BASE_URL = "http://localhost:47294";
    
    /** API密钥 */
    private static final String API_KEY = "ApiKey Public";
    
    /** HTTP客户端 */
    private final HttpClient httpClient;
    
    /** JSON处理器 */
    private final ObjectMapper objectMapper;
    
    /** 服务器基础URL */
    private String baseUrl;
    
    public AudiometryHttpClient() {
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
        this.objectMapper = new ObjectMapper();
        this.baseUrl = DEFAULT_BASE_URL;
    }
    
    /**
     * 设置服务器地址
     */
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
        log.info("设置电测听服务器地址: {}", baseUrl);
    }
    
    /**
     * 注册Hook钩子
     * @param portNumber 回调端口号
     * @return Hook URI
     */
    public String registerHook(int portNumber) throws Exception {
        String url = baseUrl + "/hooks?portNumber=" + portNumber;
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Accept", "application/json")
            .POST(HttpRequest.BodyPublishers.noBody())
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        if (response.statusCode() >= 300) {
            throw new RuntimeException("注册Hook失败: " + response.statusCode() + " - " + response.body());
        }
        
        log.info("Hook注册成功: 端口={}, 响应={}", portNumber, response.body());
        return response.body();
    }
    
    /**
     * 查询本地设备信息
     * @return 设备信息列表
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getApplications() throws Exception {
        String url = baseUrl + "/applications";
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Accept", "application/json")
            .GET()
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        if (response.statusCode() >= 300) {
            throw new RuntimeException("查询设备信息失败: " + response.statusCode() + " - " + response.body());
        }

        List<Map<String, Object>> applications = objectMapper.readValue(response.body(), 
            new TypeReference<List<Map<String, Object>>>() {});
        log.info("applications相关设备信息:{}",applications);
        log.info("查询到设备信息: 数量={}", applications.size());
        return applications;
    }
    
    /**
     * 设置诊所信息
     * @param clinic 诊所信息
     * @return 是否成功
     */
    public boolean setClinic(AudiometryClinic clinic) throws Exception {
        String url = baseUrl + "/clinic";
        String jsonBody = objectMapper.writeValueAsString(clinic);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        boolean success = response.statusCode() < 300;
        if (success) {
            log.info("设置诊所信息成功: {}", clinic.getName());
        } else {
            log.error("设置诊所信息失败: {} - {}", response.statusCode(), response.body());
        }
        
        return success;
    }
    
    /**
     * 设置检查员信息
     * @param examiner 检查员信息
     * @return 是否成功
     */
    public boolean setExaminer(AudiometryExaminer examiner) throws Exception {
        String url = baseUrl + "/examiner";
        String jsonBody = objectMapper.writeValueAsString(examiner);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        boolean success = response.statusCode() < 300;
        if (success) {
            log.info("设置检查员信息成功: {}", examiner.getName());
        } else {
            log.error("设置检查员信息失败: {} - {}", response.statusCode(), response.body());
        }
        
        return success;
    }
    
    /**
     * 清空患者信息
     * @return 是否成功
     */
    public boolean clearPatients() throws Exception {
        String url = baseUrl + "/patient";
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Accept", "application/json")
            .GET()
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        boolean success = response.statusCode() < 300;
        if (success) {
            log.info("清空患者信息成功");
        } else {
            log.error("清空患者信息失败: {} - {}", response.statusCode(), response.body());
        }
        
        return success;
    }
    
    /**
     * 添加患者信息
     * @param patient 患者信息
     * @return 是否成功
     */
    public boolean addPatient(AudiometryPatient patient) throws Exception {
        String url = baseUrl + "/patient";
        String jsonBody = objectMapper.writeValueAsString(patient);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        boolean success = response.statusCode() < 300;
        if (success) {
            log.info("添加患者信息成功: {} ({})", patient.getFullName(), patient.getPatientId());
        } else {
            log.error("添加患者信息失败: {} - {}", response.statusCode(), response.body());
        }
        
        return success;
    }

    /**
     * 获取会话信息
     * @return 会话列表
     */
    @SuppressWarnings("unchecked")
    public List<AudiometrySession> getSessions() throws Exception {
        String url = baseUrl + "/sessions";

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Accept", "application/json")
            .GET()
            .build();

        HttpResponse<String> response = httpClient.send(request,
            HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() >= 300) {
            throw new RuntimeException("获取会话信息失败: " + response.statusCode() + " - " + response.body());
        }
        log.info("response.body():{}",response.body());
        List<AudiometrySession> sessions = objectMapper.readValue(response.body(),
            new TypeReference<List<AudiometrySession>>() {});

        log.info("获取到会话信息: 数量={}", sessions.size());
        return sessions;
    }

    /**
     * 获取指定会话的测试结果
     * @param sessionIndex 会话索引
     * @return 测试结果列表
     */
    @SuppressWarnings("unchecked")
    public List<AudiometryTestResult> getSessionTests(int sessionIndex) throws Exception {
        String url = baseUrl + "/sessions/" + sessionIndex + "/tests";

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", API_KEY)
            .header("Accept", "application/json")
            .GET()
            .build();

        HttpResponse<String> response = httpClient.send(request,
            HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() >= 300) {
            throw new RuntimeException("获取测试结果失败: " + response.statusCode() + " - " + response.body());
        }

        List<AudiometryTestResult> tests = objectMapper.readValue(response.body(),
            new TypeReference<List<AudiometryTestResult>>() {});

        log.info("获取到测试结果: 会话={}, 数量={}", sessionIndex, tests.size());
        return tests;
    }

    /**
     * 启动设备软件
     * @param applicationPath 应用程序路径
     * @return 是否成功
     */
    public boolean startApplication(String applicationPath) {
        try {
            log.info("启动设备软件: {}", applicationPath);

            ProcessBuilder processBuilder = new ProcessBuilder(applicationPath,"-echo");
            Process process = processBuilder.start();

            // 等待一小段时间确保程序启动
            Thread.sleep(2000);

            log.info("设备软件启动成功");
            return true;

        } catch (Exception e) {
            log.error("启动设备软件失败: {}", applicationPath, e);
            return false;
        }
    }

    /**
     * 查找Diagnostic Suite设备信息
     * @return 设备信息，如果未找到返回null
     */
    public Map<String, Object> findDiagnosticSuite() throws Exception {
        List<Map<String, Object>> applications = getApplications();

        for (Map<String, Object> app : applications) {
            String name = (String) app.get("Name");
            log.info("设备名称: {}", name);
            if (name != null && name.contains("Diagnostic Suite")) {
                log.info("找到Diagnostic Suite设备: {}", name);
                return app;
            }
        }

        log.warn("未找到Diagnostic Suite设备");
        return null;
    }

    /**
     * 获取最新的会话
     * @return 最新会话，如果没有会话返回null
     */
    public AudiometrySession getLatestSession() throws Exception {
        log.info("获取最新的会话");
        List<AudiometrySession> sessions = getSessions();

        if (sessions.isEmpty()) {
            return null;
        }
        log.info("sessions对象: {}", sessions.get(0).toString());
        // 找到索引最大的会话
        AudiometrySession latestSession = sessions.get(0);
        for (AudiometrySession session : sessions) {
            if (session.getIndex() > latestSession.getIndex()) {
                latestSession = session;
            }
        }

        log.info("获取最新会话: {}", latestSession.getDescription());
        return latestSession;
    }

    /**
     * 检查服务器连接状态
     * @return 是否连接正常
     */
    public boolean checkConnection() {
        try {
            getApplications();
            return true;
        } catch (Exception e) {
            log.error("电测听服务器连接检查失败", e);
            return false;
        }
    }
}
