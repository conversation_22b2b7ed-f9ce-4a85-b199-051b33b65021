package org.bj.device;

import lombok.extern.slf4j.Slf4j;
import org.bj.device.processors.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 设备处理器工厂
 * 负责创建和管理设备处理器实例
 * 重构后使用独立的设备处理器类，提高代码的可维护性和可扩展性
 *
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Component
public class DeviceProcessorFactory {


    
    @Autowired(required = false)
    private ST150DeviceProcessor st150DeviceProcessor;

    @Autowired(required = false)
    private FGY200DeviceProcessor fgy200DeviceProcessor;

    @Autowired(required = false)
    private AudiometryDeviceProcessor audiometryDeviceProcessor;

    @Autowired
    private GenericDeviceProcessor genericDeviceProcessor;

    /**
     * 处理器类映射
     */
    private final ConcurrentMap<String, Class<? extends DeviceProcessor>> processorClasses = new ConcurrentHashMap<>();

    /**
     * 处理器实例缓存
     */
    private final ConcurrentMap<String, DeviceProcessor> processorCache = new ConcurrentHashMap<>();

    /**
     * 注册处理器类
     */
    public void registerProcessorClass(String deviceType, Class<? extends DeviceProcessor> processorClass) {
        processorClasses.put(deviceType.toUpperCase(), processorClass);
        log.info("注册设备处理器类: 设备类型={}, 处理器类={}", deviceType, processorClass.getSimpleName());
    }

    /**
     * 创建处理器实例
     */
    public DeviceProcessor createProcessor(String deviceModel) {
        if (deviceModel == null || deviceModel.trim().isEmpty()) {
            return null;
        }

        // 先从缓存中获取
        DeviceProcessor cachedProcessor = processorCache.get(deviceModel);
        if (cachedProcessor != null) {
            return cachedProcessor;
        }

        // 根据设备类型创建处理器
        DeviceProcessor processor = createProcessorByModel(deviceModel);

        if (processor != null) {
            // 初始化处理器
            try {
                processor.initialize();
                // 缓存处理器实例
                processorCache.put(deviceModel, processor);
                log.info("创建设备处理器成功: 设备类型={}, 处理器={}", deviceModel, processor.getClass().getSimpleName());
            } catch (Exception e) {
                log.error("初始化设备处理器失败: 设备类型={}", deviceModel, e);
                return null;
            }
        }

        return processor;
    }

    /**
     * 根据设备类型创建处理器
     */
    private DeviceProcessor createProcessorByModel(String deviceModel) {
        // 首先尝试从注册的处理器类中创建
        Class<? extends DeviceProcessor> processorClass = processorClasses.get(deviceModel.toUpperCase());
        if (processorClass != null) {
            try {
                return processorClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                log.error("创建注册的处理器实例失败: 设备类型={}, 处理器类={}", deviceModel, processorClass.getSimpleName(), e);
            }
        }

        // 使用Spring管理的处理器实例
        return switch (deviceModel.toUpperCase()) {
            case "心电图", "ECG" -> createECGProcessor();
            case "ST150肺功能", "ST150" -> st150DeviceProcessor;
            case "FGY-200肺功能", "FGY200", "健桥医电肺功能" -> fgy200DeviceProcessor;
            case "丹麦国际听力电测听", "AUDIOMETRY" -> audiometryDeviceProcessor;
            default -> genericDeviceProcessor;
        };
    }

    /**
     * 创建ECG处理器
     */
    private DeviceProcessor createECGProcessor() {
        try {
            // 尝试使用新的ECGDeviceProcessor
            Class<?> processorClass = Class.forName("org.bj.device.processors.ECGDeviceProcessor");
            return (DeviceProcessor) processorClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.warn("无法创建新的ECGDeviceProcessor，使用通用处理器: {}", e.getMessage());
            return genericDeviceProcessor;
        }
    }

    /**
     * 获取处理器实例（不创建新实例）
     */
    public DeviceProcessor getProcessor(String deviceType) {
        if (deviceType == null) {
            return null;
        }
        return processorCache.get(deviceType.toUpperCase());
    }

    /**
     * 移除处理器实例
     */
    public DeviceProcessor removeProcessor(String deviceType) {
        if (deviceType == null) {
            return null;
        }

        DeviceProcessor removed = processorCache.remove(deviceType.toUpperCase());
        if (removed != null) {
            try {
                removed.destroy();
                log.info("移除设备处理器: 设备类型={}", deviceType);
            } catch (Exception e) {
                log.error("销毁设备处理器失败: 设备类型={}", deviceType, e);
            }
        }
        return removed;
    }

    /**
     * 清空所有处理器
     */
    public void clearAllProcessors() {
        for (DeviceProcessor processor : processorCache.values()) {
            try {
                processor.destroy();
            } catch (Exception e) {
                log.error("销毁设备处理器失败", e);
            }
        }
        processorCache.clear();
        log.info("已清空所有设备处理器实例");
    }

    /**
     * 获取支持的设备类型
     */
    public java.util.Set<String> getSupportedDeviceTypes() {
        java.util.Set<String> types = new java.util.HashSet<>(processorClasses.keySet());
        types.addAll(java.util.Arrays.asList("心电图", "ST150肺功能", "普通串口"));
        return types;
    }

    /**
     * 检查是否支持指定设备类型
     */
    public boolean isSupported(String deviceType) {
        if (deviceType == null) {
            return false;
        }
        return getSupportedDeviceTypes().contains(deviceType.toUpperCase());
    }

    /**
     * 获取处理器数量
     */
    public int getProcessorCount() {
        return processorCache.size();
    }

    /**
     * 获取所有已注册的处理器类型
     */
    public java.util.Set<String> getRegisteredProcessorTypes() {
        return new java.util.HashSet<>(processorClasses.keySet());
    }

    /**
     * 获取所有已缓存的处理器类型
     */
    public java.util.Set<String> getCachedProcessorTypes() {
        return new java.util.HashSet<>(processorCache.keySet());
    }

    /**
     * 检查处理器是否已缓存
     */
    public boolean isProcessorCached(String deviceType) {
        return deviceType != null && processorCache.containsKey(deviceType.toUpperCase());
    }

    /**
     * 强制重新创建处理器
     */
    public DeviceProcessor recreateProcessor(String deviceModel) {
        if (deviceModel == null || deviceModel.trim().isEmpty()) {
            return null;
        }

        // 移除现有的处理器
        removeProcessor(deviceModel);

        // 重新创建处理器
        return createProcessor(deviceModel);
    }

    /**
     * 获取处理器统计信息
     */
    public ProcessorStatistics getStatistics() {
        ProcessorStatistics stats = new ProcessorStatistics();
        stats.setRegisteredCount(processorClasses.size());
        stats.setCachedCount(processorCache.size());
        stats.setSupportedDeviceTypes(getSupportedDeviceTypes());
        stats.setRegisteredTypes(getRegisteredProcessorTypes());
        stats.setCachedTypes(getCachedProcessorTypes());
        return stats;
    }

    /**
     * 处理器统计信息
     */
    public static class ProcessorStatistics {
        private int registeredCount;
        private int cachedCount;
        private java.util.Set<String> supportedDeviceTypes;
        private java.util.Set<String> registeredTypes;
        private java.util.Set<String> cachedTypes;

        // Getters and Setters
        public int getRegisteredCount() { return registeredCount; }
        public void setRegisteredCount(int registeredCount) { this.registeredCount = registeredCount; }
        public int getCachedCount() { return cachedCount; }
        public void setCachedCount(int cachedCount) { this.cachedCount = cachedCount; }
        public java.util.Set<String> getSupportedDeviceTypes() { return supportedDeviceTypes; }
        public void setSupportedDeviceTypes(java.util.Set<String> supportedDeviceTypes) { this.supportedDeviceTypes = supportedDeviceTypes; }
        public java.util.Set<String> getRegisteredTypes() { return registeredTypes; }
        public void setRegisteredTypes(java.util.Set<String> registeredTypes) { this.registeredTypes = registeredTypes; }
        public java.util.Set<String> getCachedTypes() { return cachedTypes; }
        public void setCachedTypes(java.util.Set<String> cachedTypes) { this.cachedTypes = cachedTypes; }

        @Override
        public String toString() {
            return String.format("ProcessorStatistics{registered=%d, cached=%d, supported=%d}", 
                registeredCount, cachedCount, supportedDeviceTypes != null ? supportedDeviceTypes.size() : 0);
        }
    }
}
