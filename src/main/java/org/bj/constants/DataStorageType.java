package org.bj.constants;

/**
 * 数据存储方式枚举
 * 
 * 定义设备数据的存储和传输方式
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public enum DataStorageType {
    
    /**
     * 直接传输模式
     * 
     * 数据直接传输到前端，不保存到数据库
     * 适用于实时监测、临时检测等场景
     */
    TRANSFER("transfer", "直接传输", "数据直接传输到前端，不保存到数据库"),
    
    /**
     * 数据库存储模式
     * 
     * 数据保存到数据库，前端通过查询获取
     * 适用于正式体检、需要持久化存储的场景
     */
    DB("db", "数据库存储", "数据保存到数据库，前端通过查询获取");
    
    /** 枚举值 */
    private final String value;
    
    /** 显示名称 */
    private final String displayName;
    
    /** 描述信息 */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param value 枚举值
     * @param displayName 显示名称
     * @param description 描述信息
     */
    DataStorageType(String value, String displayName, String description) {
        this.value = value;
        this.displayName = displayName;
        this.description = description;
    }
    
    /**
     * 获取枚举值
     * 
     * @return 枚举值
     */
    public String getValue() {
        return value;
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取描述信息
     * 
     * @return 描述信息
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static DataStorageType fromValue(String value) {
        if (value == null) {
            return null;
        }
        
        for (DataStorageType type : DataStorageType.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * 根据值获取枚举，如果不存在则返回默认值
     * 
     * @param value 枚举值
     * @param defaultValue 默认值
     * @return 对应的枚举或默认值
     */
    public static DataStorageType fromValueOrDefault(String value, DataStorageType defaultValue) {
        DataStorageType result = fromValue(value);
        return result != null ? result : defaultValue;
    }
    
    /**
     * 检查是否为直接传输模式
     * 
     * @return 是否为直接传输模式
     */
    public boolean isTransfer() {
        return this == TRANSFER;
    }
    
    /**
     * 检查是否为数据库存储模式
     * 
     * @return 是否为数据库存储模式
     */
    public boolean isDatabase() {
        return this == DB;
    }
    
    /**
     * 获取默认的数据存储方式
     * 
     * @return 默认的数据存储方式（直接传输）
     */
    public static DataStorageType getDefault() {
        return TRANSFER;
    }
    
    /**
     * 检查值是否有效
     * 
     * @param value 要检查的值
     * @return 是否为有效的枚举值
     */
    public static boolean isValid(String value) {
        return fromValue(value) != null;
    }
    
    /**
     * 获取所有可用的枚举值
     * 
     * @return 所有枚举值的数组
     */
    public static String[] getAllValues() {
        DataStorageType[] types = DataStorageType.values();
        String[] values = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            values[i] = types[i].getValue();
        }
        return values;
    }
    
    /**
     * 获取所有显示名称
     * 
     * @return 所有显示名称的数组
     */
    public static String[] getAllDisplayNames() {
        DataStorageType[] types = DataStorageType.values();
        String[] names = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            names[i] = types[i].getDisplayName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
