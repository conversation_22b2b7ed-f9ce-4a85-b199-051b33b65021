package org.bj.constants;

/**
 * 设备响应类型常量类
 * 
 * 定义设备响应的不同类型常量
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public final class ResponseTypeConstants {
    
    /**
     * 测量结果
     * 
     * 正常的设备测量数据响应，包含实际的测量值和相关信息
     */
    public static final String MEASUREMENT_RESULT = "DATA_RESULT";
    
    /**
     * 错误响应
     * 
     * 设备错误、连接失败等异常情况的响应
     */
    public static final String ERROR = "ERROR";
    
    /**
     * 状态更新
     * 
     * 设备状态变化通知，如设备连接、断开等状态信息
     */
    public static final String STATUS_UPDATE = "STATUS_UPDATE";
    
    /**
     * 校准结果
     * 
     * 设备校准操作的结果响应
     */
    public static final String CALIBRATION_RESULT = "CALIBRATION_RESULT";
    
    /**
     * 维护提醒
     * 
     * 设备维护、保养相关的提醒信息
     */
    public static final String MAINTENANCE_ALERT = "MAINTENANCE_ALERT";
    
    /**
     * 质量检查
     * 
     * 数据质量检查的结果响应
     */
    public static final String QUALITY_CHECK = "QUALITY_CHECK";
    
    /**
     * 批量完成
     * 
     * 批量测量或操作完成的通知
     */
    public static final String BATCH_COMPLETE = "BATCH_COMPLETE";
    
    /**
     * 设备信息
     * 
     * 设备信息查询的结果响应
     */
    public static final String DEVICE_INFO = "DEVICE_INFO";
    
    /**
     * 连接状态
     * 
     * 设备连接状态的响应
     */
    public static final String CONNECTION_STATUS = "CONNECTION_STATUS";
    
    /**
     * 数据传输
     * 
     * 数据传输过程中的状态响应
     */
    public static final String DATA_TRANSMISSION = "DATA_TRANSMISSION";
    
    /**
     * 默认响应类型
     */
    public static final String DEFAULT = MEASUREMENT_RESULT;
    
    /**
     * 所有可用的响应类型
     */
    public static final String[] ALL_TYPES = {
        MEASUREMENT_RESULT, ERROR, STATUS_UPDATE, CALIBRATION_RESULT,
        MAINTENANCE_ALERT, QUALITY_CHECK, BATCH_COMPLETE, DEVICE_INFO,
        CONNECTION_STATUS, DATA_TRANSMISSION
    };
    
    // 私有构造函数，防止实例化
    private ResponseTypeConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    /**
     * 检查响应类型是否有效
     * 
     * @param responseType 响应类型
     * @return 是否有效
     */
    public static boolean isValid(String responseType) {
        if (responseType == null) {
            return false;
        }
        
        for (String type : ALL_TYPES) {
            if (type.equalsIgnoreCase(responseType)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为测量结果类型
     * 
     * @param responseType 响应类型
     * @return 是否为测量结果类型
     */
    public static boolean isMeasurementResult(String responseType) {
        return MEASUREMENT_RESULT.equalsIgnoreCase(responseType);
    }
    
    /**
     * 检查是否为错误类型
     * 
     * @param responseType 响应类型
     * @return 是否为错误类型
     */
    public static boolean isError(String responseType) {
        return ERROR.equalsIgnoreCase(responseType);
    }
    
    /**
     * 检查是否为状态更新类型
     * 
     * @param responseType 响应类型
     * @return 是否为状态更新类型
     */
    public static boolean isStatusUpdate(String responseType) {
        return STATUS_UPDATE.equalsIgnoreCase(responseType);
    }
    
    /**
     * 检查是否为连接相关类型
     * 
     * @param responseType 响应类型
     * @return 是否为连接相关类型
     */
    public static boolean isConnectionRelated(String responseType) {
        return CONNECTION_STATUS.equalsIgnoreCase(responseType) || 
               STATUS_UPDATE.equalsIgnoreCase(responseType);
    }
    
    /**
     * 检查是否为数据相关类型
     * 
     * @param responseType 响应类型
     * @return 是否为数据相关类型
     */
    public static boolean isDataRelated(String responseType) {
        return MEASUREMENT_RESULT.equalsIgnoreCase(responseType) || 
               DATA_TRANSMISSION.equalsIgnoreCase(responseType) || 
               QUALITY_CHECK.equalsIgnoreCase(responseType);
    }
    
    /**
     * 检查是否为设备管理相关类型
     * 
     * @param responseType 响应类型
     * @return 是否为设备管理相关类型
     */
    public static boolean isDeviceManagementRelated(String responseType) {
        return CALIBRATION_RESULT.equalsIgnoreCase(responseType) || 
               MAINTENANCE_ALERT.equalsIgnoreCase(responseType) || 
               DEVICE_INFO.equalsIgnoreCase(responseType);
    }
    
    /**
     * 获取有效的响应类型，如果无效则返回默认值
     * 
     * @param responseType 响应类型
     * @return 有效的响应类型
     */
    public static String getValidOrDefault(String responseType) {
        return isValid(responseType) ? responseType.toUpperCase() : DEFAULT;
    }
    
    /**
     * 标准化响应类型字符串（转为大写）
     * 
     * @param responseType 响应类型
     * @return 标准化后的响应类型
     */
    public static String normalize(String responseType) {
        if (responseType == null) {
            return DEFAULT;
        }
        
        String normalized = responseType.toUpperCase().trim();
        return isValid(normalized) ? normalized : DEFAULT;
    }
    
    /**
     * 根据业务场景获取推荐的响应类型
     * 
     * @param scenario 业务场景
     * @return 推荐的响应类型
     */
    public static String getRecommendedType(String scenario) {
        if (scenario == null) {
            return DEFAULT;
        }
        
        return switch (scenario.toLowerCase()) {
            case "measurement", "test", "exam" -> MEASUREMENT_RESULT;
            case "error", "exception", "failure" -> ERROR;
            case "status", "state", "update" -> STATUS_UPDATE;
            case "calibration", "calibrate" -> CALIBRATION_RESULT;
            case "maintenance", "maintain" -> MAINTENANCE_ALERT;
            case "quality", "check", "validate" -> QUALITY_CHECK;
            case "batch", "bulk", "complete" -> BATCH_COMPLETE;
            case "info", "information", "detail" -> DEVICE_INFO;
            case "connection", "connect", "disconnect" -> CONNECTION_STATUS;
            case "transmission", "transfer", "upload" -> DATA_TRANSMISSION;
            default -> DEFAULT;
        };
    }
}
