package org.bj.constants;

/**
 * 数据存储方式常量类
 * 
 * 定义设备数据的存储和传输方式常量
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public final class DataStorageConstants {
    
    /**
     * 直接传输模式
     * 
     * 数据直接传输到前端，不保存到数据库
     * 适用于实时监测、临时检测等场景
     */
    public static final String TRANSFER = "transfer";
    
    /**
     * 数据库存储模式
     * 
     * 数据保存到数据库，前端通过查询获取
     * 适用于正式体检、需要持久化存储的场景
     */
    public static final String DB = "db";
    
    /**
     * 默认存储方式
     */
    public static final String DEFAULT = TRANSFER;
    
    /**
     * 所有可用的存储方式
     */
    public static final String[] ALL_TYPES = {TRANSFER, DB};
    
    // 私有构造函数，防止实例化
    private DataStorageConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    /**
     * 检查存储方式是否有效
     * 
     * @param storageType 存储方式
     * @return 是否有效
     */
    public static boolean isValid(String storageType) {
        if (storageType == null) {
            return false;
        }
        
        for (String type : ALL_TYPES) {
            if (type.equalsIgnoreCase(storageType)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为直接传输模式
     * 
     * @param storageType 存储方式
     * @return 是否为直接传输模式
     */
    public static boolean isTransfer(String storageType) {
        return TRANSFER.equalsIgnoreCase(storageType);
    }
    
    /**
     * 检查是否为数据库存储模式
     * 
     * @param storageType 存储方式
     * @return 是否为数据库存储模式
     */
    public static boolean isDatabase(String storageType) {
        return DB.equalsIgnoreCase(storageType);
    }
    
    /**
     * 获取有效的存储方式，如果无效则返回默认值
     * 
     * @param storageType 存储方式
     * @return 有效的存储方式
     */
    public static String getValidOrDefault(String storageType) {
        return isValid(storageType) ? storageType.toLowerCase() : DEFAULT;
    }
    
    /**
     * 标准化存储方式字符串（转为小写）
     * 
     * @param storageType 存储方式
     * @return 标准化后的存储方式
     */
    public static String normalize(String storageType) {
        if (storageType == null) {
            return DEFAULT;
        }
        
        String normalized = storageType.toLowerCase().trim();
        return isValid(normalized) ? normalized : DEFAULT;
    }
}
