package org.bj.constants;

/**
 * 数据状态常量类
 * 
 * 定义设备数据处理过程中的各种状态常量，确保状态值的一致性和可维护性
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public final class DataStatusConstants {
    
    // ==================== 基本数据状态 ====================
    
    /** 原始数据状态 - 刚从设备接收到的原始数据 */
    public static final String RAW = "RAW";
    
    /** 已处理状态 - 数据已经过解析和处理 */
    public static final String PROCESSED = "PROCESSED";
    
    /** 已验证状态 - 数据已通过验证规则检查 */
    public static final String VALIDATED = "VALIDATED";
    
    /** 错误状态 - 数据处理过程中发生错误 */
    public static final String ERROR = "ERROR";
    
    // ==================== 扩展数据状态 ====================
    
    /** 待处理状态 - 数据已接收但尚未开始处理 */
    public static final String PENDING = "PENDING";
    
    /** 处理中状态 - 数据正在处理过程中 */
    public static final String PROCESSING = "PROCESSING";
    
    /** 已保存到数据库状态 - 数据已成功保存到数据库 */
    public static final String SAVED_TO_DATABASE = "SAVED_TO_DATABASE";
    
    /** 已发送状态 - 数据已发送给前端或其他系统 */
    public static final String SENT = "SENT";
    
    /** 已完成状态 - 数据处理流程完全完成 */
    public static final String COMPLETED = "COMPLETED";
    
    /** 已取消状态 - 数据处理被取消 */
    public static final String CANCELLED = "CANCELLED";
    
    /** 超时状态 - 数据处理超时 */
    public static final String TIMEOUT = "TIMEOUT";
    
    /** 重试状态 - 数据处理失败后正在重试 */
    public static final String RETRYING = "RETRYING";
    
    // ==================== 默认值 ====================
    
    /** 默认数据状态 */
    public static final String DEFAULT = PROCESSED;
    
    // ==================== 状态数组 ====================
    
    /** 所有有效的数据状态 */
    public static final String[] ALL_STATUSES = {
        RAW, PROCESSED, VALIDATED, ERROR, PENDING, PROCESSING,
        SAVED_TO_DATABASE, SENT, COMPLETED, CANCELLED, TIMEOUT, RETRYING
    };
    
    /** 成功状态集合 */
    public static final String[] SUCCESS_STATUSES = {
        PROCESSED, VALIDATED, SAVED_TO_DATABASE, SENT, COMPLETED
    };
    
    /** 失败状态集合 */
    public static final String[] FAILURE_STATUSES = {
        ERROR, CANCELLED, TIMEOUT
    };
    
    /** 进行中状态集合 */
    public static final String[] IN_PROGRESS_STATUSES = {
        PENDING, PROCESSING, RETRYING
    };
    
    // ==================== 私有构造函数 ====================
    
    /**
     * 私有构造函数，防止实例化
     */
    private DataStatusConstants() {
        throw new UnsupportedOperationException("常量类不能被实例化");
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 检查给定的状态是否有效
     *
     * @param status 要检查的状态
     * @return 如果状态有效返回true，否则返回false
     */
    public static boolean isValidStatus(String status) {
        if (status == null) {
            return false;
        }
        
        for (String validStatus : ALL_STATUSES) {
            if (validStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取有效的状态值，如果无效则返回默认值
     *
     * @param status 要验证的状态
     * @return 有效的状态值或默认值
     */
    public static String getValidOrDefault(String status) {
        return isValidStatus(status) ? status : DEFAULT;
    }
    
    /**
     * 检查状态是否为成功状态
     *
     * @param status 要检查的状态
     * @return 如果是成功状态返回true，否则返回false
     */
    public static boolean isSuccessStatus(String status) {
        if (status == null) {
            return false;
        }
        
        for (String successStatus : SUCCESS_STATUSES) {
            if (successStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查状态是否为失败状态
     *
     * @param status 要检查的状态
     * @return 如果是失败状态返回true，否则返回false
     */
    public static boolean isFailureStatus(String status) {
        if (status == null) {
            return false;
        }
        
        for (String failureStatus : FAILURE_STATUSES) {
            if (failureStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查状态是否为进行中状态
     *
     * @param status 要检查的状态
     * @return 如果是进行中状态返回true，否则返回false
     */
    public static boolean isInProgressStatus(String status) {
        if (status == null) {
            return false;
        }
        
        for (String inProgressStatus : IN_PROGRESS_STATUSES) {
            if (inProgressStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查状态是否为原始数据状态
     *
     * @param status 要检查的状态
     * @return 如果是原始数据状态返回true，否则返回false
     */
    public static boolean isRaw(String status) {
        return RAW.equals(status);
    }
    
    /**
     * 检查状态是否为已处理状态
     *
     * @param status 要检查的状态
     * @return 如果是已处理状态返回true，否则返回false
     */
    public static boolean isProcessed(String status) {
        return PROCESSED.equals(status);
    }
    
    /**
     * 检查状态是否为已验证状态
     *
     * @param status 要检查的状态
     * @return 如果是已验证状态返回true，否则返回false
     */
    public static boolean isValidated(String status) {
        return VALIDATED.equals(status);
    }
    
    /**
     * 检查状态是否为错误状态
     *
     * @param status 要检查的状态
     * @return 如果是错误状态返回true，否则返回false
     */
    public static boolean isError(String status) {
        return ERROR.equals(status);
    }
    
    /**
     * 检查状态是否为已保存到数据库状态
     *
     * @param status 要检查的状态
     * @return 如果是已保存到数据库状态返回true，否则返回false
     */
    public static boolean isSavedToDatabase(String status) {
        return SAVED_TO_DATABASE.equals(status);
    }
    
    /**
     * 检查状态是否为已完成状态
     *
     * @param status 要检查的状态
     * @return 如果是已完成状态返回true，否则返回false
     */
    public static boolean isCompleted(String status) {
        return COMPLETED.equals(status);
    }
    
    /**
     * 获取状态的描述信息
     *
     * @param status 状态值
     * @return 状态描述
     */
    public static String getStatusDescription(String status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case RAW:
                return "原始数据";
            case PROCESSED:
                return "已处理";
            case VALIDATED:
                return "已验证";
            case ERROR:
                return "错误";
            case PENDING:
                return "待处理";
            case PROCESSING:
                return "处理中";
            case SAVED_TO_DATABASE:
                return "已保存到数据库";
            case SENT:
                return "已发送";
            case COMPLETED:
                return "已完成";
            case CANCELLED:
                return "已取消";
            case TIMEOUT:
                return "超时";
            case RETRYING:
                return "重试中";
            default:
                return "未知状态: " + status;
        }
    }
}
