package org.bj.constants;

/**
 * 设备相关常量定义
 * 
 * <AUTHOR> Agent
 * @date 2025-06-27
 */
public class DeviceConstants {
    
    // =================== 设备类型分类 ===================
    
    /** 串口设备 */
    public static final String DEVICE_TYPE_SERIAL = "串口设备";
    
    /** 其他设备 */
    public static final String DEVICE_TYPE_OTHER = "其他";
    
    // =================== 通信方式 ===================
    
    /** 串口通信 */
    public static final String COMMUNICATION_SERIAL = "SERIAL";
    
    /** HTTP通信 */
    public static final String COMMUNICATION_HTTP = "HTTP";
    
    /** USB通信 */
    public static final String COMMUNICATION_USB = "USB";
    
    /** 网络通信 */
    public static final String COMMUNICATION_NETWORK = "NETWORK";
    
    /** 蓝牙通信 */
    public static final String COMMUNICATION_BLUETOOTH = "BLUETOOTH";
    
    // =================== 串口设备类型 ===================
    
    /** BP10血压计 */
    public static final String COM_DEVICE_BP10 = "BP10";
    
    /** 心电图设备 */
    public static final String COM_DEVICE_ECG = "ECG";
    
    /** ST150肺功能设备 */
    public static final String COM_DEVICE_ST150 = "ST150";
    
    /** 肺功能设备（ST150别名） */
    public static final String COM_DEVICE_LUNG = "LUNG";
    
    /** 电子秤 */
    public static final String COM_DEVICE_ELECTRONIC_SCALE = "ELECTRONIC_SCALE";
    
    /** 体温计 */
    public static final String COM_DEVICE_THERMOMETER = "THERMOMETER";
    
    /** 身高仪 */
    public static final String COM_DEVICE_HEIGHT_METER = "HEIGHT_METER";
    
    /** 视力检测仪 */
    public static final String COM_DEVICE_VISION_TESTER = "VISION_TESTER";
    
    /** 血糖仪 */
    public static final String COM_DEVICE_GLUCOSE_METER = "GLUCOSE_METER";
    
    /** 血氧仪 */
    public static final String COM_DEVICE_OXIMETER = "OXIMETER";
    
    /** 听力计 */
    public static final String COM_DEVICE_AUDIOMETER = "AUDIOMETER";
    
    /** 肺活量计 */
    public static final String COM_DEVICE_SPIROMETER = "SPIROMETER";
    
    /** 通用设备 */
    public static final String COM_DEVICE_GENERIC = "普通串口";
    
    // =================== 非串口设备类型 ===================
    
    /** HTTP设备 */
    public static final String NON_SERIAL_HTTP_DEVICE = "HTTP_DEVICE";
    
    /** USB设备 */
    public static final String NON_SERIAL_USB_DEVICE = "USB_DEVICE";
    
    /** 网络设备 */
    public static final String NON_SERIAL_NETWORK_DEVICE = "NETWORK_DEVICE";
    
    /** 蓝牙设备 */
    public static final String NON_SERIAL_BLUETOOTH_DEVICE = "BLUETOOTH_DEVICE";
    
    // =================== 测量模式 ===================
    
    /** 手动模式 */
    public static final String MEASUREMENT_MODE_MANUAL = "MANUAL";
    
    /** 自动模式 */
    public static final String MEASUREMENT_MODE_AUTO = "AUTO";
    
    /** 连续模式 */
    public static final String MEASUREMENT_MODE_CONTINUOUS = "CONTINUOUS";
    
    // =================== 网络协议 ===================
    
    /** TCP协议 */
    public static final String NETWORK_PROTOCOL_TCP = "TCP";
    
    /** UDP协议 */
    public static final String NETWORK_PROTOCOL_UDP = "UDP";
    
    // =================== 工具方法 ===================
    
    /**
     * 判断是否为串口设备类型
     */
    public static boolean isSerialDeviceType(String deviceType) {
        return DEVICE_TYPE_SERIAL.equals(deviceType);
    }
    
    /**
     * 判断是否为其他设备类型
     */
    public static boolean isOtherDeviceType(String deviceType) {
        return DEVICE_TYPE_OTHER.equals(deviceType);
    }
    
    /**
     * 判断是否为串口通信方式
     */
    public static boolean isSerialCommunication(String communicationType) {
        return COMMUNICATION_SERIAL.equals(communicationType);
    }
    
    /**
     * 判断是否为HTTP通信方式
     */
    public static boolean isHttpCommunication(String communicationType) {
        return COMMUNICATION_HTTP.equals(communicationType);
    }
    
    /**
     * 判断是否为USB通信方式
     */
    public static boolean isUsbCommunication(String communicationType) {
        return COMMUNICATION_USB.equals(communicationType);
    }
    
    /**
     * 判断是否为网络通信方式
     */
    public static boolean isNetworkCommunication(String communicationType) {
        return COMMUNICATION_NETWORK.equals(communicationType);
    }
    
    /**
     * 判断是否为蓝牙通信方式
     */
    public static boolean isBluetoothCommunication(String communicationType) {
        return COMMUNICATION_BLUETOOTH.equals(communicationType);
    }
    
    /**
     * 根据通信方式获取默认设备类型
     */
    public static String getDefaultDeviceTypeByCommType(String communicationType) {
        if (communicationType == null) {
            return COM_DEVICE_GENERIC;
        }
        
        switch (communicationType.toUpperCase()) {
            case COMMUNICATION_HTTP:
                return NON_SERIAL_HTTP_DEVICE;
            case COMMUNICATION_USB:
                return NON_SERIAL_USB_DEVICE;
            case COMMUNICATION_NETWORK:
                return NON_SERIAL_NETWORK_DEVICE;
            case COMMUNICATION_BLUETOOTH:
                return NON_SERIAL_BLUETOOTH_DEVICE;
            case COMMUNICATION_SERIAL:
            default:
                return COM_DEVICE_GENERIC;
        }
    }
    
    /**
     * 验证设备类型和通信方式的匹配性
     */
    public static boolean isValidDeviceTypeAndCommType(String deviceType, String communicationType) {
        if (deviceType == null || communicationType == null) {
            return false;
        }
        
        // 串口设备必须使用串口通信
        if (DEVICE_TYPE_SERIAL.equals(deviceType)) {
            return COMMUNICATION_SERIAL.equals(communicationType);
        }
        
        // 其他设备不能使用串口通信
        if (DEVICE_TYPE_OTHER.equals(deviceType)) {
            return !COMMUNICATION_SERIAL.equals(communicationType);
        }
        
        return true;
    }
}
