package org.bj.constants;

/**
 * 事件名称常量类
 * 统一管理系统中所有的Socket.IO事件名称
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public final class EventConstants {
    
    // =================== 核心测试结果事件 ===================
    
    /**
     * 统一的测试结果事件
     * 所有设备的测试结果（主动查询和自动监听）都使用此事件
     */
    public static final String TEST_RESULT = "testResult";
    
    // =================== 设备连接相关事件 ===================
    
    /**
     * 设备连接成功事件
     */
    public static final String DEVICE_CONNECTED = "deviceConnected";
    
    /**
     * 设备断开连接事件
     */
    public static final String DEVICE_DISCONNECTED = "deviceDisconnected";
    
    /**
     * 设备连接错误事件
     */
    public static final String DEVICE_CONNECTION_ERROR = "deviceConnectionError";
    
    /**
     * 设备重置事件
     */
    public static final String DEVICE_RESET = "deviceReset";
    
    // =================== 测量控制相关事件 ===================
    
    /**
     * 测量开始事件
     */
    public static final String MEASUREMENT_STARTED = "measurementStarted";
    
    /**
     * 测量停止事件
     */
    public static final String MEASUREMENT_STOPPED = "measurementStopped";
    
    /**
     * 测量暂停事件
     */
    public static final String MEASUREMENT_PAUSED = "measurementPaused";
    
    /**
     * 测量恢复事件
     */
    public static final String MEASUREMENT_RESUMED = "measurementResumed";

    
    // =================== 查询操作相关事件 ===================
    
    /**
     * 查询操作响应事件（用于操作状态确认）
     */
    public static final String QUERY_OPERATION_RESPONSE = "queryOperationResponse";
    
    /**
     * 查询状态事件
     */
    public static final String QUERY_STATUS = "queryStatus";
    
    /**
     * 查询响应事件
     */
    public static final String QUERY_RESPONSE = "queryResponse";
    
    /**
     * 查询超时事件
     */
    public static final String QUERY_TIMEOUT = "queryTimeout";

    /**
     * 查询测量结果事件
     */
    public static final String QUERY_RESULTS = "queryResults";

    /**
     * 查询结果响应事件
     */
    public static final String QUERY_RESULTS_RESPONSE = "queryResultsResponse";

    // =================== 端口和串口相关事件 ===================
    
    /**
     * 端口状态事件（向后兼容）
     * @deprecated 建议使用 DEVICE_STATUS 替代
     */
    @Deprecated
    public static final String PORT_STATUS = "portStatus";

    /**
     * 统一设备状态事件（推荐使用）
     * 适用于所有类型的设备：串口、HTTP、USB、网络、蓝牙等
     */
    public static final String DEVICE_STATUS = "deviceStatus";
    
    /**
     * 端口命令状态事件
     */
    public static final String PORT_CMD_STATUS = "portCmdStatus";
    
    /**
     * 发送串口命令事件
     */
    public static final String SEND_SERIAL_COMMAND = "sendSerialCommand";

    /**
     * 命令执行结果事件
     */
    public static final String COMMAND_RESULT = "commandResult";

    /**
     * 串口数据事件（原始数据）
     */
    public static final String SERIAL_DATA = "serialData";
    
    // =================== 患者和体检相关事件 ===================

    /**
     * 患者关联事件
     */
    public static final String PATIENT_ASSOCIATED = "patientAssociated";

    /**
     * 体检项目更新事件
     */
    public static final String EXAM_ITEM_UPDATED = "examItemUpdated";

    // =================== 用户管理相关事件 ===================

    /**
     * 用户被踢掉事件
     */
    public static final String USER_KICKED = "userKicked";

    /**
     * 用户切换事件
     */
    public static final String USER_SWITCHED = "userSwitched";

    // =================== 设备操作相关事件 ===================

    /**
     * 设备操作结果事件
     */
    public static final String DEVICE_OPERATION_RESULT = "deviceOperationResult";

    /**
     * 阶段数据提取事件
     */
    public static final String STAGE_DATA_EXTRACTED = "stageDataExtracted";
    
    // =================== 错误和异常事件 ===================
    
    /**
     * 设备错误事件
     */
    public static final String DEVICE_ERROR = "deviceError";
    
    /**
     * 系统错误事件
     */
    public static final String SYSTEM_ERROR = "systemError";
    
    /**
     * 连接超时事件
     */
    public static final String CONNECTION_TIMEOUT = "connectionTimeout";
    
    // =================== 设备特定事件（保留用于兼容性） ===================


    /**
     * ST150设备连接事件
     * @deprecated 建议使用 DEVICE_CONNECTED 替代
     */
    @Deprecated
    public static final String ST150_DEVICE_CONNECTED = "st150DeviceConnected";
    
    /**
     * ST150设备断开事件
     * @deprecated 建议使用 DEVICE_DISCONNECTED 替代
     */
    @Deprecated
    public static final String ST150_DEVICE_DISCONNECTED = "st150DeviceDisconnected";
    
    /**
     * ST150患者关联事件
     * @deprecated 建议使用 PATIENT_ASSOCIATED 替代
     */
    @Deprecated
    public static final String ST150_PATIENT_ASSOCIATED = "st150PatientAssociated";
    
    // =================== 肺功能设备特定事件 ===================
    
    /**
     * 肺功能患者信息发送事件
     */
    public static final String LUNG_PATIENT_INFO_SENT = "lungPatientInfoSent";
    
    /**
     * 肺功能测试开始事件
     */
    public static final String LUNG_TEST_STARTED = "lungTestStarted";
    
    /**
     * 肺功能设备错误事件
     */
    public static final String LUNG_DEVICE_ERROR = "lungDeviceError";
    
    // =================== 工具方法 ===================
    
    /**
     * 检查是否为核心事件
     */
    public static boolean isCoreEvent(String eventName) {
        return TEST_RESULT.equals(eventName) ||
               DEVICE_CONNECTED.equals(eventName) ||
               DEVICE_DISCONNECTED.equals(eventName) ||
               MEASUREMENT_STARTED.equals(eventName);
    }
    
    /**
     * 检查是否为设备连接相关事件
     */
    public static boolean isConnectionEvent(String eventName) {
        return DEVICE_CONNECTED.equals(eventName) ||
               DEVICE_DISCONNECTED.equals(eventName) ||
               DEVICE_CONNECTION_ERROR.equals(eventName);
    }
    
    /**
     * 检查是否为测量相关事件
     */
    public static boolean isMeasurementEvent(String eventName) {
        return MEASUREMENT_STARTED.equals(eventName) ||
               MEASUREMENT_STOPPED.equals(eventName) ||
               MEASUREMENT_PAUSED.equals(eventName) ||
               MEASUREMENT_RESUMED.equals(eventName) ||
               TEST_RESULT.equals(eventName);
    }
    
    /**
     * 检查是否为查询相关事件
     */
    public static boolean isQueryEvent(String eventName) {
        return QUERY_OPERATION_RESPONSE.equals(eventName) ||
               QUERY_STATUS.equals(eventName) ||
               QUERY_RESPONSE.equals(eventName) ||
               QUERY_TIMEOUT.equals(eventName) ||
               QUERY_RESULTS.equals(eventName);
    }
    
    /**
     * 检查是否为错误相关事件
     */
    public static boolean isErrorEvent(String eventName) {
        return DEVICE_ERROR.equals(eventName) ||
               SYSTEM_ERROR.equals(eventName) ||
               DEVICE_CONNECTION_ERROR.equals(eventName) ||
               CONNECTION_TIMEOUT.equals(eventName);
    }
    
    // 私有构造函数，防止实例化
    private EventConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
