package org.bj.constants.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 用户切换事件
 * 用于解耦服务之间的循环依赖
 * 
 * <AUTHOR> Agent
 * @date 2025-06-29
 */
@Getter
public class UserSwitchEvent extends ApplicationEvent {

    /** 事件类型 */
    private final EventType eventType;
    
    /** 端口描述符 */
    private final String portDescriptor;
    
    /** 原用户ID */
    private final String oldUserId;
    
    /** 原患者姓名 */
    private final String oldPatientName;
    
    /** 新用户ID */
    private final String newUserId;
    
    /** 新患者姓名 */
    private final String newPatientName;
    
    /** 切换原因 */
    private final String reason;
    
    /** 事件时间戳 */
    private final long eventTimestamp;

    /**
     * 事件类型枚举
     */
    public enum EventType {
        /** 用户被踢掉 */
        USER_KICKED,
        /** 用户切换成功 */
        SWITCH_SUCCESS,
        /** 广播切换事件 */
        SWITCH_BROADCAST
    }

    /**
     * 构造函数
     */
    public UserSwitchEvent(Object source, EventType eventType, String portDescriptor, 
                          String oldUserId, String oldPatientName, 
                          String newUserId, String newPatientName, String reason) {
        super(source);
        this.eventType = eventType;
        this.portDescriptor = portDescriptor;
        this.oldUserId = oldUserId;
        this.oldPatientName = oldPatientName;
        this.newUserId = newUserId;
        this.newPatientName = newPatientName;
        this.reason = reason;
        this.eventTimestamp = System.currentTimeMillis();
    }

    /**
     * 创建用户被踢掉事件
     */
    public static UserSwitchEvent createUserKickedEvent(Object source, String portDescriptor,
                                                       String oldUserId, String oldPatientName,
                                                       String newUserId, String newPatientName, String reason) {
        return new UserSwitchEvent(source, EventType.USER_KICKED, portDescriptor,
                                 oldUserId, oldPatientName, newUserId, newPatientName, reason);
    }

    /**
     * 创建切换成功事件
     */
    public static UserSwitchEvent createSwitchSuccessEvent(Object source, String portDescriptor,
                                                          String oldUserId, String oldPatientName,
                                                          String newUserId, String newPatientName, String reason) {
        return new UserSwitchEvent(source, EventType.SWITCH_SUCCESS, portDescriptor,
                                 oldUserId, oldPatientName, newUserId, newPatientName, reason);
    }

    /**
     * 创建广播事件
     */
    public static UserSwitchEvent createBroadcastEvent(Object source, String portDescriptor,
                                                      String oldUserId, String oldPatientName,
                                                      String newUserId, String newPatientName, String reason) {
        return new UserSwitchEvent(source, EventType.SWITCH_BROADCAST, portDescriptor,
                                 oldUserId, oldPatientName, newUserId, newPatientName, reason);
    }

    /**
     * 获取事件描述
     */
    public String getEventDescription() {
        return String.format("%s: 端口=%s, 原会话=%s:%s, 新会话=%s:%s, 原因=%s",
                           eventType, portDescriptor, oldUserId, oldPatientName, 
                           newUserId, newPatientName, reason);
    }

    /**
     * 获取原会话描述
     */
    public String getOldSessionDescription() {
        return String.format("%s:%s", oldUserId, oldPatientName);
    }

    /**
     * 获取新会话描述
     */
    public String getNewSessionDescription() {
        return String.format("%s:%s", newUserId, newPatientName);
    }
}
