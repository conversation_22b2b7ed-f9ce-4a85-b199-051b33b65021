package org.bj.constants.event;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.bj.device.entity.ExamItemInfo;
import org.bj.device.entity.PatientInfo;

/**
 * 主动查询结果事件
 * 用于设备主动查询测量结果时的事件通知
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActiveQueryResultEvent {
    
    /** 事件类型 */
    public enum EventType {
        QUERY_STARTED("查询开始"),
        QUERY_SUCCESS("查询成功"),
        QUERY_FAILED("查询失败"),
        QUERY_TIMEOUT("查询超时"),
        QUERY_COMPLETED("查询完成"),
        QUERY_CANCELLED("查询取消");
        
        private final String description;
        
        EventType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /** 事件类型 */
    private EventType eventType;
    
    /** 上下文ID */
    private String contextId;
    
    /** 用户ID */
    private String userId;
    
    /** 设备ID */
    private String deviceId;
    
    /** 设备型号 */
    private String deviceModel;
    
    /** 设备类型 */
    private String deviceType;
    
    /** 端口描述符 */
    private String portDescriptor;
    
    /** 查询指令 */
    private String queryCommand;
    
    /** 查询结果数据 */
    private Object resultData;
    
    /** 查询次数 */
    private Integer queryAttempts;
    
    /** 最大查询次数 */
    private Integer maxAttempts;
    
    /** 查询间隔时间（毫秒） */
    private Long queryIntervalMs;
    
    /** 查询耗时（毫秒） */
    private Long elapsedTime;
    
    /** 事件时间戳 */
    @Builder.Default
    private Long timestamp = System.currentTimeMillis();
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 是否成功 */
    @Builder.Default
    private Boolean success = true;
    
    /** 患者信息 */
    private PatientInfo patientInfo;
    
    /** 体检项目信息 */
    private ExamItemInfo examItemInfo;
    
    /** 扩展属性 */
    private java.util.Map<String, Object> extraProperties;
    
    /**
     * 创建查询开始事件
     */
    public static ActiveQueryResultEvent queryStarted(String contextId, String userId, String deviceId, 
                                                     String deviceModel, String queryCommand) {
        return ActiveQueryResultEvent.builder()
                .eventType(EventType.QUERY_STARTED)
                .contextId(contextId)
                .userId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .queryCommand(queryCommand)
                .success(true)
                .build();
    }
    
    /**
     * 创建查询成功事件
     */
    public static ActiveQueryResultEvent querySuccess(String contextId, String userId, String deviceId, 
                                                     String deviceModel, String queryCommand, Object resultData,
                                                     Integer attempts, Long elapsedTime) {
        return ActiveQueryResultEvent.builder()
                .eventType(EventType.QUERY_SUCCESS)
                .contextId(contextId)
                .userId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .queryCommand(queryCommand)
                .resultData(resultData)
                .queryAttempts(attempts)
                .elapsedTime(elapsedTime)
                .success(true)
                .build();
    }
    
    /**
     * 创建查询失败事件
     */
    public static ActiveQueryResultEvent queryFailed(String contextId, String userId, String deviceId, 
                                                    String deviceModel, String queryCommand, String errorMessage,
                                                    Integer attempts, Long elapsedTime) {
        return ActiveQueryResultEvent.builder()
                .eventType(EventType.QUERY_FAILED)
                .contextId(contextId)
                .userId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .queryCommand(queryCommand)
                .errorMessage(errorMessage)
                .queryAttempts(attempts)
                .elapsedTime(elapsedTime)
                .success(false)
                .build();
    }
    
    /**
     * 创建查询超时事件
     */
    public static ActiveQueryResultEvent queryTimeout(String contextId, String userId, String deviceId, 
                                                     String deviceModel, String queryCommand, Integer attempts,
                                                     Integer maxAttempts, Long elapsedTime) {
        return ActiveQueryResultEvent.builder()
                .eventType(EventType.QUERY_TIMEOUT)
                .contextId(contextId)
                .userId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .queryCommand(queryCommand)
                .queryAttempts(attempts)
                .maxAttempts(maxAttempts)
                .elapsedTime(elapsedTime)
                .errorMessage("查询超时")
                .success(false)
                .build();
    }
    
    /**
     * 创建查询完成事件
     */
    public static ActiveQueryResultEvent queryCompleted(String contextId, String userId, String deviceId, 
                                                       String deviceModel, String queryCommand, Object resultData,
                                                       Integer totalAttempts, Long totalElapsedTime) {
        return ActiveQueryResultEvent.builder()
                .eventType(EventType.QUERY_COMPLETED)
                .contextId(contextId)
                .userId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .queryCommand(queryCommand)
                .resultData(resultData)
                .queryAttempts(totalAttempts)
                .elapsedTime(totalElapsedTime)
                .success(true)
                .build();
    }
    
    /**
     * 创建查询取消事件
     */
    public static ActiveQueryResultEvent queryCancelled(String contextId, String userId, String deviceId, 
                                                       String deviceModel, String queryCommand, Integer attempts,
                                                       Long elapsedTime) {
        return ActiveQueryResultEvent.builder()
                .eventType(EventType.QUERY_CANCELLED)
                .contextId(contextId)
                .userId(userId)
                .deviceId(deviceId)
                .deviceModel(deviceModel)
                .queryCommand(queryCommand)
                .queryAttempts(attempts)
                .elapsedTime(elapsedTime)
                .errorMessage("查询被取消")
                .success(false)
                .build();
    }
    
    /**
     * 添加扩展属性
     */
    public ActiveQueryResultEvent addExtraProperty(String key, Object value) {
        if (this.extraProperties == null) {
            this.extraProperties = new java.util.HashMap<>();
        }
        this.extraProperties.put(key, value);
        return this;
    }
    
    /**
     * 设置患者信息
     */
    public ActiveQueryResultEvent withPatientInfo(PatientInfo patientInfo) {
        this.patientInfo = patientInfo;
        return this;
    }
    
    /**
     * 设置体检项目信息
     */
    public ActiveQueryResultEvent withExamItemInfo(ExamItemInfo examItemInfo) {
        this.examItemInfo = examItemInfo;
        return this;
    }
    
    /**
     * 转换为Map格式（用于前端事件）
     */
    public java.util.Map<String, Object> toMap() {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        result.put("eventType", eventType.name());
        result.put("eventDescription", eventType.getDescription());
        result.put("contextId", contextId);
        result.put("userId", userId);
        result.put("deviceId", deviceId);
        result.put("deviceModel", deviceModel);
        result.put("deviceType", deviceType);
        result.put("portDescriptor", portDescriptor);
        result.put("queryCommand", queryCommand);
        result.put("queryAttempts", queryAttempts);
        result.put("maxAttempts", maxAttempts);
        result.put("queryIntervalMs", queryIntervalMs);
        result.put("elapsedTime", elapsedTime);
        result.put("timestamp", timestamp);
        result.put("success", success);
        
        if (resultData != null) {
            result.put("resultData", resultData);
        }
        if (errorMessage != null) {
            result.put("errorMessage", errorMessage);
        }
        if (patientInfo != null) {
            result.put("patientInfo", patientInfo);
        }
        if (examItemInfo != null) {
            result.put("examItemInfo", examItemInfo);
        }
        if (extraProperties != null && !extraProperties.isEmpty()) {
            result.put("extraProperties", extraProperties);
        }
        
        return result;
    }
    
    /**
     * 获取事件描述
     */
    public String getEventDescription() {
        return eventType.getDescription();
    }
    
    /**
     * 检查是否为成功事件
     */
    public boolean isSuccessEvent() {
        return success != null && success;
    }
    
    /**
     * 检查是否为错误事件
     */
    public boolean isErrorEvent() {
        return success != null && !success;
    }
    
    /**
     * 检查是否为完成事件（成功或失败）
     */
    public boolean isCompletionEvent() {
        return eventType == EventType.QUERY_SUCCESS || 
               eventType == EventType.QUERY_FAILED || 
               eventType == EventType.QUERY_TIMEOUT ||
               eventType == EventType.QUERY_COMPLETED ||
               eventType == EventType.QUERY_CANCELLED;
    }
}
