package org.bj.constants;

/**
 * 设备响应类型枚举
 * 
 * 定义设备响应的不同类型，用于区分不同种类的设备响应数据
 * 
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
public enum ResponseType {
    
    /**
     * 测量结果
     * 
     * 正常的设备测量数据响应，包含实际的测量值和相关信息
     * 适用于各种设备的测量数据返回
     */
    MEASUREMENT_RESULT("MEASUREMENT_RESULT", "测量结果", "正常的设备测量数据响应"),
    
    /**
     * 错误响应
     * 
     * 设备错误、连接失败等异常情况的响应
     * 包含错误信息和错误详情
     */
    ERROR("ERROR", "错误响应", "设备错误、连接失败等异常情况的响应"),
    
    /**
     * 状态更新
     * 
     * 设备状态变化通知，如设备连接、断开等状态信息
     * 用于实时更新设备状态
     */
    STATUS_UPDATE("STATUS_UPDATE", "状态更新", "设备状态变化通知"),
    
    /**
     * 校准结果
     * 
     * 设备校准操作的结果响应
     * 包含校准状态和校准数据
     */
    CALIBRATION_RESULT("CALIBRATION_RESULT", "校准结果", "设备校准操作的结果响应"),
    
    /**
     * 维护提醒
     * 
     * 设备维护、保养相关的提醒信息
     * 用于设备生命周期管理
     */
    MAINTENANCE_ALERT("MAINTENANCE_ALERT", "维护提醒", "设备维护、保养相关的提醒信息"),
    
    /**
     * 质量检查
     * 
     * 数据质量检查的结果响应
     * 包含数据质量评估信息
     */
    QUALITY_CHECK("QUALITY_CHECK", "质量检查", "数据质量检查的结果响应"),
    
    /**
     * 批量完成
     * 
     * 批量测量或操作完成的通知
     * 用于批量处理场景
     */
    BATCH_COMPLETE("BATCH_COMPLETE", "批量完成", "批量测量或操作完成的通知"),
    
    /**
     * 设备信息
     * 
     * 设备信息查询的结果响应
     * 包含设备详细信息和配置
     */
    DEVICE_INFO("DEVICE_INFO", "设备信息", "设备信息查询的结果响应"),
    
    /**
     * 连接状态
     * 
     * 设备连接状态的响应
     * 用于连接建立和断开的通知
     */
    CONNECTION_STATUS("CONNECTION_STATUS", "连接状态", "设备连接状态的响应"),
    
    /**
     * 数据传输
     * 
     * 数据传输过程中的状态响应
     * 用于大数据量传输的进度通知
     */
    DATA_TRANSMISSION("DATA_TRANSMISSION", "数据传输", "数据传输过程中的状态响应");
    
    /** 枚举值 */
    private final String value;
    
    /** 显示名称 */
    private final String displayName;
    
    /** 描述信息 */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param value 枚举值
     * @param displayName 显示名称
     * @param description 描述信息
     */
    ResponseType(String value, String displayName, String description) {
        this.value = value;
        this.displayName = displayName;
        this.description = description;
    }
    
    /**
     * 获取枚举值
     * 
     * @return 枚举值
     */
    public String getValue() {
        return value;
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取描述信息
     * 
     * @return 描述信息
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ResponseType fromValue(String value) {
        if (value == null) {
            return null;
        }
        
        for (ResponseType type : ResponseType.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * 根据值获取枚举，如果不存在则返回默认值
     * 
     * @param value 枚举值
     * @param defaultValue 默认值
     * @return 对应的枚举或默认值
     */
    public static ResponseType fromValueOrDefault(String value, ResponseType defaultValue) {
        ResponseType result = fromValue(value);
        return result != null ? result : defaultValue;
    }
    
    /**
     * 检查是否为测量结果类型
     * 
     * @return 是否为测量结果类型
     */
    public boolean isMeasurementResult() {
        return this == MEASUREMENT_RESULT;
    }
    
    /**
     * 检查是否为错误类型
     * 
     * @return 是否为错误类型
     */
    public boolean isError() {
        return this == ERROR;
    }
    
    /**
     * 检查是否为状态更新类型
     * 
     * @return 是否为状态更新类型
     */
    public boolean isStatusUpdate() {
        return this == STATUS_UPDATE;
    }
    
    /**
     * 检查是否为连接相关类型
     * 
     * @return 是否为连接相关类型
     */
    public boolean isConnectionRelated() {
        return this == CONNECTION_STATUS || this == STATUS_UPDATE;
    }
    
    /**
     * 检查是否为数据相关类型
     * 
     * @return 是否为数据相关类型
     */
    public boolean isDataRelated() {
        return this == MEASUREMENT_RESULT || this == DATA_TRANSMISSION || this == QUALITY_CHECK;
    }
    
    /**
     * 获取默认的响应类型
     * 
     * @return 默认的响应类型（测量结果）
     */
    public static ResponseType getDefault() {
        return MEASUREMENT_RESULT;
    }
    
    /**
     * 检查值是否有效
     * 
     * @param value 要检查的值
     * @return 是否为有效的枚举值
     */
    public static boolean isValid(String value) {
        return fromValue(value) != null;
    }
    
    /**
     * 获取所有可用的枚举值
     * 
     * @return 所有枚举值的数组
     */
    public static String[] getAllValues() {
        ResponseType[] types = ResponseType.values();
        String[] values = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            values[i] = types[i].getValue();
        }
        return values;
    }
    
    /**
     * 获取所有显示名称
     * 
     * @return 所有显示名称的数组
     */
    public static String[] getAllDisplayNames() {
        ResponseType[] types = ResponseType.values();
        String[] names = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            names[i] = types[i].getDisplayName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
