package org.bj;

import com.corundumstudio.socketio.SocketIOServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration.class,
    org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration.class
})
@Slf4j
public class ComDataReader {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(ComDataReader.class);
        // 明确禁用Web环境
        app.setWebApplicationType(WebApplicationType.NONE);
        app.run(args);
    }
}
