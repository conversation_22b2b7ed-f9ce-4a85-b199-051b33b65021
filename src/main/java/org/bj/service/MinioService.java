package org.bj.service;

import io.minio.*;
import io.minio.http.Method;
import io.minio.PostPolicy;
import org.bj.config.MinioConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.*;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * MinIO对象存储服务
 * <p>
 * 提供文件上传、下载、删除等功能
 *
 * <AUTHOR> Agent
 * @date 2025-07-05
 */
@Service
@ConditionalOnProperty(prefix = "minio", name = "enabled", havingValue = "true")
public class MinioService {

    private static final Logger logger = LoggerFactory.getLogger(MinioService.class);

    // MinIO最佳实践常量
    private static final long LARGE_FILE_THRESHOLD = 64 * 1024 * 1024; // 64MB - 大文件阈值
    private static final long PART_SIZE = 16 * 1024 * 1024; // 16MB - 分片大小

    @Autowired(required = false)
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    /**
     * 上传文件 - 使用MinIO最佳实践
     * 自动选择单文件上传或分片上传
     *
     * @param file       文件
     * @param objectName 对象名称（可选，为空时自动生成）
     * @return 上传结果信息
     */
    public Map<String, Object> uploadFile(File file, String objectName) {
        return uploadFile(file, objectName, null);
    }

    /**
     * 上传文件 - 带自定义元数据
     *
     * @param file       文件
     * @param objectName 对象名称
     * @param metadata   自定义元数据
     * @return 上传结果信息
     */
    public Map<String, Object> uploadFile(File file, String objectName, Map<String, String> metadata) {
        return executeWithRetry(() -> uploadFileInternal(file, objectName, metadata),
            "上传文件: " + file.getName());
    }

    /**
     * 内部文件上传方法 - 实现MinIO最佳实践
     */
    private Map<String, Object> uploadFileInternal(File file, String objectName, Map<String, String> metadata) throws Exception {
        // 验证文件
        UploadValidationResult validation = validateFileForUpload(file);
        if (!validation.isValid()) {
            return createErrorResult(validation.getErrorMessage());
        }

        String bucketName = minioConfig.getDefaultBucket();
        ensureBucketExists(bucketName);

        long fileSize = file.length();
        String contentType = getContentType(file.getName());

        // 计算文件MD5用于完整性校验
        String md5Hash = calculateFileMD5(file);

        // 构建元数据
        Map<String, String> objectMetadata = buildObjectMetadata(file, metadata, md5Hash);

        Map<String, Object> result;

        // 根据文件大小选择上传策略
        if (fileSize >= LARGE_FILE_THRESHOLD) {
            logger.info("文件大小{}MB，使用大文件优化上传: {}", fileSize / (1024 * 1024), file.getName());
            result = uploadLargeFile(file, bucketName, objectName, contentType, objectMetadata);
        } else {
            logger.info("文件大小{}KB，使用标准上传: {}", fileSize / 1024, file.getName());
            result = uploadSmallFile(file, bucketName, objectName, contentType, objectMetadata);
        }

        // 添加通用结果信息
        if (Boolean.TRUE.equals(result.get("success"))) {
            result.put("fileSize", fileSize);
            result.put("uploadTime", ZonedDateTime.now().toString());
            result.put("downloadUrl", getDownloadUrl(bucketName, objectName));
            result.put("md5Hash", md5Hash);
            result.put("uploadMethod", fileSize >= LARGE_FILE_THRESHOLD ? "large-file" : "standard");
        }

        return result;
    }

    /**
     * 小文件上传（单次上传）
     */
    private Map<String, Object> uploadSmallFile(File file, String bucketName, String objectName,
                                               String contentType, Map<String, String> metadata) throws Exception {
        try (InputStream inputStream = new BufferedInputStream(new FileInputStream(file))) {
            PutObjectArgs.Builder argsBuilder = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, file.length(), -1)
                    .contentType(contentType);

            // 添加元数据
            if (metadata != null && !metadata.isEmpty()) {
                argsBuilder.userMetadata(metadata);
            }

            ObjectWriteResponse response = minioClient.putObject(argsBuilder.build());

            logger.info("小文件上传成功: bucket={}, object={}, size={}KB",
                bucketName, objectName, file.length() / 1024);

            return createSuccessResult(bucketName, objectName, response.etag());
        }
    }

    /**
     * 大文件上传 - 使用单次上传但优化缓冲区
     */
    private Map<String, Object> uploadLargeFile(File file, String bucketName, String objectName,
                                               String contentType, Map<String, String> metadata) throws Exception {
        long fileSize = file.length();

        logger.info("开始大文件上传: 文件大小={}MB", fileSize / (1024 * 1024));

        // 使用更大的缓冲区提高大文件上传性能
        try (InputStream inputStream = new BufferedInputStream(new FileInputStream(file), 64 * 1024)) {
            PutObjectArgs.Builder argsBuilder = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, fileSize, PART_SIZE) // 使用PART_SIZE作为分片大小
                    .contentType(contentType);

            // 添加元数据
            if (metadata != null && !metadata.isEmpty()) {
                argsBuilder.userMetadata(metadata);
            }

            ObjectWriteResponse response = minioClient.putObject(argsBuilder.build());

            logger.info("大文件上传成功: bucket={}, object={}, size={}MB",
                bucketName, objectName, fileSize / (1024 * 1024));

            return createSuccessResult(bucketName, objectName, response.etag());
        }
    }

    /**
     * 上传字节数组 - 最简化版本
     */
    public Map<String, Object> uploadBytes(byte[] data, String fileName, String objectName) {
        return uploadBytesSimple(data, fileName, objectName);
    }

    /**
     * 上传字节数组 - 带元数据
     */
    public Map<String, Object> uploadBytes(byte[] data, String fileName, String objectName, Map<String, String> metadata) {
        return uploadBytesSimple(data, fileName, objectName);
    }

    /**
     * 字节数组上传 - 使用POST方法绕过网络设备的PUT限制
     */
    private Map<String, Object> uploadBytesSimple(byte[] data, String fileName, String objectName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 基本验证
            if (data == null || data.length == 0) {
                result.put("success", false);
                result.put("message", "数据为空");
                return result;
            }

            String bucketName = minioConfig.getDefaultBucket();
            ensureBucketExists(bucketName);

            logger.info("使用POST方法上传文件，绕过网络设备PUT限制: {}", fileName);

            // 使用POST方法替代PUT方法
            result = uploadUsingPostMethod(data, fileName, bucketName, objectName);

            if (Boolean.TRUE.equals(result.get("success"))) {
                logger.info("POST方法上传成功: bucket={}, object={}, size={}KB",
                    bucketName, objectName, data.length / 1024);
            }

            return result;

        } catch (Exception e) {
            logger.error("POST方法上传失败: {}, 错误详情: {}", fileName, e.toString(), e);

            result.put("success", false);
            result.put("message", "上传失败: " + e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("uploadMethod", "POST");
            return result;
        }
    }

    /**
     * 使用POST方法上传文件 - 绕过网络设备限制
     */
    private Map<String, Object> uploadUsingPostMethod(byte[] data, String fileName, String bucketName, String objectName) {
        try {
            // 使用MinIO的PostPolicy进行上传
            PostPolicy policy = new PostPolicy(bucketName, ZonedDateTime.now().plusHours(1));
            policy.addEqualsCondition("key", objectName);
            policy.addContentLengthRangeCondition(1, data.length);

            Map<String, String> formData = minioClient.getPresignedPostFormData(policy);

            // 使用HTTP POST上传
            String uploadUrl = minioConfig.getEndpoint() + "/" + bucketName;
            Map<String, Object> uploadResult = performPostUpload(uploadUrl, formData, data, objectName);

            if (Boolean.TRUE.equals(uploadResult.get("success"))) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("bucketName", bucketName);
                result.put("objectName", objectName);
                result.put("fileSize", data.length);
                result.put("uploadTime", LocalDateTime.now().toString());
                result.put("downloadUrl", getDownloadUrl(bucketName, objectName));
                result.put("uploadMethod", "POST");
                return result;
            } else {
                return uploadResult;
            }

        } catch (Exception e) {
            logger.warn("POST Policy方法失败，尝试直接POST上传: {}", e.getMessage());
            return performDirectPostUpload(data, fileName, bucketName, objectName);
        }
    }

    /**
     * 执行POST上传
     */
    private Map<String, Object> performPostUpload(String uploadUrl, Map<String, String> formData,
                                                 byte[] data, String objectName) {
        try {
            // 构建multipart/form-data请求
            okhttp3.MultipartBody.Builder builder = new okhttp3.MultipartBody.Builder()
                    .setType(okhttp3.MultipartBody.FORM);

            // 添加表单字段
            for (Map.Entry<String, String> entry : formData.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }

            // 添加文件数据
            okhttp3.RequestBody fileBody = okhttp3.RequestBody.create(data, okhttp3.MediaType.parse("application/octet-stream"));
            builder.addFormDataPart("file", objectName, fileBody);

            okhttp3.RequestBody requestBody = builder.build();

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(uploadUrl)
                    .post(requestBody)
                    .addHeader("User-Agent", "MinIO-Java-POST-Upload")
                    .build();

            // 使用MinIO客户端的HTTP客户端
            okhttp3.OkHttpClient httpClient = getMinioHttpClient();
            try (okhttp3.Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", true);
                    result.put("responseCode", response.code());
                    return result;
                } else {
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("message", "POST上传失败: " + response.code() + " " + response.message());
                    return result;
                }
            }

        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "POST上传异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 直接POST上传（不使用Policy）
     */
    private Map<String, Object> performDirectPostUpload(byte[] data, String fileName, String bucketName, String objectName) {
        try {
            String uploadUrl = minioConfig.getEndpoint() + "/" + bucketName + "/" + objectName;

            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(data,
                okhttp3.MediaType.parse(getContentType(fileName)));

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(uploadUrl)
                    .post(requestBody)  // 使用POST而不是PUT
                    .addHeader("Content-Type", getContentType(fileName))
                    .addHeader("Content-Length", String.valueOf(data.length))
                    .addHeader("User-Agent", "MinIO-Java-Direct-POST")
                    .addHeader("Connection", "close")
                    .build();

            okhttp3.OkHttpClient httpClient = getMinioHttpClient();
            try (okhttp3.Response response = httpClient.newCall(request).execute()) {
                Map<String, Object> result = new HashMap<>();
                if (response.isSuccessful()) {
                    result.put("success", true);
                    result.put("responseCode", response.code());
                    logger.info("直接POST上传成功: {}", objectName);
                } else {
                    result.put("success", false);
                    result.put("message", "直接POST上传失败: " + response.code() + " " + response.message());
                    logger.warn("直接POST上传失败: {} - {}", response.code(), response.message());
                }
                return result;
            }

        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "直接POST上传异常: " + e.getMessage());
            logger.error("直接POST上传异常", e);
            return result;
        }
    }

    /**
     * 获取MinIO客户端的HTTP客户端
     */
    private okhttp3.OkHttpClient getMinioHttpClient() {
        // 创建专用的HTTP客户端，避免PUT/DELETE限制
        return new okhttp3.OkHttpClient.Builder()
                .connectTimeout(minioConfig.getConnectTimeout(), TimeUnit.SECONDS)
                .writeTimeout(minioConfig.getWriteTimeout(), TimeUnit.SECONDS)
                .readTimeout(minioConfig.getReadTimeout(), TimeUnit.SECONDS)
                .retryOnConnectionFailure(false)
                .connectionPool(new okhttp3.ConnectionPool(1, 1, TimeUnit.MINUTES))
                .build();
    }

    /**
     * 分析Windows网络错误
     */
    private void analyzeWindowsNetworkError(Exception e) {
        String errorMsg = e.getMessage();
        if (errorMsg != null) {
            if (errorMsg.contains("Connection reset")) {
                logger.warn("Windows网络连接重置 - 可能原因:");
                logger.warn("  1. Windows Defender防火墙阻止");
                logger.warn("  2. Windows网络适配器问题");
                logger.warn("  3. JDK 17网络模块兼容性");
                logger.warn("  4. Windows TCP/IP栈配置");
            } else if (errorMsg.contains("timeout")) {
                logger.warn("Windows网络超时 - 检查网络配置");
            } else if (errorMsg.contains("refused")) {
                logger.warn("Windows连接被拒绝 - 检查防火墙和端口");
            }
        }
    }

    /**
     * 内部字节数组上传方法 - 优化版本
     */
    private Map<String, Object> uploadBytesInternal(byte[] data, String fileName, String objectName,
                                                   Map<String, String> metadata) throws Exception {
        // 验证数据
        if (data == null || data.length == 0) {
            return createErrorResult("数据为空");
        }

        String bucketName = minioConfig.getDefaultBucket();
        ensureBucketExists(bucketName);

        String contentType = getContentType(fileName);
        String md5Hash = calculateBytesMD5(data);

        // 构建元数据
        Map<String, String> objectMetadata = buildBytesMetadata(fileName, metadata, md5Hash);

        // 使用缓冲流提高性能
        try (InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(data))) {
            PutObjectArgs.Builder argsBuilder = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, data.length, -1)
                    .contentType(contentType);

            if (objectMetadata != null && !objectMetadata.isEmpty()) {
                argsBuilder.userMetadata(objectMetadata);
            }

            ObjectWriteResponse response = minioClient.putObject(argsBuilder.build());

            logger.info("字节数据上传成功: bucket={}, object={}, size={}KB",
                bucketName, objectName, data.length / 1024);

            Map<String, Object> result = createSuccessResult(bucketName, objectName, response.etag());
            result.put("fileSize", data.length);
            result.put("uploadTime", ZonedDateTime.now().toString());
            result.put("downloadUrl", getDownloadUrl(bucketName, objectName));
            result.put("md5Hash", md5Hash);
            result.put("uploadMethod", "single");

            return result;
        }
    }



    /**
     * 下载文件
     */
    public InputStream downloadFile(String bucketName, String objectName) throws Exception {
        GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucketName).object(objectName).build();

        return minioClient.getObject(getObjectArgs);
    }

    /**
     * 获取文件下载URL - 默认7天有效期
     */
    public String getDownloadUrl(String bucketName, String objectName) {
        return getDownloadUrl(bucketName, objectName, 7, TimeUnit.DAYS);
    }

    /**
     * 获取文件下载URL - 自定义有效期
     */
    public String getDownloadUrl(String bucketName, String objectName, int expiry, TimeUnit timeUnit) {
        try {
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiry, timeUnit)
                    .build();

            return minioClient.getPresignedObjectUrl(args);

        } catch (Exception e) {
            logger.error("获取下载URL失败: bucket={}, object={}", bucketName, objectName, e);
            return null;
        }
    }

    /**
     * 获取预签名上传URL - 用于客户端直接上传
     */
    public String getPresignedUploadUrl(String bucketName, String objectName, int expiry, TimeUnit timeUnit) {
        try {
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiry, timeUnit)
                    .build();

            return minioClient.getPresignedObjectUrl(args);

        } catch (Exception e) {
            logger.error("获取预签名上传URL失败: bucket={}, object={}", bucketName, objectName, e);
            return null;
        }
    }

    /**
     * 获取预签名上传URL - 默认1小时有效期
     */
    public String getPresignedUploadUrl(String bucketName, String objectName) {
        return getPresignedUploadUrl(bucketName, objectName, 1, TimeUnit.HOURS);
    }

    /**
     * 流式上传 - 适用于不知道确切大小的数据流
     */
    public Map<String, Object> uploadStream(InputStream inputStream, String fileName, String objectName,
                                          String contentType, Map<String, String> metadata) {
        return executeWithRetry(() -> uploadStreamInternal(inputStream, fileName, objectName, contentType, metadata),
            "流式上传: " + fileName);
    }

    /**
     * 内部流式上传方法
     */
    private Map<String, Object> uploadStreamInternal(InputStream inputStream, String fileName, String objectName,
                                                   String contentType, Map<String, String> metadata) throws Exception {
        if (inputStream == null) {
            return createErrorResult("输入流为空");
        }

        String bucketName = minioConfig.getDefaultBucket();
        ensureBucketExists(bucketName);

        if (contentType == null) {
            contentType = getContentType(fileName);
        }

        // 构建元数据
        Map<String, String> objectMetadata = buildStreamMetadata(fileName, metadata);

        // 使用缓冲流提高性能
        try (BufferedInputStream bufferedStream = new BufferedInputStream(inputStream)) {
            PutObjectArgs.Builder argsBuilder = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(bufferedStream, -1, PART_SIZE) // 使用-1表示未知大小，PART_SIZE作为分片大小
                    .contentType(contentType);

            if (objectMetadata != null && !objectMetadata.isEmpty()) {
                argsBuilder.userMetadata(objectMetadata);
            }

            ObjectWriteResponse response = minioClient.putObject(argsBuilder.build());

            logger.info("流式上传成功: bucket={}, object={}", bucketName, objectName);

            Map<String, Object> result = createSuccessResult(bucketName, objectName, response.etag());
            result.put("uploadTime", ZonedDateTime.now().toString());
            result.put("downloadUrl", getDownloadUrl(bucketName, objectName));
            result.put("uploadMethod", "stream");

            return result;
        }
    }

    /**
     * 构建流式上传元数据
     */
    private Map<String, String> buildStreamMetadata(String fileName, Map<String, String> customMetadata) {
        Map<String, String> metadata = new HashMap<>();

        metadata.put("original-filename", fileName);
        metadata.put("upload-timestamp", String.valueOf(System.currentTimeMillis()));
        metadata.put("upload-type", "stream");

        if (customMetadata != null) {
            metadata.putAll(customMetadata);
        }

        return metadata;
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String bucketName, String objectName) {
        try {
            RemoveObjectArgs removeObjectArgs = RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build();

            minioClient.removeObject(removeObjectArgs);

            logger.info("文件删除成功: bucket={}, object={}", bucketName, objectName);
            return true;

        } catch (Exception e) {
            logger.error("文件删除失败: bucket={}, object={}", bucketName, objectName, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String bucketName, String objectName) {
        try {
            StatObjectArgs statObjectArgs = StatObjectArgs.builder().bucket(bucketName).object(objectName).build();

            minioClient.statObject(statObjectArgs);
            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 确保存储桶存在
     */
    private void ensureBucketExists(String bucketName) throws Exception {
        BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder().bucket(bucketName).build();

        if (!minioClient.bucketExists(bucketExistsArgs)) {
            MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder().bucket(bucketName).build();

            minioClient.makeBucket(makeBucketArgs);
            logger.info("创建存储桶: {}", bucketName);
        }
    }

    /**
     * 验证文件用于上传
     */
    private UploadValidationResult validateFileForUpload(File file) {
        if (file == null) {
            return new UploadValidationResult(false, "文件对象为空");
        }

        if (!file.exists()) {
            return new UploadValidationResult(false, "文件不存在: " + file.getPath());
        }

        if (!file.isFile()) {
            return new UploadValidationResult(false, "不是有效文件: " + file.getPath());
        }

        if (!file.canRead()) {
            return new UploadValidationResult(false, "文件不可读: " + file.getPath());
        }

        long fileSize = file.length();
        if (fileSize == 0) {
            return new UploadValidationResult(false, "文件为空: " + file.getPath());
        }

        // 检查文件大小限制（5TB - MinIO最大对象大小）
        long maxFileSize = 5L * 1024 * 1024 * 1024 * 1024; // 5TB
        if (fileSize > maxFileSize) {
            return new UploadValidationResult(false, "文件过大，超过5TB限制: " + file.getPath());
        }

        return new UploadValidationResult(true, null);
    }



    /**
     * 计算文件MD5哈希
     */
    private String calculateFileMD5(File file) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            try (InputStream inputStream = new BufferedInputStream(new FileInputStream(file))) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    md5.update(buffer, 0, bytesRead);
                }
            }

            byte[] hashBytes = md5.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();

        } catch (Exception e) {
            logger.warn("计算文件MD5失败: {}", file.getName(), e);
            return null;
        }
    }

    /**
     * 计算字节数组MD5哈希
     */
    private String calculateBytesMD5(byte[] data) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md5.digest(data);
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            logger.warn("计算字节数组MD5失败", e);
            return null;
        }
    }

    /**
     * 构建对象元数据
     */
    private Map<String, String> buildObjectMetadata(File file, Map<String, String> customMetadata, String md5Hash) {
        Map<String, String> metadata = new HashMap<>();

        // 添加文件基本信息
        metadata.put("original-filename", file.getName());
        metadata.put("file-size", String.valueOf(file.length()));
        metadata.put("upload-timestamp", String.valueOf(System.currentTimeMillis()));

        if (md5Hash != null) {
            metadata.put("md5-hash", md5Hash);
        }

        // 添加文件最后修改时间
        metadata.put("last-modified", String.valueOf(file.lastModified()));

        // 添加自定义元数据
        if (customMetadata != null) {
            metadata.putAll(customMetadata);
        }

        return metadata;
    }

    /**
     * 构建字节数组元数据
     */
    private Map<String, String> buildBytesMetadata(String fileName, Map<String, String> customMetadata, String md5Hash) {
        Map<String, String> metadata = new HashMap<>();

        metadata.put("original-filename", fileName);
        metadata.put("upload-timestamp", String.valueOf(System.currentTimeMillis()));
        metadata.put("upload-type", "bytes");

        if (md5Hash != null) {
            metadata.put("md5-hash", md5Hash);
        }

        if (customMetadata != null) {
            metadata.putAll(customMetadata);
        }

        return metadata;
    }

    /**
     * 创建成功结果
     */
    private Map<String, Object> createSuccessResult(String bucketName, String objectName, String etag) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("bucketName", bucketName);
        result.put("objectName", objectName);
        result.put("etag", etag);
        return result;
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        return result;
    }

    /**
     * 上传验证结果类
     */
    private static class UploadValidationResult {
        private final boolean valid;
        private final String errorMessage;

        public UploadValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }


    /**
     * 检查对象是否已存在
     */
    private boolean isObjectExists(String bucketName, String objectName) {
        try {
            if (minioClient == null) {
                return false;
            }

            StatObjectArgs statArgs = StatObjectArgs.builder().bucket(bucketName).object(objectName).build();

            minioClient.statObject(statArgs);
            return true;

        } catch (Exception e) {
            // 对象不存在或其他错误
            return false;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * 获取内容类型
     */
    private String getContentType(String fileName) {
        String extension = getFileExtension(fileName);

        switch (extension) {
            case "pdf":
                return "application/pdf";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "txt":
                return "text/plain";
            case "json":
                return "application/json";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 测试MinIO连接
     */
    public boolean testConnection() {
        try {
            if (minioClient == null) {
                return false;
            }

            // 尝试列出存储桶来测试连接
            minioClient.listBuckets();
            logger.info("MinIO连接测试成功");
            return true;

        } catch (Exception e) {
            logger.error("MinIO连接测试失败", e);
            return false;
        }
    }

    /**
     * 简化的重试机制 - 其他程序能正常工作，简化我们的实现
     */
    private <T> T executeWithRetry(SupplierWithException<T> supplier, String operation) {
        Exception lastException = null;
        int maxRetries = minioConfig.getMaxRetryCount();

        for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                return supplier.get();
            } catch (Exception e) {
                lastException = e;

                // 只对明确的网络异常进行重试
                if (attempt <= maxRetries && isNetworkException(e)) {
                    logger.warn("{}失败，第{}次重试: {}", operation, attempt, e.getMessage());

                    try {
                        Thread.sleep(1000); // 固定1秒等待
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    logger.error("{}失败: {}", operation, e.getMessage());
                    break;
                }
            }
        }

        // 返回失败结果
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("success", false);
        errorResult.put("message", operation + "失败: " + (lastException != null ? lastException.getMessage() : "未知错误"));

        @SuppressWarnings("unchecked")
        T result = (T) errorResult;
        return result;
    }

    /**
     * 简化的网络异常判断
     */
    private boolean isNetworkException(Exception e) {
        // 只对明确的网络异常重试
        if (e instanceof java.net.SocketException ||
            e instanceof java.net.SocketTimeoutException ||
            e instanceof java.net.ConnectException) {
            return true;
        }

        // 检查Connection reset
        String message = e.getMessage();
        return message != null && message.toLowerCase().contains("connection reset");
    }

    /**
     * 带异常的Supplier接口
     */
    @FunctionalInterface
    private interface SupplierWithException<T> {
        T get() throws Exception;
    }
}
