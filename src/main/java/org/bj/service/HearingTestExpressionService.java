package org.bj.service;

import lombok.extern.slf4j.Slf4j;
import org.bj.config.AudiometryProperties;
import org.bj.device.audiometry.entity.ElectricalAudiometryResult;
import org.springframework.context.annotation.Profile;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 听力测试表达式服务
 * 用于评估听力测试指标的异常规则
 *
 * <AUTHOR> Agent
 * @date 2025-07-09
 */
@Slf4j
@Service
@Profile("audiometry")
public class HearingTestExpressionService {

    private final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 评估异常规则
     *
     * @param indicatorName 指标名称
     * @param value 指标值
     * @param allData 所有数据
     * @param indicators 指标配置列表
     * @return 异常信息
     */
    public Map<String, Object> evaluateAbnormalRules(String indicatorName, Object value,
                                                     ElectricalAudiometryResult allData,
                                                     List<AudiometryProperties.HearingTest.Indicator> indicators) {
        Map<String, Object> result = new HashMap<>();
        result.put("flag", "0");
        result.put("description", "正常");
        result.put("symbol", "");

        try {
            // 查找对应的指标配置
            AudiometryProperties.HearingTest.Indicator indicator = findIndicatorByName(indicatorName, indicators);
            if (indicator == null || indicator.getAbnormalRules() == null) {
                return result;
            }

            // 创建表达式上下文
            StandardEvaluationContext context = createEvaluationContext(value, allData);

            // 按优先级排序并评估规则
            List<AudiometryProperties.HearingTest.Indicator.AbnormalRule> sortedRules = 
                indicator.getAbnormalRules().stream()
                    .filter(AudiometryProperties.HearingTest.Indicator.AbnormalRule::isEnabled)
                    .sorted((r1, r2) -> Integer.compare(r1.getPriority(), r2.getPriority()))
                    .toList();

            for (AudiometryProperties.HearingTest.Indicator.AbnormalRule rule : sortedRules) {
                try {
                    Expression expression = parser.parseExpression(rule.getCondition());
                    Boolean ruleResult = expression.getValue(context, Boolean.class);
                    
                    if (Boolean.TRUE.equals(ruleResult)) {
                        result.put("flag", rule.getFlag());
                        result.put("description", rule.getDescription());
                        result.put("symbol", rule.getSymbol());
                        log.debug("指标 {} 匹配异常规则: {}", indicatorName, rule.getName());
                        break; // 找到第一个匹配的规则就停止
                    }
                } catch (Exception e) {
                    log.warn("评估异常规则失败: 指标={}, 规则={}, 条件={}", 
                        indicatorName, rule.getName(), rule.getCondition(), e);
                }
            }

        } catch (Exception e) {
            log.error("评估指标异常规则失败: 指标={}", indicatorName, e);
        }

        return result;
    }

    /**
     * 根据名称查找指标配置
     */
    private AudiometryProperties.HearingTest.Indicator findIndicatorByName(String name, 
                                                                          List<AudiometryProperties.HearingTest.Indicator> indicators) {
        if (indicators == null) {
            return null;
        }
        
        return indicators.stream()
            .filter(indicator -> name.equals(indicator.getName()))
            .findFirst()
            .orElse(null);
    }

    /**
     * 创建表达式评估上下文
     */
    private StandardEvaluationContext createEvaluationContext(Object value, ElectricalAudiometryResult allData) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        
        // 设置当前值
        context.setVariable("value", value);
        
        // 设置所有数据变量
  /*      if (allData != null) {
            for (Map.Entry<String, Object> entry : allData) {
                context.setVariable(entry.getKey(), entry.getValue());
            }
        }*/
        
        return context;
    }

    /**
     * 测试表达式是否有效
     */
    public boolean isValidExpression(String expression) {
        try {
            parser.parseExpression(expression);
            return true;
        } catch (Exception e) {
            log.warn("无效的表达式: {}", expression, e);
            return false;
        }
    }

    /**
     * 格式化听力测试结果
     */
    public String formatHearingTestResults(Map<String, Object> hearingTestData) {
        if (hearingTestData == null || hearingTestData.isEmpty()) {
            return "无听力测试数据";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("听力测试结果:\n");

        // 左耳数据
        Object acLeft = hearingTestData.get("acLeft");
        Object bcLeft = hearingTestData.get("bcLeft");
        Object ptaLeft = hearingTestData.get("ptaLeft");
        
        if (acLeft != null || bcLeft != null || ptaLeft != null) {
            sb.append("左耳: ");
            if (acLeft != null) sb.append("气导阈值=").append(acLeft).append("dB HL ");
            if (bcLeft != null) sb.append("骨导阈值=").append(bcLeft).append("dB HL ");
            if (ptaLeft != null) sb.append("平均听阈=").append(ptaLeft).append("dB HL");
            sb.append("\n");
        }

        // 右耳数据
        Object acRight = hearingTestData.get("acRight");
        Object bcRight = hearingTestData.get("bcRight");
        Object ptaRight = hearingTestData.get("ptaRight");
        
        if (acRight != null || bcRight != null || ptaRight != null) {
            sb.append("右耳: ");
            if (acRight != null) sb.append("气导阈值=").append(acRight).append("dB HL ");
            if (bcRight != null) sb.append("骨导阈值=").append(bcRight).append("dB HL ");
            if (ptaRight != null) sb.append("平均听阈=").append(ptaRight).append("dB HL");
            sb.append("\n");
        }

        return sb.toString().trim();
    }
}
