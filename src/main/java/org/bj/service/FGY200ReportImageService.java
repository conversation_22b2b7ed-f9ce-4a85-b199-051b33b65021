package org.bj.service;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bj.config.FGY200Properties;
import org.bj.device.entity.ExamItemInfo;
import org.bj.device.entity.PatientInfo;
import org.bj.util.FileUploadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.google.common.io.Files.getFileExtension;

/**
 * FGY200报告图片处理服务
 * <p>
 * 负责定时扫描和处理肺功能报告图片
 *
 * <AUTHOR> Agent
 * @date 2025-01-08
 */
@Slf4j
@Service
@Profile("fgy200")
public class FGY200ReportImageService {

    @Autowired
    @Qualifier("mainJdbcTemplate")
    private JdbcTemplate mainJdbcTemplate;

    @Autowired
    private FGY200Properties fgy200Properties;

    @Autowired(required = false)
    private FileUploadUtil fileUploadUtil;

    /**
     * 待处理的报告图片任务队列
     * Key: 体检号, Value: 报告图片处理任务
     */
    private final ConcurrentHashMap<String, ReportImageTask> pendingTasks = new ConcurrentHashMap<>();

    /**
     * 报告图片处理任务
     */
    @Data
    public static class ReportImageTask {
        // Getters and Setters
        private String examNo;
        private String customerRegItemGroupId;
        private PatientInfo patientInfo;
        private ExamItemInfo examItemInfo;
        private Map<String, Object> lungFunctionResult;
        private String deviceModel;
        private LocalDateTime createTime;
        private int retryCount;

        // 构造函数和getter/setter
        public ReportImageTask(String examNo, String customerRegItemGroupId, PatientInfo patientInfo, ExamItemInfo examItemInfo, Map<String, Object> lungFunctionResult, String deviceModel) {
            this.examNo = examNo;
            this.customerRegItemGroupId = customerRegItemGroupId;
            this.patientInfo = patientInfo;
            this.examItemInfo = examItemInfo;
            this.lungFunctionResult = lungFunctionResult;
            this.deviceModel = deviceModel;
            this.createTime = LocalDateTime.now();
            this.retryCount = 0;
        }

        public void incrementRetryCount() {
            this.retryCount++;
        }
    }

    /**
     * 添加报告图片处理任务
     */
    public void addReportImageTask(String examNo, String customerRegItemGroupId, PatientInfo patientInfo, ExamItemInfo examItemInfo, Map<String, Object> lungFunctionResult, String deviceModel) {
        ReportImageTask task = new ReportImageTask(examNo, customerRegItemGroupId, patientInfo, examItemInfo, lungFunctionResult, deviceModel);
        pendingTasks.put(examNo, task);
        log.info("添加报告图片处理任务: 体检号={}", examNo);
    }

    /**
     * 定时处理报告图片任务
     */
    @Async
    public void processReportImageTasks() {
        // 1. 处理队列中的任务
        processQueuedTasks();

        // 2. 扫描数据库中需要补充图片的记录
        scanDatabaseForMissingImages();
    }

    /**
     * 扫描数据库中需要补充图片的记录
     */
    private void scanDatabaseForMissingImages() {
        try {
            // 查询需要补充报告图片的肺功能检查记录
            // 识别条件：
            // 1. his_code 包含 'LUNG' 或 item_group_name 包含 '肺功能'
            // 2. check_status = '已检' (检查已完成)
            // 3. pic_sync_status IS NULL 或 pic_sync_status = 0 (图片未同步)
            String querySql = """
                    SELECT id, exam_no, pic_sync_status, his_code, item_group_name,check_time, customer_reg_id
                    FROM customer_reg_item_group 
                    WHERE his_code = ?
                    AND check_status = '已检'
                    AND (pic_sync_status IS NULL OR pic_sync_status = 0)
                    ORDER BY check_time DESC
                    LIMIT 20
                    """;

            List<Map<String, Object>> records = mainJdbcTemplate.queryForList(querySql, fgy200Properties.getHisCode());

            if (!records.isEmpty()) {
                log.info("发现需要补充报告图片的肺功能记录数量: {}", records.size());

                for (Map<String, Object> record : records) {
                    try {
                        processImageSyncRecord(record);
                    } catch (Exception e) {
                        log.error("处理图片同步记录失败: 记录ID={}, 体检号={}", record.get("id"), record.get("exam_no"), e);
                    }
                }
            } else {
                log.debug("未发现需要补充报告图片的肺功能记录");
            }

        } catch (Exception e) {
            log.error("扫描数据库中需要补充图片的记录失败", e);
        }
    }

    /**
     * 处理单个图片同步记录
     */
    private void processImageSyncRecord(Map<String, Object> record) {
        try {
            String examNo = (String) record.get("exam_no");
            String customerRegItemGroupId = record.get("id").toString();
            String hisCode = (String) record.get("his_code");
            String itemGroupName = (String) record.get("item_group_name");

            log.debug("处理肺功能图片同步记录: 体检号={}, 项目组ID={}, his_code={}, 项目名称={}", examNo, customerRegItemGroupId, hisCode, itemGroupName);


            // 使用统一的预定义文件名方式扫描并上传报告图片
            List<String> reportImageUrls = new ArrayList<>();
            String imageFormat = fgy200Properties.getReport().getImageFormat();
            List<String> predefinedFileNames = generatePredefinedFileNames(examNo, imageFormat);

            for (String fileName : predefinedFileNames) {
                String fullPath = fgy200Properties.getReport().getImageDirectory() + "/" + fileName;
                File reportFile = new File(fullPath);

                if (reportFile.exists() && reportFile.isFile()) {
                    try {
                        String uploadedUrl = uploadReportImage(reportFile);
                        if (uploadedUrl != null && !uploadedUrl.isEmpty()) {
                            reportImageUrls.add(uploadedUrl);
                            log.info("补充报告图片上传成功: 文件={}, URL={}", fileName, uploadedUrl);
                        }
                    } catch (Exception e) {
                        log.warn("补充报告图片上传失败: 文件={}, 错误={}", fileName, e.getMessage());
                    }
                } else {
                    log.debug("补充报告图片文件不存在: {}", fullPath);
                }
            }

            if (!reportImageUrls.isEmpty()) {
                // 更新报告图片URL和同步状态
                updateReportImageUrlsAndSyncStatus(customerRegItemGroupId, reportImageUrls);
                log.info("补充肺功能报告图片成功: 体检号={}, 图片数量={}", examNo, reportImageUrls.size());
            } else {
                log.debug("未找到肺功能报告图片文件: 体检号={}", examNo);
                // 标记为已尝试同步，避免重复扫描
                //markImageSyncAttempted(customerRegItemGroupId);
            }

        } catch (Exception e) {
            log.error("处理肺功能图片同步记录异常: 记录={}", record, e);
        }
    }

    /**
     * 标记图片同步已尝试（避免重复扫描）
     */
    private void markImageSyncAttempted(String customerRegItemGroupId) {
        try {
            String updateSql = """
                    UPDATE customer_reg_item_group
                    SET pic_sync_status = -1,
                        update_time = ?
                    WHERE id = ?
                    """;

            mainJdbcTemplate.update(updateSql, LocalDateTime.now(), customerRegItemGroupId);
            log.debug("标记图片同步已尝试: 项目组ID={}", customerRegItemGroupId);

        } catch (Exception e) {
            log.warn("标记图片同步状态失败: 项目组ID={}", customerRegItemGroupId, e);
        }
    }

    /**
     * 处理队列中的任务
     */
    private void processQueuedTasks() {
        if (pendingTasks.isEmpty()) {
            return;
        }

        log.debug("开始处理队列中的报告图片任务，待处理数量: {}", pendingTasks.size());

        Iterator<Map.Entry<String, ReportImageTask>> iterator = pendingTasks.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, ReportImageTask> entry = iterator.next();
            ReportImageTask task = entry.getValue();

            try {
                boolean success = processReportImageTask(task);
                if (success) {
                    iterator.remove();
                    log.info("队列报告图片处理成功: 体检号={}", task.getExamNo());
                } else {
                    task.incrementRetryCount();
                    if (task.getRetryCount() >= fgy200Properties.getReport().getMaxRetryCount()) {
                        iterator.remove();
                        log.warn("队列报告图片处理失败，超过最大重试次数: 体检号={}", task.getExamNo());
                    }
                }
            } catch (Exception e) {
                log.error("处理队列报告图片任务异常: 体检号={}", task.getExamNo(), e);
                task.incrementRetryCount();
                if (task.getRetryCount() >= fgy200Properties.getReport().getMaxRetryCount()) {
                    iterator.remove();
                }
            }
        }
    }


    /**
     * 处理单个报告图片任务
     */
    private boolean processReportImageTask(ReportImageTask task) {
        try {
            String examNo = task.getExamNo();

            // 1. 扫描报告图片文件
            List<String> reportImageUrls = scanAndUploadReportImages(task.getPatientInfo());

            if (reportImageUrls.isEmpty()) {
                log.debug("未找到报告图片文件: 体检号={}", examNo);
                return false; // 图片还未生成，继续等待
            }

            // 2. 更新数据库中的报告图片URL
            updateReportImageUrls(task.getCustomerRegItemGroupId(), reportImageUrls);

            log.info("报告图片处理完成: 体检号={}, 图片数量={}", examNo, reportImageUrls.size());
            return true;

        } catch (Exception e) {
            log.error("处理报告图片任务失败: 体检号={}", task.getExamNo(), e);
            return false;
        }
    }

    /**
     * 扫描并上传报告图片
     * 改进版本：使用预定义文件名模式，避免磁盘扫描带来的性能问题
     */
    private List<String> scanAndUploadReportImages(PatientInfo patientInfo) {
        List<String> uploadedUrls = new ArrayList<>();

        try {
            String examNo = patientInfo.getExamNo();
            String reportDirectory = fgy200Properties.getReport().getImageDirectory();
            String imageFormat = fgy200Properties.getReport().getImageFormat();

            log.debug("开始检查报告图片: 体检号={}, 目录={}", examNo, reportDirectory);

            // 预定义5张图片的文件名（基于常见的肺功能报告图片数量）
            List<String> predefinedFileNames = generatePredefinedFileNames(examNo, imageFormat);

            // 逐个检查预定义的文件名是否存在
            for (String fileName : predefinedFileNames) {
                String fullPath = reportDirectory + "/" + fileName;
                File reportFile = new File(fullPath);

                if (reportFile.exists() && reportFile.isFile()) {
                    try {
                        String uploadedUrl = uploadReportImage(reportFile);
                        if (uploadedUrl != null && !uploadedUrl.isEmpty()) {
                            uploadedUrls.add(uploadedUrl);
                            log.info("报告图片上传成功: 文件={}, URL={}", fileName, uploadedUrl);
                        }
                    } catch (Exception e) {
                        log.warn("报告图片上传失败: 文件={}, 错误={}", fileName, e.getMessage());
                    }
                } else {
                    log.debug("报告图片文件不存在: {}", fullPath);
                }
            }

            if (uploadedUrls.isEmpty()) {
                log.debug("未找到任何报告图片文件: 体检号={}, 检查的文件名={}", examNo, predefinedFileNames);
            } else {
                log.info("成功上传报告图片: 体检号={}, 图片数量={}", examNo, uploadedUrls.size());
            }

        } catch (Exception e) {
            log.error("检查报告图片失败: 体检号={}", patientInfo.getExamNo(), e);
        }

        return uploadedUrls;
    }

    /**
     * 生成预定义的文件名列表
     * 基于配置的文件名模式生成5张图片的文件名
     */
    private List<String> generatePredefinedFileNames(String examNo, String imageFormat) {
        List<String> fileNames = new ArrayList<>();

        // 根据配置的文件名模式生成文件名
        String fileNamePattern = fgy200Properties.getReport().getFileNamePattern();

        // 生成5张图片的文件名（序号从0到4）
        for (int i = 0; i < 5; i++) {
            String fileName = buildFileName(fileNamePattern, examNo, i, imageFormat);
            fileNames.add(fileName);
        }

        log.debug("生成预定义文件名列表: 体检号={}, 文件名={}", examNo, fileNames);
        return fileNames;
    }

    /**
     * 根据模式构建文件名
     */
    private String buildFileName(String pattern, String examNo, int index, String imageFormat) {
        String fileName = pattern
            .replace("{examNo}", examNo)
            .replace("{序号}", String.valueOf(index))
            .replace("{index}", String.valueOf(index));

        // 添加文件扩展名
        if (!fileName.endsWith("." + imageFormat)) {
            fileName += "." + imageFormat;
        }

        return fileName;
    }

    // 注意：已移除 scanReportFiles 和 buildFileNameRegex 方法
    // 这些方法使用磁盘扫描方式，已被预定义文件名方式替代以提升性能

    /**
     * 上传报告图片到MinIO
     */
    private String uploadReportImage(File reportFile) {
        try {
            if (fileUploadUtil == null) {
                log.warn("文件上传工具未配置，跳过图片上传");
                return "";
            }

            // 构建对象路径
            //获取后缀
            String extension = getFileExtension(reportFile.getName());
            String objectPath = "lung-fuction" + UUID.randomUUID() + "." + extension;

            // 上传文件 - 使用正确的方法签名
            Map<String, Object> uploadResult = fileUploadUtil.uploadToMinio(reportFile, objectPath);

            if (uploadResult != null && Boolean.TRUE.equals(uploadResult.get("success"))) {
                String downloadUrl = (String) uploadResult.get("downloadUrl");
                log.info("报告图片上传成功: 本地文件={}, 上传路径={}, URL={}", reportFile.getAbsolutePath(), objectPath, downloadUrl);
                return downloadUrl;
            } else {
                String errorMessage = uploadResult != null ? (String) uploadResult.get("message") : "未知错误";
                log.warn("报告图片上传失败: 文件={}, 错误={}", reportFile.getName(), errorMessage);
                return "";
            }
        } catch (Exception e) {
            log.error("上传报告图片失败: 文件={}", reportFile.getAbsolutePath(), e);
            return null;
        }
    }

    /**
     * 更新数据库中的报告图片URL
     */
    private void updateReportImageUrls(String customerRegItemGroupId, List<String> reportImageUrls) {
        updateReportImageUrlsAndSyncStatus(customerRegItemGroupId, reportImageUrls);
    }

    /**
     * 更新报告图片URL和同步状态
     */
    private void updateReportImageUrlsAndSyncStatus(String customerRegItemGroupId, List<String> reportImageUrls) {
        try {
            if (reportImageUrls.isEmpty()) {
                return;
            }

            LocalDateTime now = LocalDateTime.now();

            // 构建JSON数组格式的URL列表
            String jsonUrls = buildJsonArray(reportImageUrls);

            String updateSql = """
                    UPDATE customer_reg_item_group
                    SET report_pics = ?,
                        pic_sync_status = 1,
                        update_time = ?
                    WHERE id = ?
                    """;

            int updatedRows = mainJdbcTemplate.update(updateSql, jsonUrls, now, customerRegItemGroupId);

            if (updatedRows > 0) {
                log.info("报告图片URL和同步状态更新成功: 项目组ID={}, 图片数量={}, 同步状态=1", customerRegItemGroupId, reportImageUrls.size());
            } else {
                log.warn("报告图片URL更新失败，未找到对应记录: 项目组ID={}", customerRegItemGroupId);
            }

        } catch (Exception e) {
            log.error("更新报告图片URL和同步状态失败: 项目组ID={}", customerRegItemGroupId, e);
            throw e;
        }
    }

    /**
     * 构建JSON数组格式的字符串
     */
    private String buildJsonArray(List<String> urls) {
        if (urls.isEmpty()) {
            return "[]";
        }

        StringBuilder json = new StringBuilder("[");
        for (int i = 0; i < urls.size(); i++) {
            if (i > 0) {
                json.append(",");
            }
            json.append("\"").append(urls.get(i)).append("\"");
        }
        json.append("]");

        return json.toString();
    }

    /**
     * 获取待处理任务数量
     */
    public int getPendingTaskCount() {
        return pendingTasks.size();
    }

    /**
     * 清理超时任务
     */
    public void cleanupTimeoutTasks() {
        LocalDateTime timeout = LocalDateTime.now().minusHours(2); // 2小时超时

        pendingTasks.entrySet().removeIf(entry -> {
            ReportImageTask task = entry.getValue();
            if (task.getCreateTime().isBefore(timeout)) {
                log.warn("清理超时的报告图片任务: 体检号={}, 创建时间={}", task.getExamNo(), task.getCreateTime());
                return true;
            }
            return false;
        });
    }
}
