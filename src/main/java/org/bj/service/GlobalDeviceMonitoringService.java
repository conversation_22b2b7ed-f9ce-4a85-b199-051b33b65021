package org.bj.service;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bj.device.DeviceProcessor;
import org.bj.device.entity.DeviceContext;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * 全局设备监控服务
 * 
 * 提供统一的设备监控管理，替代各个设备处理器中的独立定时任务
 * 
 * 特性：
 * 1. 单一全局定时任务监控所有注册设备
 * 2. 支持不同设备类型的监控策略
 * 3. 自动清理完成或超时的监控任务
 * 4. 配置化的监控间隔和超时设置
 * 
 * <AUTHOR> Agent
 * @date 2025-07-09
 */
@Slf4j
@Service
public class GlobalDeviceMonitoringService {

    /**
     * 监控任务注册表
     * Key: 监控任务ID (通常是 deviceId + "_" + patientId 或 deviceId)
     * Value: 监控任务信息
     */
    private final ConcurrentMap<String, MonitoringTask> monitoringTasks = new ConcurrentHashMap<>();

    /**
     * 设备处理器注册表
     * Key: 设备型号
     * Value: 设备处理器实例
     */
    private final ConcurrentMap<String, DeviceProcessor> deviceProcessors = new ConcurrentHashMap<>();

    /**
     * 监控配置注册表
     * Key: 设备型号
     * Value: 监控配置
     */
    private final ConcurrentMap<String, GlobalMonitoringConfig> monitoringConfigs = new ConcurrentHashMap<>();

    /**
     * 注册设备处理器
     *
     * @param deviceModel 设备型号
     * @param processor 设备处理器
     * @param config 监控配置
     */
    public void registerDeviceProcessor(String deviceModel, DeviceProcessor processor, GlobalMonitoringConfig config) {
        deviceProcessors.put(deviceModel, processor);
        monitoringConfigs.put(deviceModel, config);
        log.info("注册设备处理器到全局监控: 设备型号={}, 监控间隔={}秒", deviceModel, config.getIntervalSeconds());
    }

    /**
     * 注册监控任务
     * 
     * @param taskId 任务ID
     * @param deviceModel 设备型号
     * @param context 设备上下文
     * @param customData 自定义数据（如患者信息等）
     */
    public void registerMonitoringTask(String taskId, String deviceModel, DeviceContext context, Object customData) {
        MonitoringTask task = new MonitoringTask();
        task.setTaskId(taskId);
        task.setDeviceModel(deviceModel);
        task.setContext(context);
        task.setCustomData(customData);
        task.setStartTime(System.currentTimeMillis());
        task.setLastCheckTime(System.currentTimeMillis());
        task.setStatus(MonitoringTaskStatus.ACTIVE);

        monitoringTasks.put(taskId, task);
        log.info("注册监控任务: 任务ID={}, 设备型号={}", taskId, deviceModel);
    }

    /**
     * 取消监控任务
     * 
     * @param taskId 任务ID
     */
    public void unregisterMonitoringTask(String taskId) {
        MonitoringTask task = monitoringTasks.remove(taskId);
        if (task != null) {
            task.setStatus(MonitoringTaskStatus.COMPLETED);
            log.info("取消监控任务: 任务ID={}, 设备型号={}", taskId, task.getDeviceModel());
        }
    }

    /**
     * 执行全局监控检查
     * 由定时任务调用，检查所有注册的监控任务
     */
    public void executeGlobalMonitoring() {
        long startTime = System.currentTimeMillis();

        if (monitoringTasks.isEmpty()) {
            log.debug("全局监控检查: 无活跃任务");
            return;
        }

        log.info("开始执行全局设备监控检查，当前任务数量: {}, 线程: {}",
            monitoringTasks.size(), Thread.currentThread().getName());

        List<String> tasksToRemove = new ArrayList<>();
        long currentTime = System.currentTimeMillis();

        for (Map.Entry<String, MonitoringTask> entry : monitoringTasks.entrySet()) {
            String taskId = entry.getKey();
            MonitoringTask task = entry.getValue();

            try {
                // 检查任务是否应该被执行
                if (shouldExecuteTask(task, currentTime)) {
                    executeMonitoringTask(task);
                    task.setLastCheckTime(currentTime);
                }

                // 检查任务是否应该被清理
                if (shouldCleanupTask(task, currentTime)) {
                    tasksToRemove.add(taskId);
                }

            } catch (Exception e) {
                log.error("执行监控任务失败: 任务ID={}, 设备型号={}", taskId, task.getDeviceModel(), e);
                
                // 增加错误计数
                task.setErrorCount(task.getErrorCount() + 1);
                
                // 如果错误次数过多，标记任务为失败
                if (task.getErrorCount() >= getMaxErrorCount(task.getDeviceModel())) {
                    task.setStatus(MonitoringTaskStatus.FAILED);
                    tasksToRemove.add(taskId);
                }
            }
        }

        // 清理需要移除的任务
        for (String taskId : tasksToRemove) {
            MonitoringTask task = monitoringTasks.remove(taskId);
            if (task != null) {
                log.info("✅ 清理监控任务: 任务ID={}, 设备型号={}, 状态={}, 运行时长={}秒",
                    taskId, task.getDeviceModel(), task.getStatus(), (currentTime - task.getStartTime()) / 1000);
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        if (!tasksToRemove.isEmpty()) {
            log.info("全局监控检查完成，清理任务数量: {}, 剩余任务数量: {}, 耗时: {}ms",
                tasksToRemove.size(), monitoringTasks.size(), duration);
        } else {
            log.debug("全局监控检查完成，无任务清理, 剩余任务数量: {}, 耗时: {}ms",
                monitoringTasks.size(), duration);
        }
    }

    /**
     * 获取当前监控任务数量
     */
    public int getActiveTaskCount() {
        return monitoringTasks.size();
    }

    /**
     * 获取指定设备型号的监控任务数量
     */
    public int getActiveTaskCount(String deviceModel) {
        return (int) monitoringTasks.values().stream()
            .filter(task -> deviceModel.equals(task.getDeviceModel()))
            .count();
    }

    /**
     * 获取已注册的设备型号列表
     */
    public java.util.Set<String> getRegisteredDeviceModels() {
        return deviceProcessors.keySet();
    }

    /**
     * 获取监控服务状态信息
     */
    public String getServiceStatus() {
        return String.format("GlobalDeviceMonitoringService状态: 已注册设备=%d, 活跃任务=%d, 监控配置=%d",
            deviceProcessors.size(), monitoringTasks.size(), monitoringConfigs.size());
    }

    /**
     * 打印详细状态信息（用于调试）
     */
    public void printDetailedStatus() {
        log.info("=== 全局设备监控服务状态 ===");
        log.info("已注册设备处理器数量: {}", deviceProcessors.size());
        log.info("活跃监控任务数量: {}", monitoringTasks.size());
        log.info("监控配置数量: {}", monitoringConfigs.size());

        if (!deviceProcessors.isEmpty()) {
            log.info("已注册设备型号: {}", String.join(", ", deviceProcessors.keySet()));
        }

        if (!monitoringTasks.isEmpty()) {
            log.info("活跃任务详情:");
            monitoringTasks.forEach((taskId, task) -> {
                log.info("  任务ID: {}, 设备型号: {}, 状态: {}, 错误次数: {}",
                    taskId, task.getDeviceModel(), task.getStatus(), task.getErrorCount());
            });
        }

        log.info("=== 状态信息结束 ===");
    }

    /**
     * 检查任务是否应该被执行
     */
    private boolean shouldExecuteTask(MonitoringTask task, long currentTime) {
        GlobalMonitoringConfig config = monitoringConfigs.get(task.getDeviceModel());
        if (config == null) {
            return false;
        }

        long intervalMs = config.getIntervalSeconds() * 1000L;
        return (currentTime - task.getLastCheckTime()) >= intervalMs;
    }

    /**
     * 检查任务是否应该被清理
     */
    private boolean shouldCleanupTask(MonitoringTask task, long currentTime) {
        // 如果任务已完成或失败，应该被清理
        if (task.getStatus() == MonitoringTaskStatus.COMPLETED ||
            task.getStatus() == MonitoringTaskStatus.FAILED) {
            return true;
        }

        // 检查是否超时
        GlobalMonitoringConfig config = monitoringConfigs.get(task.getDeviceModel());
        if (config != null && config.getTimeoutSeconds() > 0) {
            long timeoutMs = config.getTimeoutSeconds() * 1000L;
            if ((currentTime - task.getStartTime()) > timeoutMs) {
                task.setStatus(MonitoringTaskStatus.TIMEOUT);
                return true;
            }
        }

        return false;
    }

    /**
     * 执行具体的监控任务
     */
    private void executeMonitoringTask(MonitoringTask task) {
        DeviceProcessor processor = deviceProcessors.get(task.getDeviceModel());
        if (processor == null) {
            log.warn("未找到设备处理器: 设备型号={}", task.getDeviceModel());
            return;
        }

        // 调用设备处理器的监控方法
        boolean result = processor.executeMonitoring(task.getContext(), task.getCustomData());

        if (result) {
            // 监控成功，继续监控 - 检查是否应该完成任务
            if (processor.isMonitoringCompleted(task.getContext(), task.getCustomData())) {
                task.setStatus(MonitoringTaskStatus.COMPLETED);
                log.debug("监控任务完成: 任务ID={}, 设备型号={}", task.getTaskId(), task.getDeviceModel());
            }
        } else {
            // 监控返回false，表示应该停止监控
            task.setStatus(MonitoringTaskStatus.COMPLETED);
            log.debug("监控任务停止: 任务ID={}, 设备型号={}", task.getTaskId(), task.getDeviceModel());
        }
    }

    /**
     * 获取设备的最大错误次数
     */
    private int getMaxErrorCount(String deviceModel) {
        GlobalMonitoringConfig config = monitoringConfigs.get(deviceModel);
        return config != null ? config.getMaxErrorCount() : 5; // 默认5次
    }

    // =================== 内部类 ===================

    /**
     * 监控任务信息
     */
    @Data
    public static class MonitoringTask {
        // Getters and Setters
        private String taskId;
        private String deviceModel;
        private DeviceContext context;
        private Object customData;
        private long startTime;
        private long lastCheckTime;
        private MonitoringTaskStatus status;
        private int errorCount = 0;
    }

    /**
     * 监控配置信息
     */
    @Data
    public static class GlobalMonitoringConfig {
        // Getters and Setters
        private int intervalSeconds = 10;      // 监控间隔（秒）
        private int timeoutSeconds = 300;      // 超时时间（秒）
        private int maxErrorCount = 5;         // 最大错误次数
        private boolean enabled = true;        // 是否启用监控

        public GlobalMonitoringConfig(int intervalSeconds, int timeoutSeconds, int maxErrorCount) {
            this.intervalSeconds = intervalSeconds;
            this.timeoutSeconds = timeoutSeconds;
            this.maxErrorCount = maxErrorCount;
        }
    }

    /**
     * 监控任务状态枚举
     */
    public enum MonitoringTaskStatus {
        ACTIVE,      // 活跃中
        COMPLETED,   // 已完成
        FAILED,      // 失败
        TIMEOUT      // 超时
    }
}
