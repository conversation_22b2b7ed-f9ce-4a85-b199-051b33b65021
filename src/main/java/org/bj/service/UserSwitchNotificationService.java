package org.bj.service;

import lombok.extern.slf4j.Slf4j;
import org.bj.constants.event.UserSwitchEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import com.corundumstudio.socketio.SocketIOClient;

import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 用户切换通知服务
 * 负责处理用户切换时的通知和状态管理
 * 使用事件监听器模式，避免循环依赖
 *
 * <AUTHOR> Agent
 * @date 2025-06-28
 */
@Slf4j
@Service
public class UserSwitchNotificationService {

    @Autowired
    private SocketIOService socketIOService;

    /** 用户切换历史记录：端口 -> 切换记录 */
    private final ConcurrentMap<String, UserSwitchRecord> switchHistory = new ConcurrentHashMap<>();

    /**
     * 用户切换记录
     */
    public static class UserSwitchRecord {
        private String portDescriptor;
        private String oldUserId;
        private String newUserId;
        private long switchTime;
        private String reason;

        public UserSwitchRecord(String portDescriptor, String oldUserId, String newUserId, String reason) {
            this.portDescriptor = portDescriptor;
            this.oldUserId = oldUserId;
            this.newUserId = newUserId;
            this.switchTime = System.currentTimeMillis();
            this.reason = reason;
        }

        // Getters
        public String getPortDescriptor() { return portDescriptor; }
        public String getOldUserId() { return oldUserId; }
        public String getNewUserId() { return newUserId; }
        public long getSwitchTime() { return switchTime; }
        public String getReason() { return reason; }
    }

    /**
     * 监听用户切换事件
     */
    @EventListener
    @Async
    public void handleUserSwitchEvent(UserSwitchEvent event) {
        try {
            switch (event.getEventType()) {
                case USER_KICKED:
                    handleUserKickedEvent(event);
                    break;
                case SWITCH_SUCCESS:
                    handleSwitchSuccessEvent(event);
                    break;
                case SWITCH_BROADCAST:
                    handleBroadcastEvent(event);
                    break;
                default:
                    log.warn("未知的用户切换事件类型: {}", event.getEventType());
            }
        } catch (Exception e) {
            log.error("处理用户切换事件失败: {}", event.getEventDescription(), e);
        }
    }

    /**
     * 处理用户被踢掉事件
     */
    private void handleUserKickedEvent(UserSwitchEvent event) {
        notifyUserKicked(event.getOldUserId(), event.getPortDescriptor(),
                        event.getNewUserId(), event.getReason());
    }

    /**
     * 处理切换成功事件
     */
    private void handleSwitchSuccessEvent(UserSwitchEvent event) {
        notifyUserSwitchSuccess(event.getNewUserId(), event.getPortDescriptor(),
                               event.getOldUserId());
    }

    /**
     * 处理广播事件
     */
    private void handleBroadcastEvent(UserSwitchEvent event) {
        broadcastUserSwitch(event.getPortDescriptor(), event.getOldUserId(),
                           event.getNewUserId(), event.getReason());
    }

    /**
     * 通知用户被踢掉
     */
    private void notifyUserKicked(String oldUserId, String portDescriptor, String newUserId, String reason) {
        try {
            log.info("通知用户被踢掉: 原用户={}, 端口={}, 新用户={}, 原因={}", oldUserId, portDescriptor, newUserId, reason);

            // 1. 记录切换历史
            UserSwitchRecord record = new UserSwitchRecord(portDescriptor, oldUserId, newUserId, reason);
            switchHistory.put(portDescriptor, record);

            // 2. 获取原用户的Socket.IO客户端
            SocketIOClient oldUserClient = socketIOService.getUserClient(oldUserId);
            if (oldUserClient != null) {
                // 3. 发送被踢掉通知
                Map<String, Object> kickEvent = createKickEvent(portDescriptor, newUserId, reason);
                oldUserClient.sendEvent("userKicked", kickEvent);

                // 4. 发送设备断开连接事件
                Map<String, Object> disconnectEvent = createDeviceDisconnectEvent(portDescriptor, reason);
                oldUserClient.sendEvent("deviceDisconnected", disconnectEvent);

                log.info("用户被踢掉通知发送成功: 原用户={}, 端口={}", oldUserId, portDescriptor);
            } else {
                log.warn("未找到原用户的Socket.IO连接: 原用户={}", oldUserId);
            }

        } catch (Exception e) {
            log.error("通知用户被踢掉失败: 原用户={}, 端口={}, 新用户={}", oldUserId, portDescriptor, newUserId, e);
        }
    }

    /**
     * 通知用户切换成功
     */
    private void notifyUserSwitchSuccess(String newUserId, String portDescriptor, String oldUserId) {
        try {
            log.info("通知用户切换成功: 新用户={}, 端口={}, 原用户={}", newUserId, portDescriptor, oldUserId);

            // 1. 获取新用户的Socket.IO客户端
            SocketIOClient newUserClient = socketIOService.getUserClient(newUserId);
            if (newUserClient != null) {
                // 2. 发送切换成功通知
                Map<String, Object> successEvent = createSwitchSuccessEvent(portDescriptor, oldUserId);
                newUserClient.sendEvent("userSwitched", successEvent);

                log.info("用户切换成功通知发送成功: 新用户={}, 端口={}", newUserId, portDescriptor);
            } else {
                log.warn("未找到新用户的Socket.IO连接: 新用户={}", newUserId);
            }

        } catch (Exception e) {
            log.error("通知用户切换成功失败: 新用户={}, 端口={}, 原用户={}", newUserId, portDescriptor, oldUserId, e);
        }
    }

    /**
     * 广播用户切换事件（可选，用于管理员监控）
     */
    private void broadcastUserSwitch(String portDescriptor, String oldUserId, String newUserId, String reason) {
        try {
            Map<String, Object> broadcastEvent = new HashMap<>();
            broadcastEvent.put("event", "USER_SWITCH_BROADCAST");
            broadcastEvent.put("portDescriptor", portDescriptor);
            broadcastEvent.put("oldUserId", oldUserId);
            broadcastEvent.put("newUserId", newUserId);
            broadcastEvent.put("reason", reason);
            broadcastEvent.put("timestamp", System.currentTimeMillis());

            // 广播给所有连接的客户端（管理员可以监控）
            socketIOService.broadcastEvent("userSwitchBroadcast", broadcastEvent);

            log.debug("用户切换广播发送成功: 端口={}, 原用户={}, 新用户={}", portDescriptor, oldUserId, newUserId);

        } catch (Exception e) {
            log.error("广播用户切换事件失败: 端口={}, 原用户={}, 新用户={}", portDescriptor, oldUserId, newUserId, e);
        }
    }

    /**
     * 创建被踢掉事件
     */
    private Map<String, Object> createKickEvent(String portDescriptor, String newUserId, String reason) {
        Map<String, Object> event = new HashMap<>();
        event.put("event", "USER_KICKED");
        event.put("message", String.format("您已被新用户踢掉，串口 %s 的连接已断开", portDescriptor));
        event.put("portDescriptor", portDescriptor);
        event.put("newUserId", newUserId);
        event.put("reason", reason);
        event.put("timestamp", System.currentTimeMillis());
        event.put("action", "RECONNECT_REQUIRED"); // 提示用户需要重新连接
        return event;
    }

    /**
     * 创建设备断开连接事件
     */
    private Map<String, Object> createDeviceDisconnectEvent(String portDescriptor, String reason) {
        Map<String, Object> event = new HashMap<>();
        event.put("event", "DEVICE_DISCONNECTED");
        event.put("portDescriptor", portDescriptor);
        event.put("reason", reason);
        event.put("forced", true); // 标记为强制断开
        event.put("timestamp", System.currentTimeMillis());
        return event;
    }

    /**
     * 创建切换成功事件
     */
    private Map<String, Object> createSwitchSuccessEvent(String portDescriptor, String oldUserId) {
        Map<String, Object> event = new HashMap<>();
        event.put("event", "USER_SWITCH_SUCCESS");
        event.put("message", String.format("成功接管串口 %s，原用户已被踢掉", portDescriptor));
        event.put("portDescriptor", portDescriptor);
        event.put("oldUserId", oldUserId);
        event.put("timestamp", System.currentTimeMillis());
        return event;
    }
}
