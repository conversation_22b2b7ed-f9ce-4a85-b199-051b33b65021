package org.bj.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bj.config.AudiometryProperties;
import org.bj.device.audiometry.entity.ElectricalAudiometryResult;
import org.bj.device.entity.ExamItemInfo;
import org.bj.device.entity.PatientInfo;
import org.bj.util.FileUploadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 电测听报告图片服务
 * 处理电测听报告图片的异步上传和管理
 *
 * <AUTHOR> Agent
 * @date 2025-07-09
 */
@Slf4j
@Service
@Profile("audiometry")
public class AudiometryReportImageService {

    @Autowired
    @Qualifier("mainJdbcTemplate")
    private JdbcTemplate mainJdbcTemplate;

    @Autowired
    private AudiometryProperties audiometryProperties;

    @Autowired(required = false)
    private FileUploadUtil fileUploadUtil;

    /** 待处理的报告图片任务队列 */
    private final Map<String, ReportImageTask> pendingTasks = new ConcurrentHashMap<>();

    /**
     * 报告图片处理任务
     */
    @Data
    public static class ReportImageTask {
        private final String examNo;
        private final String customerRegItemGroupId;
        private final PatientInfo patientInfo;
        private final ExamItemInfo examItemInfo;
        private final ElectricalAudiometryResult hearingTestResult;
        private final String deviceModel;
        private final LocalDateTime createTime;
        private int retryCount = 0;

        public ReportImageTask(String examNo, String customerRegItemGroupId, PatientInfo patientInfo, 
                              ExamItemInfo examItemInfo, ElectricalAudiometryResult hearingTestResult, String deviceModel) {
            this.examNo = examNo;
            this.customerRegItemGroupId = customerRegItemGroupId;
            this.patientInfo = patientInfo;
            this.examItemInfo = examItemInfo;
            this.hearingTestResult = hearingTestResult;
            this.deviceModel = deviceModel;
            this.createTime = LocalDateTime.now();
        }
    }

    /**
     * 添加报告图片处理任务
     */
    public void addReportImageTask(String examNo, String customerRegItemGroupId, PatientInfo patientInfo,
                                   ExamItemInfo examItemInfo, ElectricalAudiometryResult hearingTestResult, String deviceModel) {
        ReportImageTask task = new ReportImageTask(examNo, customerRegItemGroupId, patientInfo, examItemInfo, hearingTestResult, deviceModel);
        pendingTasks.put(examNo, task);
        log.info("添加电测听报告图片处理任务: 体检号={}", examNo);
    }

    /**
     * 处理报告图片任务
     * 由定时任务配置类调用
     */
    @Async
    public void processReportImageTasks() {
        // 1. 处理队列中的任务
        processQueuedTasks();

        // 2. 扫描数据库中需要补充图片的记录
        scanDatabaseForMissingImages();
    }

    /**
     * 处理队列中的任务
     */
    private void processQueuedTasks() {
        if (pendingTasks.isEmpty()) {
            return;
        }

        log.debug("开始处理电测听报告图片队列任务，队列大小: {}", pendingTasks.size());

        Iterator<Map.Entry<String, ReportImageTask>> iterator = pendingTasks.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, ReportImageTask> entry = iterator.next();
            ReportImageTask task = entry.getValue();

            try {
                // 检查任务是否超时
                if (isTaskExpired(task)) {
                    log.warn("电测听报告图片任务超时，移除任务: 体检号={}", task.getExamNo());
                    iterator.remove();
                    continue;
                }

                // 处理任务
                boolean success = processReportImageTask(task);
                if (success) {
                    log.info("电测听报告图片任务处理成功: 体检号={}", task.getExamNo());
                    iterator.remove();
                } else {
                    // 增加重试次数
                    task.setRetryCount(task.getRetryCount() + 1);
                    if (task.getRetryCount() >= audiometryProperties.getReport().getMaxRetryCount()) {
                        log.warn("电测听报告图片任务重试次数超限，移除任务: 体检号={}, 重试次数={}", 
                            task.getExamNo(), task.getRetryCount());
                        iterator.remove();
                    }
                }

            } catch (Exception e) {
                log.error("处理电测听报告图片任务异常: 体检号={}", task.getExamNo(), e);
                task.setRetryCount(task.getRetryCount() + 1);
                if (task.getRetryCount() >= audiometryProperties.getReport().getMaxRetryCount()) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 扫描数据库中需要补充图片的记录
     */
    private void scanDatabaseForMissingImages() {
        try {
            String querySql = """
                    SELECT id, exam_no, his_code, item_group_name, check_time
                    FROM customer_reg_item_group 
                    WHERE his_code = ? 
                      AND check_status = '已检'
                      AND (report_pics IS NULL OR report_pics = '' OR report_pics = '[]')
                      AND check_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    ORDER BY check_time DESC
                    LIMIT 50
                    """;

            List<Map<String, Object>> records = mainJdbcTemplate.queryForList(querySql, audiometryProperties.getHisCode());

            if (!records.isEmpty()) {
                log.info("发现需要补充报告图片的电测听记录数量: {}", records.size());

                for (Map<String, Object> record : records) {
                    try {
                        processImageSyncRecord(record);
                    } catch (Exception e) {
                        log.error("处理图片同步记录失败: 记录ID={}, 体检号={}", record.get("id"), record.get("exam_no"), e);
                    }
                }
            } else {
                log.debug("未发现需要补充报告图片的电测听记录");
            }

        } catch (Exception e) {
            log.error("扫描数据库中需要补充图片的记录失败", e);
        }
    }

    /**
     * 处理单个图片同步记录
     */
    private void processImageSyncRecord(Map<String, Object> record) {
        try {
            String examNo = (String) record.get("exam_no");
            String customerRegItemGroupId = record.get("id").toString();
            String hisCode = (String) record.get("his_code");
            String itemGroupName = (String) record.get("item_group_name");

            log.debug("处理电测听图片同步记录: 体检号={}, 项目组ID={}, his_code={}, 项目名称={}", 
                examNo, customerRegItemGroupId, hisCode, itemGroupName);

            // 使用统一的预定义文件名方式扫描并上传报告图片
            List<String> reportImageUrls = new ArrayList<>();
            String imageFormat = audiometryProperties.getReport().getImageFormat();
            List<String> predefinedFileNames = generatePredefinedFileNames(examNo, imageFormat);

            String reportDirectory = audiometryProperties.getReport().getImageDirectory();
            for (String fileName : predefinedFileNames) {
                String fullPath = reportDirectory + "/" + fileName;
                File reportFile = new File(fullPath);

                if (reportFile.exists() && reportFile.isFile()) {
                    try {
                        String uploadedUrl = uploadReportImage(reportFile);
                        if (uploadedUrl != null && !uploadedUrl.isEmpty()) {
                            reportImageUrls.add(uploadedUrl);
                            log.info("电测听报告图片上传成功: 文件={}, URL={}", fileName, uploadedUrl);
                        }
                    } catch (Exception e) {
                        log.warn("电测听报告图片上传失败: 文件={}, 错误={}", fileName, e.getMessage());
                    }
                }
            }

            // 更新数据库
            if (!reportImageUrls.isEmpty()) {
                updateReportImageUrls(customerRegItemGroupId, reportImageUrls);
                log.info("电测听图片同步完成: 体检号={}, 图片数量={}", examNo, reportImageUrls.size());
            } else {
                log.debug("未找到电测听报告图片: 体检号={}", examNo);
            }

        } catch (Exception e) {
            log.error("处理电测听图片同步记录失败", e);
        }
    }

    /**
     * 处理单个报告图片任务
     */
    private boolean processReportImageTask(ReportImageTask task) {
        try {
            String examNo = task.getExamNo();

            // 1. 扫描报告图片文件
            List<String> reportImageUrls = scanAndUploadReportImages(task.getPatientInfo());

            if (reportImageUrls.isEmpty()) {
                log.debug("未找到电测听报告图片文件: 体检号={}", examNo);
                return false; // 图片还未生成，继续等待
            }

            // 2. 更新数据库中的报告图片URL
            updateReportImageUrls(task.getCustomerRegItemGroupId(), reportImageUrls);

            log.info("电测听报告图片处理完成: 体检号={}, 图片数量={}", examNo, reportImageUrls.size());
            return true;

        } catch (Exception e) {
            log.error("处理电测听报告图片任务失败: 体检号={}", task.getExamNo(), e);
            return false;
        }
    }

    /**
     * 扫描并上传报告图片
     */
    private List<String> scanAndUploadReportImages(PatientInfo patientInfo) {
        List<String> uploadedUrls = new ArrayList<>();

        try {
            String examNo = patientInfo.getExamNo();
            String reportDirectory = audiometryProperties.getReport().getImageDirectory();
            String imageFormat = audiometryProperties.getReport().getImageFormat();

            log.debug("开始检查电测听报告图片: 体检号={}, 目录={}", examNo, reportDirectory);

            // 预定义图片文件名
            List<String> predefinedFileNames = generatePredefinedFileNames(examNo, imageFormat);

            // 逐个检查预定义的文件名是否存在
            for (String fileName : predefinedFileNames) {
                String fullPath = reportDirectory + "/" + fileName;
                File reportFile = new File(fullPath);

                if (reportFile.exists() && reportFile.isFile()) {
                    try {
                        String uploadedUrl = uploadReportImage(reportFile);
                        if (uploadedUrl != null && !uploadedUrl.isEmpty()) {
                            uploadedUrls.add(uploadedUrl);
                            log.info("电测听报告图片上传成功: 文件={}, URL={}", fileName, uploadedUrl);
                        }
                    } catch (Exception e) {
                        log.warn("电测听报告图片上传失败: 文件={}, 错误={}", fileName, e.getMessage());
                    }
                } else {
                    log.debug("电测听报告图片文件不存在: {}", fullPath);
                }
            }

            if (uploadedUrls.isEmpty()) {
                log.debug("未找到任何电测听报告图片文件: 体检号={}, 检查的文件名={}", examNo, predefinedFileNames);
            } else {
                log.info("成功上传电测听报告图片: 体检号={}, 图片数量={}", examNo, uploadedUrls.size());
            }

        } catch (Exception e) {
            log.error("扫描电测听报告图片失败: 患者={}", patientInfo.getName(), e);
        }

        return uploadedUrls;
    }

    /**
     * 生成预定义文件名列表
     */
    private List<String> generatePredefinedFileNames(String examNo, String imageFormat) {
        List<String> fileNames = new ArrayList<>();
        String pattern = audiometryProperties.getReport().getFileNamePattern();

        // 生成多个可能的文件名（通常电测听报告有1-3张图片）
        for (int i = 0; i < 5; i++) {
            String fileName = pattern
                .replace("{examNo}", examNo)
                .replace("{序号}", String.valueOf(i))
                .replace("{index}", String.valueOf(i));
            
            if (!fileName.endsWith("." + imageFormat)) {
                fileName += "." + imageFormat;
            }
            
            fileNames.add(fileName);
        }

        return fileNames;
    }

    /**
     * 上传报告图片到MinIO
     */
    private String uploadReportImage(File reportFile) {
        if (fileUploadUtil == null) {
            log.warn("FileUploadUtil未配置，跳过图片上传");
            return "";
        }

        try {
            // 构建对象路径
            String extension = getFileExtension(reportFile.getName());
            String objectPath = "audiometry-reports/" + UUID.randomUUID() + "." + extension;

            // 上传文件
            Map<String, Object> uploadResult = fileUploadUtil.uploadToMinio(reportFile, objectPath);

            if (uploadResult != null && Boolean.TRUE.equals(uploadResult.get("success"))) {
                String downloadUrl = (String) uploadResult.get("downloadUrl");
                log.info("电测听报告图片上传成功: 本地文件={}, 上传路径={}, URL={}", 
                    reportFile.getAbsolutePath(), objectPath, downloadUrl);
                return downloadUrl;
            } else {
                String errorMessage = uploadResult != null ? (String) uploadResult.get("message") : "未知错误";
                log.warn("电测听报告图片上传失败: 文件={}, 错误={}", reportFile.getName(), errorMessage);
                return "";
            }

        } catch (Exception e) {
            log.error("电测听报告图片上传异常: 文件={}", reportFile.getName(), e);
            return "";
        }
    }

    /**
     * 更新报告图片URL到数据库
     */
    private void updateReportImageUrls(String customerRegItemGroupId, List<String> imageUrls) {
        try {
            String jsonArray = "[\"" + String.join("\",\"", imageUrls) + "\"]";
            
            String updateSql = """
                    UPDATE customer_reg_item_group 
                    SET report_pics = ?, update_time = ? 
                    WHERE id = ?
                    """;

            mainJdbcTemplate.update(updateSql, jsonArray, LocalDateTime.now(), customerRegItemGroupId);
            log.info("更新电测听报告图片URL成功: itemGroupId={}, 图片数量={}", customerRegItemGroupId, imageUrls.size());

        } catch (Exception e) {
            log.error("更新电测听报告图片URL失败: itemGroupId={}", customerRegItemGroupId, e);
        }
    }

    /**
     * 检查任务是否过期
     */
    private boolean isTaskExpired(ReportImageTask task) {
        LocalDateTime expireTime = task.getCreateTime().plusHours(audiometryProperties.getReport().getTaskTimeoutHours());
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        return "png"; // 默认扩展名
    }
}
