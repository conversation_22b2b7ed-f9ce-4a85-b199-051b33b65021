package org.bj.service;

import lombok.extern.slf4j.Slf4j;
import org.bj.config.FGY200Properties;
import org.bj.device.entity.PatientInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 肺功能表达式解析服务
 * 
 * 使用Spring Expression Language (SpEL)解析和计算肺功能异常值判断公式
 * 
 * <AUTHOR> Agent
 * @date 2025-07-06
 */
@Slf4j
@Service
@Profile("fgy200")
public class LungFunctionExpressionService {

    private final ExpressionParser parser = new SpelExpressionParser();

    @Autowired
    private FGY200Properties fgy200Properties;

    /**
     * 异常判断结果
     */
    public static class AbnormalResult {
        private final String flag;
        private final String description;
        private final String symbol;
        private final String ruleName;

        public AbnormalResult(String flag, String description, String symbol, String ruleName) {
            this.flag = flag;
            this.description = description;
            this.symbol = symbol;
            this.ruleName = ruleName;
        }

        public String getFlag() { return flag; }
        public String getDescription() { return description; }
        public String getSymbol() { return symbol; }
        public String getRuleName() { return ruleName; }

        public boolean isAbnormal() {
            return !"0".equals(flag);
        }
    }

    /**
     * 计算指标的异常状态
     *
     * @param indicator 指标配置
     * @param value 指标值
     * @param allValues 所有指标值（用于复杂计算）
     * @return 异常判断结果
     */
    public AbnormalResult calculateAbnormalStatus(FGY200Properties.LungFunction.Indicator indicator,
                                                Object value, Map<String, Object> allValues) {
        return calculateAbnormalStatus(indicator, value, allValues, null);
    }

    /**
     * 计算指标的异常状态（支持患者信息）
     *
     * @param indicator 指标配置
     * @param value 指标值
     * @param allValues 所有指标值（用于复杂计算）
     * @param patientInfo 患者信息（用于预期值计算）
     * @return 异常判断结果
     */
    public AbnormalResult calculateAbnormalStatus(FGY200Properties.LungFunction.Indicator indicator,
                                                Object value, Map<String, Object> allValues, PatientInfo patientInfo) {
        try {
            if (value == null) {
                return new AbnormalResult("0", "正常", "", "默认");
            }

            // 解析数值
            double numericValue;
            try {
                numericValue = Double.parseDouble(value.toString());
            } catch (NumberFormatException e) {
                log.warn("无法解析指标值为数字: {} = {}", indicator.getCode(), value);
                return new AbnormalResult("0", "正常", "", "默认");
            }

            // 扩展所有指标值，包含预期值
            Map<String, Object> extendedValues = enhanceValuesWithPredicted(allValues, patientInfo);

            // 按优先级排序规则
            var sortedRules = indicator.getAbnormalRules().stream()
                    .filter(FGY200Properties.LungFunction.AbnormalRule::isEnabled)
                    .sorted((r1, r2) -> Integer.compare(r1.getPriority(), r2.getPriority()))
                    .toList();

            // 逐个检查规则
            for (var rule : sortedRules) {
                try {
                    if (evaluateCondition(rule.getCondition(), numericValue, extendedValues)) {
                        log.debug("指标 {} 匹配异常规则: {} -> {}",
                                indicator.getCode(), rule.getName(), rule.getDescription());
                        return new AbnormalResult(rule.getFlag(), rule.getDescription(),
                                                rule.getSymbol(), rule.getName());
                    }
                } catch (Exception e) {
                    log.error("计算异常规则失败: 指标={}, 规则={}, 表达式={}",
                            indicator.getCode(), rule.getName(), rule.getCondition(), e);
                }
            }

            // 没有匹配的异常规则，返回正常
            return new AbnormalResult("0", "正常", "", "默认");

        } catch (Exception e) {
            log.error("计算指标异常状态失败: {}", indicator.getCode(), e);
            return new AbnormalResult("0", "正常", "", "默认");
        }
    }

    /**
     * 评估条件表达式
     * 
     * @param condition 条件表达式
     * @param value 当前指标值
     * @param allValues 所有指标值
     * @return 是否满足条件
     */
    private boolean evaluateCondition(String condition, double value, Map<String, Object> allValues) {
        try {
            // 解析表达式
            Expression expression = parser.parseExpression(condition);
            
            // 创建评估上下文
            StandardEvaluationContext context = new StandardEvaluationContext();
            
            // 设置当前值变量
            context.setVariable("value", value);

            // 设置所有指标值变量
            if (allValues != null) {
                for (Map.Entry<String, Object> entry : allValues.entrySet()) {
                    String key = entry.getKey();
                    Object val = entry.getValue();

                    // 尝试转换为数字
                    if (val != null) {
                        try {
                            double numVal = Double.parseDouble(val.toString());
                            context.setVariable(key, numVal);
                        } catch (NumberFormatException e) {
                            // 保持原始值
                            context.setVariable(key, val);
                        }
                    }
                }
            }
            
            // 计算表达式结果
            Boolean result = expression.getValue(context, Boolean.class);
            return result != null && result;
            
        } catch (Exception e) {
            log.error("评估条件表达式失败: {}", condition, e);
            return false;
        }
    }

    /**
     * 验证表达式语法
     * 
     * @param expression 表达式
     * @return 是否有效
     */
    public boolean validateExpression(String expression) {
        try {
            parser.parseExpression(expression);
            return true;
        } catch (Exception e) {
            log.warn("表达式语法无效: {}", expression, e);
            return false;
        }
    }

    /**
     * 增强指标值，添加预期值和计算值
     *
     * @param allValues 原始指标值
     * @param patientInfo 患者信息
     * @return 增强后的指标值
     */
    private Map<String, Object> enhanceValuesWithPredicted(Map<String, Object> allValues, PatientInfo patientInfo) {
        Map<String, Object> enhanced = new HashMap<>();
        if (allValues != null) {
            enhanced.putAll(allValues);
        }

        try {
            // 检查是否启用预期值计算
            if (fgy200Properties.getLungFunction().getPredictedValues() == null ||
                !fgy200Properties.getLungFunction().getPredictedValues().isEnabled()) {
                return enhanced;
            }

            var predictedConfig = fgy200Properties.getLungFunction().getPredictedValues();

            // 1. 从数据中提取预期值（如果设备提供）
            if (predictedConfig.getFieldMapping() != null) {
                for (Map.Entry<String, String> entry : predictedConfig.getFieldMapping().entrySet()) {
                    String predictedKey = entry.getKey();
                    String dataFieldName = entry.getValue();

                    Object predictedValue = allValues != null ? allValues.get(dataFieldName) : null;
                    if (predictedValue != null) {
                        try {
                            double numValue = Double.parseDouble(predictedValue.toString());
                            enhanced.put(predictedKey, numValue);
                            log.debug("添加预期值: {} = {}", predictedKey, numValue);
                        } catch (NumberFormatException e) {
                            log.warn("预期值格式错误: {} = {}", predictedKey, predictedValue);
                        }
                    }
                }
            }

            // 2. 如果没有预期值且有患者信息，尝试计算预期值
            if (patientInfo != null && predictedConfig.getCalculationFormulas() != null) {
                calculatePredictedValues(enhanced, patientInfo, predictedConfig.getCalculationFormulas());
            }

        } catch (Exception e) {
            log.error("增强指标值时发生错误", e);
        }

        return enhanced;
    }

    /**
     * 根据患者信息计算预期值
     *
     * @param enhanced 增强的指标值Map
     * @param patientInfo 患者信息
     * @param formulas 计算公式
     */
    private void calculatePredictedValues(Map<String, Object> enhanced, PatientInfo patientInfo,
                                        Map<String, String> formulas) {
        try {
            // 添加患者基本信息到计算上下文
           /* if (patientInfo.getAge() != null) {
                enhanced.put("age", patientInfo.getAge());
            }
            if (patientInfo.getHeight() != null) {
                enhanced.put("height", patientInfo.getHeight());
            }
            if (patientInfo.getWeight() != null) {
                enhanced.put("weight", patientInfo.getWeight());
            }

            String gender = patientInfo.getGender();
            boolean isMale = "男".equals(gender) || "M".equals(gender) || "1".equals(gender);
            enhanced.put("isMale", isMale);
            enhanced.put("isFemale", !isMale);

            // 计算预期值
            for (Map.Entry<String, String> entry : formulas.entrySet()) {
                String predictedKey = entry.getKey();
                String formula = entry.getValue();

                // 根据性别选择合适的公式
                if ((predictedKey.contains("_MALE") && !isMale) ||
                    (predictedKey.contains("_FEMALE") && isMale)) {
                    continue;
                }

                try {
                    Object result = testExpression(formula, 0, enhanced);
                    if (result instanceof Number) {
                        String baseKey = predictedKey.replace("_MALE", "").replace("_FEMALE", "");
                        enhanced.put(baseKey, ((Number) result).doubleValue());
                        log.debug("计算预期值: {} = {}", baseKey, result);
                    }
                } catch (Exception e) {
                    log.warn("计算预期值失败: {} = {}", predictedKey, formula, e);
                }
            }*/

        } catch (Exception e) {
            log.error("计算预期值时发生错误", e);
        }
    }

    /**
     * 测试表达式计算
     *
     * @param expression 表达式
     * @param testValue 测试值
     * @param testVariables 测试变量
     * @return 计算结果
     */
    public Object testExpression(String expression, double testValue, Map<String, Object> testVariables) {
        try {
            Expression exp = parser.parseExpression(expression);
            StandardEvaluationContext context = new StandardEvaluationContext();

            context.setVariable("value", testValue);
            if (testVariables != null) {
                testVariables.forEach(context::setVariable);
            }

            return exp.getValue(context);
        } catch (Exception e) {
            log.error("测试表达式失败: {}", expression, e);
            return null;
        }
    }
}
