package org.bj.service;

import cn.hutool.json.JSONObject;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.fazecast.jSerialComm.SerialPort;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.bj.constants.EventConstants;
import org.bj.device.DeviceHandler;
import org.bj.device.DeviceSessionManager;
import org.bj.device.entity.ConnectRequest;
import org.bj.device.entity.SerialPortData;
import org.bj.device.entity.UserSession;
import org.bj.device.serialport.SerialPortResourceManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@Slf4j
@Service
public class SocketIOService {

    @Autowired
    private SocketIOServer server;
    @Autowired
    private DeviceSessionManager deviceSessionManager;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private SerialPortResourceManager serialPortResourceManager;

    private final ConcurrentHashMap<String, UserSession> userSessions = new ConcurrentHashMap<>();

    /**
     * 延迟获取DeviceHandler，避免循环依赖
     */
    private DeviceHandler getDeviceHandler() {
        return applicationContext.getBean(DeviceHandler.class);
    }

    @PostConstruct
    private void startServer() {
        try {
            log.info("开始初始化 Socket.IO 服务器...");
            log.info("Socket.IO 配置 - 主机: {}, 端口: {}", server.getConfiguration().getHostname(), server.getConfiguration().getPort());

            server.addConnectListener(client -> {
                String userId = client.getHandshakeData().getSingleUrlParam("userId");

                // 处理userId为空的情况
                if (userId == null || userId.trim().isEmpty()) {
                    userId = "anonymous-" + client.getSessionId().toString();
                    log.warn("客户端未提供userId，使用默认值: {}", userId);
                }

                userSessions.put(userId, new UserSession(client));
                log.info("用户连接成功: {} (SessionId: {})", userId, client.getSessionId());
            });

            server.addDisconnectListener(client -> {
                String userId = client.getHandshakeData().getSingleUrlParam("userId");

                // 处理userId为空的情况
                if (userId == null || userId.trim().isEmpty()) {
                    userId = "anonymous-" + client.getSessionId().toString();
                }

                // 清理用户会话
                userSessions.remove(userId);

                // 自动清理该用户的所有串口资源
                try {
                    // 1. 清理设备处理器资源
                    getDeviceHandler().cleanupUserResources(userId);

                    // 2. 清理串口资源管理器中的资源
                    serialPortResourceManager.cleanupUserPorts(userId);

                    log.info("用户断开连接并清理资源: {} (SessionId: {})", userId, client.getSessionId());
                } catch (Exception e) {
                    log.error("清理用户资源失败: 用户={}, SessionId={}", userId, client.getSessionId(), e);
                }
            });

            // 统一的connectPort事件处理 - 支持传统模式和体检模式
            server.addEventListener("connectPort", ConnectRequest.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                //将requestObj转换为JSON字符串
                String requestJson = new JSONObject(requestObj).toString();
                log.info("connectPort事件触发: 用户={}, 请求={}", rawUserId, requestJson);

                // 处理userId为空的情况
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理连接请求
                getDeviceHandler().handleConnect(client, requestObj, userId);
            });

            // 断开端口连接事件
            server.addEventListener("disconnectPort", Object.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理断开请求
                getDeviceHandler().handleDisconnectPort(client, requestObj, userId);
            });

            // 发送串口命令事件
            server.addEventListener("sendPortCmd", SerialPortData.class, (client, message, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理命令发送
                getDeviceHandler().handleSendCommand(client, message, userId);
            });

            // 开始测量事件
            server.addEventListener("startMeasurement", Object.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理测量启动
                getDeviceHandler().handleStartMeasurement(client, requestObj, userId);
            });

            // 停止测量事件
            server.addEventListener("stopMeasurement", Object.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理测量停止
                getDeviceHandler().handleStopMeasurement(client, requestObj, userId);
            });

            // 重置设备事件
            server.addEventListener("resetDevice", Object.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理设备重置
                getDeviceHandler().handleResetDevice(client, requestObj, userId);
            });

            // 校准设备事件
            server.addEventListener("calibrateDevice", Object.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一串口处理器处理设备校准
                getDeviceHandler().handleCalibrateDevice(client, requestObj, userId);
            });

            // 获取设备统计信息事件
            server.addEventListener("getDeviceStatistics", Object.class, (client, data, ackRequest) -> {
                try {
                    java.util.Map<String, Object> stats = deviceSessionManager.getDeviceStatistics();
                    client.sendEvent(EventConstants.DEVICE_OPERATION_RESULT, stats);
                } catch (Exception e) {
                    log.error("获取设备统计信息失败", e);
                }
            });

            // 查询测量结果事件
            server.addEventListener("queryResults", ConnectRequest.class, (client, requestObj, ackRequest) -> {
                String rawUserId = client.getHandshakeData().getSingleUrlParam("userId");
                final String userId = (rawUserId == null || rawUserId.trim().isEmpty()) ? "anonymous-" + client.getSessionId().toString() : rawUserId;

                // 使用统一设备处理器处理查询测量结果
                getDeviceHandler().handleQueryResults(client, requestObj, userId);
            });

            log.info("正在启动 Socket.IO 服务器...");
            server.start();
            log.info("Socket.IO 服务器 已启动成功!");
            log.info("Socket.IO 服务器监听地址: {}:{}", server.getConfiguration().getHostname(), server.getConfiguration().getPort());
        } catch (Exception e) {
            log.error("Socket.IO 服务器启动失败!", e);
            throw new RuntimeException("Socket.IO 服务器启动失败", e);
        }
    }


    @PreDestroy
    private void stopServer() {
        server.stop();
    }


    /**
     * 获取用户的Socket.IO客户端
     */
    public SocketIOClient getUserClient(String userId) {
        UserSession session = userSessions.get(userId);
        return (session != null && session.getClient().isChannelOpen()) ? session.getClient() : null;
    }

    /**
     * 广播事件给所有连接的客户端
     */
    public void broadcastEvent(String eventName, Object data) {
        try {
            server.getBroadcastOperations().sendEvent(eventName, data);
            log.debug("广播事件: 事件={}, 连接数={}", eventName, userSessions.size());
        } catch (Exception e) {
            log.error("广播事件失败: 事件={}", eventName, e);
        }
    }
}