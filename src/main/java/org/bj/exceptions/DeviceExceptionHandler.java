package org.bj.exceptions;

import com.corundumstudio.socketio.SocketIOClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 统一异常处理器
 * 提供全局异常处理功能，不依赖特定的设备上下文
 * 负责归并各种异常事件并统一通知给体检系统
 *
 * <AUTHOR> Agent
 * @date 2025-07-07
 */
@Slf4j
@Service
public class DeviceExceptionHandler {

    /**
     * 异常统计映射
     */
    private final ConcurrentMap<String, ExceptionStatistics> exceptionStats = new ConcurrentHashMap<>();

    /**
     * 处理连接异常
     */
    public void handleConnectionException(SocketIOClient client, String userId, Exception exception) {
        try {
            String errorCode = "CONNECTION_ERROR";
            String errorMessage = "连接失败: " + exception.getMessage();

            log.error("连接异常: 用户={}, 端口={}, 错误={}", userId, errorMessage, exception);


            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);

        } catch (Exception e) {
            log.error("处理连接异常时发生错误", e);
        }
    }

    /**
     * 处理测量异常
     */
    public void handleMeasurementException(SocketIOClient client, String userId, Exception exception) {
        try {
            String errorCode = "MEASUREMENT_ERROR";
            String errorMessage = "测量失败: " + exception.getMessage();

            log.error("测量异常: 用户={}, 端口={}, 错误={}", userId, errorMessage, exception);


            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);

        } catch (Exception e) {
            log.error("处理测量异常时发生错误", e);
        }
    }

    /**
     * 处理数据处理异常
     */
    public void handleDataProcessingException(SocketIOClient client, String userId, String rawData, Exception exception) {
        try {
            String errorCode = "DATA_PROCESSING_ERROR";
            String errorMessage = "数据处理失败: " + exception.getMessage();

            log.error("数据处理异常: 用户={}, 端口={}, 数据={}, 错误={}", userId, rawData, errorMessage, exception);


            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);

        } catch (Exception e) {
            log.error("处理数据处理异常时发生错误", e);
        }
    }

    /**
     * 处理设备通信异常
     */
    public void handleDeviceCommunicationException(SocketIOClient client, String userId, String command, Exception exception) {
        try {
            String errorCode = "DEVICE_COMMUNICATION_ERROR";
            String errorMessage = "设备通信失败: " + exception.getMessage();

            log.error("设备通信异常: 用户={}, 端口={}, 指令={}, 错误={}", userId, command, errorMessage, exception);


            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);

        } catch (Exception e) {
            log.error("处理设备通信异常时发生错误", e);
        }
    }

    /**
     * 处理缓冲异常
     */
    public void handleBufferException(SocketIOClient client, String userId, String bufferData, Exception exception) {
        try {
            String errorCode = "BUFFER_ERROR";
            String errorMessage = "数据缓冲异常: " + exception.getMessage();

            log.error("缓冲异常: 用户={}, 端口={}, 缓冲数据={}, 错误={}", userId, bufferData, errorMessage, exception);


            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);

        } catch (Exception e) {
            log.error("处理缓冲异常时发生错误", e);
        }
    }

    /**
     * 处理超时异常
     */
    public void handleTimeoutException(SocketIOClient client, String userId, String portDescriptor) {
        try {
            String errorCode = "TIMEOUT_ERROR";
            String errorMessage = "操作超时";

            log.error("超时异常: 用户={}, 端口={}", userId, portDescriptor);

            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);

        } catch (Exception e) {
            log.error("处理超时异常时发生错误", e);
        }
    }

    /**
     * 处理通用异常
     */
    public void handleGenericException(SocketIOClient client, String userId, Exception exception) {
        try {
            String errorCode = "GENERIC_ERROR";
            String errorMessage = exception.getMessage();

            log.error("通用异常: 用户={}, 错误={}", userId, errorMessage, exception);

            // 发送标准错误事件
            sendStandardErrorEvent(client, errorMessage, errorCode);
        } catch (Exception e) {
            log.error("处理通用异常时发生错误", e);
        }
    }

    /**
     * 发送标准错误事件（保持原有事件名称兼容性）
     */
    private void sendStandardErrorEvent(SocketIOClient client, String errorMessage, String errorCode) {
        try {
            // 构造标准错误响应
            StandardErrorResponse errorResponse = new StandardErrorResponse();
            errorResponse.setErrorMessage(errorMessage);
            errorResponse.setErrorCode(errorCode);
            errorResponse.setTimestamp(System.currentTimeMillis());

            // 发送原有的错误事件（保持兼容性）
            client.sendEvent("globalError", errorResponse);

            log.debug("发送标准错误事件:错误码={}, 消息={}", errorCode, errorMessage);

        } catch (Exception e) {
            log.error("发送标准错误事件失败", e);
        }
    }


    /**
     * 标准错误响应
     */
    @Data
    public static class StandardErrorResponse {
        private String port;
        private String errorMessage;
        private String errorCode;
        private Long timestamp;
    }

    /**
     * 异常统计
     */
    @Data
    public static class ExceptionStatistics {
        private String errorCode;
        private String userId;
        private String portDescriptor;
        private Integer count = 0;
        private Long firstOccurrence = System.currentTimeMillis();
        private Long lastOccurrence = System.currentTimeMillis();

        public ExceptionStatistics(String errorCode, String userId, String portDescriptor) {
            this.errorCode = errorCode;
            this.userId = userId;
            this.portDescriptor = portDescriptor;
        }

        public void incrementCount() {
            this.count++;
            this.lastOccurrence = System.currentTimeMillis();
        }
    }
}
