-- FGY-200肺功能检测仪数据库表结构
-- 模拟Access数据库的"存储数据表"结构

CREATE TABLE 存储数据表 (
    -- 主键和基本信息
    ID_KEY INTEGER AUTO_INCREMENT PRIMARY KEY,
    门诊号 VARCHAR(50),
    科室 VARCHAR(100),
    工种 VARCHAR(100),
    吸烟情况 VARCHAR(100),
    用药情况 VARCHAR(500),
    试诊原因 VARCHAR(500),
    申请医师 VARCHAR(100),
    
    -- 患者基本信息
    ID VARCHAR(50),
    姓名 VARCHAR(100),
    性别 VARCHAR(10),
    年龄 INTEGER,
    身高 INTEGER,
    体重 INTEGER,
    BMI DECIMAL(5,2),
    日期 VARCHAR(20),
    时间 VARCHAR(20),
    
    -- 肺功能检测指标
    常规FVC数据 TEXT,
    FVC DECIMAL(8,2),
    FVC% DECIMAL(8,2),
    FEV1 DECIMAL(8,2),
    FEV1% DECIMAL(8,2),
    FEV2 DECIMAL(8,2),
    FEV3 DECIMAL(8,2),
    FEV6 DECIMAL(8,2),
    FEV1% DECIMAL(8,2),
    FEV_1 DECIMAL(8,2),
    FEV_2 DECIMAL(8,2),
    FEV_3 DECIMAL(8,2),
    PEF DECIMAL(8,2),
    VEXP DECIMAL(8,2),
    VC DECIMAL(8,2),
    MVV DECIMAL(8,2),
    
    -- 检测结果和状态
    诊断结果 TEXT,
    常规VC数据 TEXT,
    常规FEV1数据 TEXT,
    常规VE数据 TEXT,
    常规FVC原控 TEXT,
    常规FEV1原控 TEXT,
    受检者状态 VARCHAR(200),
    
    -- 质控数据
    常规FVC质控 TEXT,
    常规FEV1质控 TEXT,
    
    -- 波形数据
    常规FVC数据 TEXT,
    常规VC数据 TEXT,
    常规MVV数据 TEXT
);

-- 创建索引以提高查询性能
CREATE INDEX idx_patient_id ON 存储数据表(ID);
CREATE INDEX idx_test_date ON 存储数据表(日期);
CREATE INDEX idx_patient_name ON 存储数据表(姓名);
CREATE INDEX idx_primary_key ON 存储数据表(ID_KEY);
