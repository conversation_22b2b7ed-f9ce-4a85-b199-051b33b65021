# 测试环境配置文件

# 日志配置
logging:
  level:
    org.bj: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 数据库配置（测试环境）
database:
  main:
    enabled: false  # 测试环境默认禁用主数据库
    type: mysql
    host: localhost
    port: 3306
    database: physical_exam_test
    username: root
    password: 123456

# FGY-200设备配置（测试环境）
fgy200:
  # 测试环境路径
  program-path: "src/test/resources/fgy200"
  patient-file: "Tester.ini"
  database-file: "fei.mdb"
  file-encoding: "GBK"

  # 报告配置
  report:
    image-directory: "target/test-reports"
    image-format: "jpg"
    image-quality: 0.9
    image-width: 800
    image-height: 600

  # 医生配置（测试环境）
  doctor-config:
    check-doctor:
      code: "TEST_CHECK_DOC"
      name: "测试检查医生"
      sign-pic: ""
    report-doctor:
      code: "TEST_REPORT_DOC"
      name: "测试报告医生"
      sign-pic: ""
    audit-doctor:
      code: "TEST_AUDIT_DOC"
      name: "测试审核医生"
      sign-pic: ""

# 兼容旧配置格式
device:
  fgy200:
    data-table: "存储数据表"
    
    # 数据库字段配置
    database-fields:
      primary-key: "ID_KEY"
      patient-id: "ID"
      patient-name: "姓名"
      gender: "性别"
      age: "年龄"
      height: "身高"
      weight: "体重"
      bmi: "BMI"
      test-date: "日期"
      test-time: "时间"
      diagnosis: "诊断结果"
      patient-status: "受检者状态"
    
    # 肺功能指标字段配置
    lung-function-fields:
      fvc: "FVC"
      fvc-percent: "FVC%"
      fev1: "FEV1"
      fev1-percent: "FEV1%"
      fev1-ratio: "FEV_1"
      pef: "PEF"
      vc: "VC"
      mvv: "MVV"
      fvc-quality: "常规FVC质控"
      fev1-quality: "常规FEV1质控"
      fvc-waveform: "常规FVC数据"
      vc-waveform: "常规VC数据"
      mvv-waveform: "常规MVV数据"
    
    # 结果监控配置
    monitoring:
      interval: 5
      timeout: 60
    
    # SQL查询配置
    sql:
      select-latest: "SELECT * FROM {table} ORDER BY {primaryKey} DESC LIMIT 1"
      select-by-patient: "SELECT * FROM {table} WHERE {patientId} = ? AND {testDate} >= ? ORDER BY {primaryKey} DESC LIMIT 1"
      count-records: "SELECT COUNT(*) FROM {table}"

# Spring配置
spring:
  # 数据源配置（测试用）
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # H2数据库配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: none
    show-sql: true
    
# 测试配置
test:
  # 是否启用性能测试
  performance-test-enabled: true
  # 性能测试数据量
  performance-test-size: 100
  # 是否启用详细日志
  verbose-logging: true
