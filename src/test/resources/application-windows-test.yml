# Windows进程工具测试配置文件

# 日志配置
logging:
  level:
    org.bj.util.WindowsProcessUtil: DEBUG
    org.bj.util.WindowsProcessUtilTest: DEBUG
    org.springframework.test: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 测试配置
test:
  windows-process-util:
    # 测试超时时间（秒）
    timeout: 30
    # 是否启用性能测试
    performance-test-enabled: true
    # 性能测试迭代次数
    performance-test-iterations: 10
    # 是否启用并发测试
    concurrent-test-enabled: true
    # 并发测试线程数
    concurrent-test-threads: 5
    
    # 测试程序路径（根据实际环境调整）
    test-programs:
      notepad: "C:\\Windows\\System32\\notepad.exe"
      calculator: "C:\\Windows\\System32\\calc.exe"
      command-prompt: "C:\\Windows\\System32\\cmd.exe"
      explorer: "C:\\Windows\\explorer.exe"
    
    # 测试文件夹路径
    test-folders:
      temp: "${java.io.tmpdir}"
      windows: "C:\\Windows"
      system32: "C:\\Windows\\System32"
    
    # 测试命令
    test-commands:
      simple: "echo Hello World"
      with-args: "echo Test with arguments"
      invalid: "nonexistentcommand12345"
      long-running: "ping localhost -n 3"

# Spring配置
spring:
  # 测试数据源配置
  datasource:
    url: jdbc:h2:mem:windows-test-db
    driver-class-name: org.h2.Driver
    username: sa
    password:
  
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: false

# 系统属性配置（用于测试）
system:
  properties:
    # 临时目录
    temp-dir: "${java.io.tmpdir}"
    # 用户目录
    user-dir: "${user.home}"
    # 操作系统信息
    os-name: "${os.name}"
    os-version: "${os.version}"
    os-arch: "${os.arch}"
