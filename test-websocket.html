<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO WebSocket 测试工具</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        
        input, button, select, textarea {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background-color: #0056b3; }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .log-area {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #0066cc; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🔌 Socket.IO WebSocket 测试工具</h1>
    
    <!-- 连接控制面板 -->
    <div class="container">
        <h2>连接设置</h2>
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:9092" style="width: 300px;">
        </div>
        <div class="form-group">
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="test-user-001" style="width: 200px;">
        </div>
        <div>
            <button id="connectBtn" onclick="connectSocket()">连接</button>
            <button id="disconnectBtn" onclick="disconnectSocket()" disabled>断开连接</button>
        </div>
        <div id="connectionStatus" class="status disconnected">未连接</div>
    </div>

    <div class="grid">
        <!-- 串口测试面板 -->
        <div class="container">
            <h2>串口功能测试</h2>
            
            <h3>连接串口</h3>
            <div class="form-group">
                <label for="portName">串口名称:</label>
                <input type="text" id="portName" value="COM3" placeholder="例如: COM3, /dev/ttyUSB0">
            </div>
            <div class="form-group">
                <label for="baudRate">波特率:</label>
                <select id="baudRate">
                    <option value="9600" selected>9600</option>
                    <option value="19200">19200</option>
                    <option value="38400">38400</option>
                    <option value="57600">57600</option>
                    <option value="115200">115200</option>
                </select>
            </div>
            <button onclick="connectPort()" id="connectPortBtn" disabled>连接串口</button>
            
            <h3>发送命令</h3>
            <div class="form-group">
                <label for="serialCommand">串口命令:</label>
                <input type="text" id="serialCommand" value="AT" placeholder="输入要发送的命令">
            </div>
            <button onclick="sendCommand()" id="sendCommandBtn" disabled>发送命令</button>
            
            <h3>预设命令</h3>
            <button onclick="sendPresetCommand('AT')" disabled class="preset-cmd">AT</button>
            <button onclick="sendPresetCommand('AT+VERSION')" disabled class="preset-cmd">AT+VERSION</button>
            <button onclick="sendPresetCommand('HELLO')" disabled class="preset-cmd">HELLO</button>
        </div>

        <!-- 消息监控面板 -->
        <div class="container">
            <h2>消息监控</h2>
            <div>
                <button onclick="clearLog()">清空日志</button>
                <button onclick="exportLog()">导出日志</button>
            </div>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <!-- 原始事件测试 -->
    <div class="container">
        <h2>原始事件测试</h2>
        <div class="form-group">
            <label for="eventName">事件名称:</label>
            <input type="text" id="eventName" value="test" placeholder="输入事件名称">
        </div>
        <div class="form-group">
            <label for="eventData">事件数据 (JSON):</label>
            <textarea id="eventData" rows="3" style="width: 100%;">{"message": "test data"}</textarea>
        </div>
        <button onclick="sendCustomEvent()" id="sendEventBtn" disabled>发送自定义事件</button>
    </div>

    <script>
        let socket = null;
        let isConnected = false;

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateConnectionStatus(status, message) {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
        }

        function updateButtonStates() {
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const connectPortBtn = document.getElementById('connectPortBtn');
            const sendCommandBtn = document.getElementById('sendCommandBtn');
            const sendEventBtn = document.getElementById('sendEventBtn');
            const presetCmds = document.querySelectorAll('.preset-cmd');

            connectBtn.disabled = isConnected;
            disconnectBtn.disabled = !isConnected;
            connectPortBtn.disabled = !isConnected;
            sendCommandBtn.disabled = !isConnected;
            sendEventBtn.disabled = !isConnected;
            
            presetCmds.forEach(btn => {
                btn.disabled = !isConnected;
            });
        }

        function connectSocket() {
            const serverUrl = document.getElementById('serverUrl').value;
            const userId = document.getElementById('userId').value;

            if (!serverUrl || !userId) {
                alert('请填写服务器地址和用户ID');
                return;
            }

            updateConnectionStatus('connecting', '正在连接...');
            log(`尝试连接到: ${serverUrl}?userId=${userId}`, 'info');

            socket = io(serverUrl, {
                query: { userId: userId },
                transports: ['websocket', 'polling']
            });

            // 连接成功
            socket.on('connect', () => {
                isConnected = true;
                updateConnectionStatus('connected', `已连接 (ID: ${socket.id})`);
                log(`✅ 连接成功! Socket ID: ${socket.id}`, 'success');
                updateButtonStates();
            });

            // 连接失败
            socket.on('connect_error', (error) => {
                isConnected = false;
                updateConnectionStatus('disconnected', '连接失败');
                log(`❌ 连接失败: ${error.message}`, 'error');
                updateButtonStates();
            });

            // 断开连接
            socket.on('disconnect', (reason) => {
                isConnected = false;
                updateConnectionStatus('disconnected', '连接已断开');
                log(`🔌 连接断开: ${reason}`, 'warning');
                updateButtonStates();
            });

            // 监听串口数据
            socket.on('serialData', (data) => {
                log(`📡 收到串口数据: ${JSON.stringify(data)}`, 'success');
            });

            // 监听串口状态
            socket.on('portStatus', (status) => {
                log(`🔌 串口状态: ${JSON.stringify(status)}`, 'info');
            });

            // 监听命令状态
            socket.on('portCmdStatus', (status) => {
                log(`📤 命令状态: ${JSON.stringify(status)}`, 'info');
            });

            // 监听所有事件
            socket.onAny((eventName, ...args) => {
                if (!['connect', 'disconnect', 'serialData', 'portStatus', 'portCmdStatus'].includes(eventName)) {
                    log(`🎯 收到事件 [${eventName}]: ${JSON.stringify(args)}`, 'info');
                }
            });
        }

        function disconnectSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
        }

        function connectPort() {
            if (!socket || !isConnected) {
                alert('请先连接Socket.IO服务器');
                return;
            }

            const portName = document.getElementById('portName').value;
            const baudRate = parseInt(document.getElementById('baudRate').value);

            const portConfig = {
                portDescriptor: portName,
                baudRate: baudRate,
                dataBits: 8,
                stopBits: 1,
                parity: 0
            };

            log(`🔌 尝试连接串口: ${JSON.stringify(portConfig)}`, 'info');
            socket.emit('connectPort', portConfig);
        }

        function sendCommand() {
            if (!socket || !isConnected) {
                alert('请先连接Socket.IO服务器');
                return;
            }

            const command = document.getElementById('serialCommand').value;
            const portName = document.getElementById('portName').value;

            if (!command) {
                alert('请输入要发送的命令');
                return;
            }

            const commandData = {
                port: portName,
                data: command
            };

            log(`📤 发送命令: ${JSON.stringify(commandData)}`, 'info');
            socket.emit('sendPortCmd', commandData);
        }

        function sendPresetCommand(cmd) {
            document.getElementById('serialCommand').value = cmd;
            sendCommand();
        }

        function sendCustomEvent() {
            if (!socket || !isConnected) {
                alert('请先连接Socket.IO服务器');
                return;
            }

            const eventName = document.getElementById('eventName').value;
            const eventDataStr = document.getElementById('eventData').value;

            if (!eventName) {
                alert('请输入事件名称');
                return;
            }

            try {
                const eventData = JSON.parse(eventDataStr);
                log(`🎯 发送自定义事件 [${eventName}]: ${JSON.stringify(eventData)}`, 'info');
                socket.emit(eventName, eventData);
            } catch (e) {
                alert('事件数据格式错误，请输入有效的JSON');
            }
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        function exportLog() {
            const logArea = document.getElementById('logArea');
            const logText = logArea.innerText;
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `websocket-test-log-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 初始化
        updateButtonStates();
        log('🚀 WebSocket测试工具已就绪', 'info');
    </script>
</body>
</html>
