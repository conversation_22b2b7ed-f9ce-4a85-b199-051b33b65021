@echo off
chcp 65001 > nul
echo ========================================
echo 构建通用版本（支持外部配置）
echo ========================================
echo.
echo 当前配置：
echo - 构建类型: 通用版本
echo - 输出文件: comReader.jar
echo - 配置方式: 外部配置文件
echo.

echo 开始构建...
mvn clean package -DskipTests

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 构建成功！
    echo ========================================
    echo 输出文件: target/comReader.jar
    echo.
    echo 部署说明:
    echo 1. 肺功能工作站: 运行 deploy\deploy-fgy200-workstation.bat
    echo 2. 电测听工作站: 运行 deploy\deploy-audiometry-workstation.bat
    echo.
    echo 手动启动命令:
    echo - 肺功能: java -Dspring.profiles.active=fgy200 -jar comReader.jar
    echo - 电测听: java -Dspring.profiles.active=audiometry -jar comReader.jar
    echo - 混合环境: java -Dspring.profiles.active=production -jar comReader.jar
    echo.
) else (
    echo.
    echo ========================================
    echo 构建失败！
    echo ========================================
    echo 请检查错误信息并重试
    echo.
)

pause
