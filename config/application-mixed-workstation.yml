# 混合设备工作站外部配置文件
# 使用方法：将此文件放在 JAR 包同目录下，重命名为 application.yml
# 启动命令：java -Dspring.profiles.active=production -jar comReader.jar

# 激活生产 Profile（支持所有设备）
spring:
  profiles:
    active: production

# Socket.IO服务器配置
socketio:
  hostname: localhost
  port: 9092

# 应用配置
app:
  name: "设备通信服务"
  version: "1.0.0"
  description: "混合设备工作站版本"
  environment: "mixed-workstation"

# 工作站特定配置
workstation:
  type: "mixed"
  name: "混合设备工作站"
  location: "体检中心"
  
# 设备启用配置
device:
  enabled:
    fgy200: true
    audiometry: true
    ecg: true
    st150: true

# 日志配置
logging:
  level:
    root: INFO
    org.bj: INFO
    org.bj.device.processors: INFO
    org.bj.service: INFO
    org.bj.config: INFO
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

  file:
    name: "logs/mixed-workstation.log"
    max-size: "10MB"
    max-history: 30
    total-size-cap: "500MB"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
