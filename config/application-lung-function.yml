# 肺功能工作站专用配置

# Socket.IO配置
socketio:
  hostname: 0.0.0.0
  port: 9092

# 日志配置
logging:
  level:
    org.springframework: INFO
    org.bj: INFO
    com.corundumstudio.socketio: WARN
    com.fazecast.jSerialComm: INFO
  file:
    name: logs/lung-function.log

# 生产环境串口配置
serial:
  default:
    baudRate: 115200
    dataBits: 8
    stopBits: 1
    parity: 0
    timeout: 5000

# FGY200肺功能设备配置
fgy200:
  # 程序安装路径（根据实际安装路径调整）
  programPath: "D:"
  patientFile: "Tester.ini"
  databaseFile: "fei.mdb"
  fileEncoding: "GBK"
  hisCode: "肺功能检测"
  
  database:
    enabled: true
    path: "D:/fei.mdb"
    driver: "net.ucanaccess.jdbc.UcanaccessDriver"
    pool:
      maximumPoolSize: 5
      minimumIdle: 1
      connectionTimeout: 30000
      idleTimeout: 600000
  
  deviceConnection:
    connectionTimeout: 30
    retryCount: 3
    autoReconnect: true
  
  sqlConfig:
    tableName: "存储数据表"
    selectLatestSql: "SELECT TOP 1 * FROM 存储数据表 ORDER BY ID_KEY DESC"
    selectByPatientSql: "SELECT TOP 1 * FROM 存储数据表 WHERE ID = #{patientId} AND 日期 >= #{startDate} ORDER BY ID_KEY DESC"
    selectByPatientAndDateRangeSql: "SELECT TOP 1 * FROM 存储数据表 WHERE ID = #{patientId} AND 日期 BETWEEN #{startDate} AND #{endDate} ORDER BY ID_KEY DESC"
    selectByDateRangeSql: "SELECT * FROM 存储数据表 WHERE 日期 BETWEEN #{startDate} AND #{endDate} ORDER BY ID_KEY DESC"
    countRecordsSql: "SELECT COUNT(*) FROM 存储数据表"
    countByPatientSql: "SELECT COUNT(*) FROM 存储数据表 WHERE ID = #{patientId}"
    countByDateRangeSql: "SELECT COUNT(*) FROM 存储数据表 WHERE 日期 BETWEEN #{startDate} AND #{endDate}"
    queryParams:
      dateFormat: "yyyy-MM-dd"
      timeFormat: "HH:mm:ss"
  
  monitoring:
    interval: 10
    timeout: 300
  
  report:
    imageDirectory: "D:/lung_function_reports"
    imageFormat: "jpg"
    imageQuality: 0.9
    imageWidth: 800
    imageHeight: 600
    fileNamePattern: "{examNo}.{序号}"
    processInterval: 30
    maxRetryCount: 5
    taskTimeoutHours: 2
  
  doctorConfig:
    checkDoctor:
      code: "AUTO_SYSTEM"
      name: "系统自动"
      signPic: ""
    reportDoctor:
      code: "AUTO_SYSTEM"
      name: "系统自动"
      signPic: ""
    auditDoctor:
      code: "AUTO_SYSTEM"
      name: "系统自动"
      signPic: ""

# 注意：此配置文件中不包含任何电测听相关配置
# AudiometryDeviceProcessor相关的Bean会因为Profile="!lung-function"而被自动排除
