# 外部配置文件 - 生产环境

# Socket.IO配置
socketio:
  hostname: localhost
  port: 9092

# 日志配置
logging:
  level:
    org.springframework: INFO
    org.bj: DEBUG
    com.corundumstudio.socketio: INFO
    com.fazecast.jSerialComm: DEBUG
  file:
    name: logs/application.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 串口配置（可根据实际情况修改）
serial:
  default:
    baudRate: 9600
    dataBits: 8
    stopBits: 1
    parity: 0
    timeout: 2000

# 应用特定配置
app:
  name: "串口数据读取器"
  version: "1.0.0"
  description: "GraalVM原生镜像版本"
