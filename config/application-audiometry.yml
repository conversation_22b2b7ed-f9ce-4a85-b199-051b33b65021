# 电测听工作站专用配置

# Socket.IO配置
socketio:
  hostname: 0.0.0.0
  port: 9092

# 日志配置
logging:
  level:
    org.springframework: INFO
    org.bj: INFO
    com.corundumstudio.socketio: WARN
    com.fazecast.jSerialComm: INFO
  file:
    name: logs/audiometry.log

# 生产环境串口配置
serial:
  default:
    baudRate: 115200
    dataBits: 8
    stopBits: 1
    parity: 0
    timeout: 5000

# 电测听设备配置
audiometry:
  # 程序安装路径（根据实际安装路径调整）
  programPath: "C:/Program Files/Interacoustics/OtoAccess"
  softwareExecutable: "OtoAccess.exe"
  hisCode: "电测听"
  
  deviceConnection:
    serverHost: "localhost"
    serverPort: 8080
    hookPort: 9092
    connectionTimeout: 30
    retryCount: 3
    autoReconnect: true
    apiKey: "your-api-key-here"
  
  monitoring:
    interval: 30
    timeout: 300
    maxErrorCount: 5
  
  report:
    imageDirectory: "C:/AudiometryReports"
    imageFormat: "png"
    imageQuality: 0.9
    fileNamePattern: "{examNo}.{序号}"
    processInterval: 30
    maxRetryCount: 5
    taskTimeoutHours: 2
  
  dataStorage:
    storageType: "db"
    validationEnabled: true
  
  softwareManagement:
    autoCloseAfterMeasurement: true
    autoStartBeforeMeasurement: true
    closeBeforeStart: true
    closeWaitTimeout: 10
    startWaitTimeout: 15
    runAsAdmin: true
    autoDetectAdminRequired: true
    restartStrategy:
      enabled: true
      waitBeforeRestart: 2
      waitAfterRestart: 3
      maxRestartAttempts: 3

# 注意：此配置文件中不包含任何FGY200相关配置
# FGY200相关的Bean会因为Profile="!audiometry"而被自动排除
