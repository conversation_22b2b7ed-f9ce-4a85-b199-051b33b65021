# 肺功能工作站外部配置文件
# 使用方法：将此文件放在 JAR 包同目录下，重命名为 application.yml
# 启动命令：java -Dspring.profiles.active=fgy200 -jar comReader.jar

# 激活肺功能 Profile
spring:
  profiles:
    active: fgy200

# Socket.IO服务器配置
socketio:
  hostname: localhost
  port: 9092

# 应用配置
app:
  name: "设备通信服务"
  version: "1.0.0"
  description: "肺功能工作站专用版本"
  environment: "fgy200-workstation"

# 工作站特定配置
workstation:
  type: "fgy200"
  name: "肺功能工作站"
  location: "体检中心"
  
# 设备启用配置
device:
  enabled:
    fgy200: true
    audiometry: false
    ecg: false
    st150: true

# 日志配置
logging:
  level:
    root: INFO
    org.bj: INFO
    org.bj.device.processors.FGY200DeviceProcessor: DEBUG
    org.bj.service.FGY200ReportImageService: DEBUG
    org.bj.config.FGY200ScheduledTaskConfig: DEBUG
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

  file:
    name: "logs/fgy200-workstation.log"
    max-size: "10MB"
    max-history: 30
    total-size-cap: "500MB"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
