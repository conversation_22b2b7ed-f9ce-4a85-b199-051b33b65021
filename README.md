# Socket.IO串口通信服务

基于Socket.IO的高性能串口通信服务，专为体检设备数据采集而设计。

## 🚀 快速开始

### 环境要求

- Java 21+
- Maven 3.6+

### 构建和运行

```bash
# 构建项目
mvn clean package

# 运行服务
java -jar target/checkupMain.jar
```

## 📡 连接配置

### Socket.IO连接地址
```
http://localhost:9092
```

### 前端连接示例

```typescript
import { io } from 'socket.io-client';

const socket = io('http://localhost:9092', {
  query: { userId: 'your-user-id' }
});

// 监听连接
socket.on('connect', () => {
  console.log('连接成功');
});

// 监听串口数据
socket.on('serialData', (data) => {
  console.log('收到设备数据:', data);
});
```

## 🔧 设备操作

### 连接串口设备

```javascript
socket.emit('connectPort', {
  portDescriptor: 'COM3',
  baudRate: 9600,
  dataBits: 8,
  stopBits: 1,
  parity: 0
});
```

### 发送设备命令

```javascript
socket.emit('sendPortCmd', {
  port: 'COM3',
  data: 'AT+VERSION'
});
```

### 监听设备状态

```javascript
// 设备连接状态
socket.on('portStatus', (status) => {
  console.log('设备状态:', status);
});

// 命令执行状态
socket.on('portCmdStatus', (status) => {
  console.log('命令状态:', status);
});
```

## ⚙️ 配置

### application.yml

```yaml
# Socket.IO服务器配置
socketio:
  hostname: localhost
  port: 9092

# 串口配置
serial:
  default:
    baudRate: 9600
    dataBits: 8
    stopBits: 1
    parity: 0
    timeout: 2000

# 日志配置
logging:
  level:
    org.bj: DEBUG
    com.corundumstudio.socketio: INFO
    com.fazecast.jSerialComm: DEBUG
```

## 🏗️ 项目结构

```
src/main/java/org/bj/
├── ComDataReader.java          # 主启动类
├── config/
│   ├── SerialPortConfig.java   # 串口配置
│   └── SocketIOServerConfig.java # Socket.IO配置
├── service/
│   └── SocketIOService.java    # Socket.IO服务
├── entity/                     # 实体类
├── builder/                    # 串口工厂类
└── resources/
    ├── application.yml         # 应用配置
    └── WebSocket.html         # 测试页面
```

## 🔍 测试

### 使用内置测试页面

1. 启动服务
2. 浏览器打开 `src/main/resources/WebSocket.html`
3. 输入连接地址: `ws://localhost:9092`
4. 测试连接和数据传输

### 使用浏览器控制台

```javascript
// 连接测试
const socket = io('http://localhost:9092', {
  query: { userId: 'test-user' }
});

socket.on('connect', () => {
  console.log('✅ 连接成功');
});
```

## 📦 部署

### 开发环境

```bash
java -jar checkupMain.jar
```

### 生产环境

```bash
# 使用外部配置
java -jar checkupMain.jar --spring.config.location=config/application.yml
```

### Docker部署

```dockerfile
FROM openjdk:21-jre-slim
COPY target/checkupMain.jar app.jar
EXPOSE 9092
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -an | findstr 9092
   ```

2. **串口权限问题**
   - 确认应用有串口访问权限
   - 检查设备驱动是否正确安装

3. **连接失败**
   - 检查防火墙设置
   - 确认服务正常启动

### 日志查看

```bash
# 查看应用日志
tail -f logs/application.log
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
