# PowerShell script to test audiometry software launch methods
# Run this script to test different launch methods

Write-Host "========================================" -ForegroundColor Green
Write-Host "Audiometry Software Launch Test" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$softwarePath = "C:\Program Files (x86)\Interacoustics\Diagnostic Suite\DiagnosticSuiteStandalone.exe"
$processName = "DiagnosticSuiteStandalone"

Write-Host "Software Path: $softwarePath" -ForegroundColor Yellow
Write-Host "Process Name: $processName" -ForegroundColor Yellow
Write-Host ""

# Check if file exists
Write-Host "1. Checking if software file exists..." -ForegroundColor Cyan
if (Test-Path $softwarePath) {
    Write-Host "[OK] Software file exists" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Software file not found" -ForegroundColor Red
    Write-Host "Please check the installation path" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Check if process is already running
Write-Host "2. Checking if process is already running..." -ForegroundColor Cyan
$existingProcess = Get-Process -Name $processName -ErrorAction SilentlyContinue
if ($existingProcess) {
    Write-Host "[WARNING] Process is already running" -ForegroundColor Yellow
    $choice = Read-Host "Do you want to close it first? (Y/N)"
    if ($choice -eq "Y" -or $choice -eq "y") {
        Stop-Process -Name $processName -Force -ErrorAction SilentlyContinue
        Write-Host "Existing process closed" -ForegroundColor Green
        Start-Sleep -Seconds 2
    }
} else {
    Write-Host "[OK] No running process detected" -ForegroundColor Green
}
Write-Host ""

# Test Method 1: Direct Start-Process
Write-Host "3. Testing Method 1: Direct Start-Process..." -ForegroundColor Cyan
try {
    $process1 = Start-Process -FilePath $softwarePath -PassThru
    Write-Host "[INFO] Direct Start-Process executed" -ForegroundColor Blue
    Start-Sleep -Seconds 3
    
    # Check if process is running and has window
    $runningProcess1 = Get-Process -Name $processName -ErrorAction SilentlyContinue
    if ($runningProcess1) {
        Write-Host "[SUCCESS] Process started with Direct method" -ForegroundColor Green
        if ($runningProcess1.MainWindowTitle) {
            Write-Host "[SUCCESS] Window detected: $($runningProcess1.MainWindowTitle)" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Process running but no window detected" -ForegroundColor Yellow
        }
    } else {
        Write-Host "[ERROR] Process not found after Direct start" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Direct Start-Process failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Clean up before next test
Stop-Process -Name $processName -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# Test Method 2: CMD START simulation
Write-Host "4. Testing Method 2: CMD START simulation..." -ForegroundColor Cyan
try {
    $cmdCommand = "cmd /c start `"`" `"$softwarePath`""
    Invoke-Expression $cmdCommand
    Write-Host "[INFO] CMD START simulation executed" -ForegroundColor Blue
    Start-Sleep -Seconds 3
    
    # Check if process is running and has window
    $runningProcess2 = Get-Process -Name $processName -ErrorAction SilentlyContinue
    if ($runningProcess2) {
        Write-Host "[SUCCESS] Process started with CMD START method" -ForegroundColor Green
        if ($runningProcess2.MainWindowTitle) {
            Write-Host "[SUCCESS] Window detected: $($runningProcess2.MainWindowTitle)" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Process running but no window detected" -ForegroundColor Yellow
        }
    } else {
        Write-Host "[ERROR] Process not found after CMD START" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] CMD START simulation failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Clean up before next test
Stop-Process -Name $processName -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# Test Method 3: Explorer launch
Write-Host "5. Testing Method 3: Explorer launch..." -ForegroundColor Cyan
try {
    Start-Process -FilePath "explorer.exe" -ArgumentList $softwarePath
    Write-Host "[INFO] Explorer launch executed" -ForegroundColor Blue
    Start-Sleep -Seconds 3
    
    # Check if process is running and has window
    $runningProcess3 = Get-Process -Name $processName -ErrorAction SilentlyContinue
    if ($runningProcess3) {
        Write-Host "[SUCCESS] Process started with Explorer method" -ForegroundColor Green
        if ($runningProcess3.MainWindowTitle) {
            Write-Host "[SUCCESS] Window detected: $($runningProcess3.MainWindowTitle)" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Process running but no window detected" -ForegroundColor Yellow
        }
    } else {
        Write-Host "[ERROR] Process not found after Explorer launch" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Explorer launch failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Final process check
Write-Host "6. Final process status check..." -ForegroundColor Cyan
$finalProcesses = Get-Process -Name $processName -ErrorAction SilentlyContinue
if ($finalProcesses) {
    Write-Host "Found $($finalProcesses.Count) running process(es):" -ForegroundColor Yellow
    foreach ($proc in $finalProcesses) {
        Write-Host "  PID: $($proc.Id), Window: $($proc.MainWindowTitle)" -ForegroundColor Yellow
    }
} else {
    Write-Host "No processes found" -ForegroundColor Yellow
}
Write-Host ""

# Cleanup option
Write-Host "7. Cleanup..." -ForegroundColor Cyan
$cleanup = Read-Host "Do you want to close all test processes? (Y/N)"
if ($cleanup -eq "Y" -or $cleanup -eq "y") {
    Stop-Process -Name $processName -Force -ErrorAction SilentlyContinue
    Write-Host "All test processes closed" -ForegroundColor Green
}
Write-Host ""

Write-Host "========================================" -ForegroundColor Green
Write-Host "Test Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Results Summary:" -ForegroundColor Yellow
Write-Host "- Check which method successfully showed the software interface" -ForegroundColor White
Write-Host "- The method that shows a window title is the best one" -ForegroundColor White
Write-Host "- If CMD START method works best, our code fix should solve the problem" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
