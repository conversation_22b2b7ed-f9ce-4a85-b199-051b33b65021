#!/bin/bash

echo "========================================"
echo "MinIO服务器端诊断脚本"
echo "========================================"
echo ""

echo "诊断时间: $(date)"
echo "服务器IP: $(hostname -I)"
echo ""

echo "1. MinIO服务状态检查:"
echo "----------------------------------------"
echo "1.1 MinIO进程状态:"
ps aux | grep minio | grep -v grep
echo ""

echo "1.2 MinIO服务状态:"
if command -v systemctl &> /dev/null; then
    systemctl status minio
else
    service minio status
fi
echo ""

echo "1.3 MinIO端口监听:"
netstat -tlnp | grep 9000
netstat -tlnp | grep 9001
echo ""

echo "2. MinIO配置检查:"
echo "----------------------------------------"
echo "2.1 MinIO配置文件:"
if [ -f /etc/default/minio ]; then
    echo "配置文件存在: /etc/default/minio"
    cat /etc/default/minio | grep -v "SECRET"
else
    echo "未找到标准配置文件"
fi
echo ""

echo "2.2 MinIO环境变量:"
env | grep MINIO | grep -v "SECRET"
echo ""

echo "3. 网络连接分析:"
echo "----------------------------------------"
echo "3.1 当前网络连接:"
netstat -an | grep 9000 | head -20
echo ""

echo "3.2 连接统计:"
echo "ESTABLISHED连接数: $(netstat -an | grep 9000 | grep ESTABLISHED | wc -l)"
echo "TIME_WAIT连接数: $(netstat -an | grep 9000 | grep TIME_WAIT | wc -l)"
echo "CLOSE_WAIT连接数: $(netstat -an | grep 9000 | grep CLOSE_WAIT | wc -l)"
echo ""

echo "3.3 客户端连接分析:"
netstat -an | grep 9000 | grep ESTABLISHED | awk '{print $5}' | cut -d: -f1 | sort | uniq -c
echo ""

echo "4. 系统资源检查:"
echo "----------------------------------------"
echo "4.1 CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
echo ""

echo "4.2 内存使用:"
free -h
echo ""

echo "4.3 磁盘使用:"
df -h | grep -E "(/$|/data|/mnt)"
echo ""

echo "4.4 网络接口状态:"
ip addr show | grep -E "(inet |UP|DOWN)"
echo ""

echo "5. TCP参数检查:"
echo "----------------------------------------"
echo "5.1 TCP连接相关参数:"
sysctl net.ipv4.tcp_keepalive_time
sysctl net.ipv4.tcp_keepalive_intvl
sysctl net.ipv4.tcp_keepalive_probes
sysctl net.ipv4.tcp_fin_timeout
sysctl net.core.somaxconn
echo ""

echo "5.2 网络缓冲区:"
sysctl net.core.rmem_max
sysctl net.core.wmem_max
sysctl net.ipv4.tcp_rmem
sysctl net.ipv4.tcp_wmem
echo ""

echo "6. MinIO日志分析:"
echo "----------------------------------------"
echo "6.1 最近的MinIO日志 (最后50行):"
if [ -f /var/log/minio/minio.log ]; then
    tail -50 /var/log/minio/minio.log
elif [ -f /var/log/minio.log ]; then
    tail -50 /var/log/minio.log
else
    echo "未找到MinIO日志文件"
    journalctl -u minio -n 50 --no-pager 2>/dev/null || echo "无法获取systemd日志"
fi
echo ""

echo "6.2 错误日志搜索:"
if [ -f /var/log/minio/minio.log ]; then
    echo "连接重置相关错误:"
    grep -i "reset\|connection\|timeout" /var/log/minio/minio.log | tail -10
elif [ -f /var/log/minio.log ]; then
    grep -i "reset\|connection\|timeout" /var/log/minio.log | tail -10
fi
echo ""

echo "7. 防火墙检查:"
echo "----------------------------------------"
echo "7.1 iptables规则:"
iptables -L -n | grep -E "(9000|9001|ACCEPT|DROP|REJECT)"
echo ""

echo "7.2 ufw状态 (如果安装):"
if command -v ufw &> /dev/null; then
    ufw status
fi
echo ""

echo "8. 系统日志检查:"
echo "----------------------------------------"
echo "8.1 最近的网络相关错误:"
dmesg | grep -i "network\|tcp\|reset" | tail -10
echo ""

echo "8.2 系统日志中的连接错误:"
grep -i "connection reset\|tcp.*reset" /var/log/syslog 2>/dev/null | tail -5 || echo "无syslog或无相关错误"
echo ""

echo "9. MinIO健康检查:"
echo "----------------------------------------"
echo "9.1 本地健康检查:"
curl -s http://localhost:9000/minio/health/live
echo ""
curl -s http://localhost:9000/minio/health/ready
echo ""

echo "9.2 MinIO管理信息:"
if command -v mc &> /dev/null; then
    echo "尝试获取MinIO信息..."
    mc admin info local 2>/dev/null || echo "无法获取MinIO管理信息"
fi
echo ""

echo "10. 网络测试:"
echo "----------------------------------------"
echo "10.1 从服务器测试客户端连通性:"
echo "请提供客户端IP进行反向测试"
echo "客户端IP应该是: $(netstat -an | grep 9000 | grep ESTABLISHED | awk '{print $5}' | cut -d: -f1 | head -1)"
CLIENT_IP=$(netstat -an | grep 9000 | grep ESTABLISHED | awk '{print $5}' | cut -d: -f1 | head -1)
if [ ! -z "$CLIENT_IP" ]; then
    echo "测试到客户端的连通性:"
    ping -c 3 $CLIENT_IP
fi
echo ""

echo "========================================"
echo "诊断完成"
echo "========================================"
echo ""
echo "关键检查点:"
echo "1. MinIO服务是否正常运行"
echo "2. 端口9000是否正确监听"
echo "3. 是否有大量TIME_WAIT连接"
echo "4. 系统资源是否充足"
echo "5. 防火墙是否阻止连接"
echo "6. TCP参数是否合理"
echo ""
echo "如果发现问题，建议:"
echo "1. 重启MinIO服务: systemctl restart minio"
echo "2. 检查防火墙规则"
echo "3. 调整TCP参数"
echo "4. 检查网络设备配置"
echo ""
