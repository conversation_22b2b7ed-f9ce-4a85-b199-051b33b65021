@echo off
echo ========================================
echo 调试其他程序的MinIO连接方式
echo ========================================
echo.

echo 1. 检查当前运行的Java进程:
wmic process where "name='java.exe'" get processid,commandline
echo.

echo 2. 检查MinIO相关的网络连接:
netstat -ano | findstr 9000
echo.

echo 3. 检查是否有其他程序在使用MinIO:
netstat -ano | findstr *************
echo.

echo 4. 检查系统中的MinIO客户端:
where minio 2>nul
where mc 2>nul
echo.

echo 5. 检查Python/Node.js等其他语言的MinIO客户端:
pip list | findstr minio 2>nul
npm list -g | findstr minio 2>nul
echo.

echo 6. 检查浏览器访问MinIO控制台:
echo 请手动测试: http://*************:9000
echo 或者: http://*************:9001
echo.

echo 7. 使用curl测试MinIO API:
curl -v http://*************:9000/ 2>&1
echo.

echo 8. 检查Windows事件日志中的网络错误:
wevtutil qe System /c:10 /rd:true /f:text | findstr /i "network\|tcp\|reset"
echo.

pause
