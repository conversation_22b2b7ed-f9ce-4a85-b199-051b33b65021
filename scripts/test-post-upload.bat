@echo off
echo ========================================
echo 测试POST方法绕过PUT限制
echo ========================================
echo.

echo 问题确认: 网络设备禁用了PUT和DELETE请求
echo 解决方案: 使用POST方法进行文件上传
echo.

set MINIO_ENDPOINT=http://192.168.11.98:9000
set BUCKET=images

echo 目标服务器: %MINIO_ENDPOINT%
echo 存储桶: %BUCKET%
echo.

echo 1. 测试GET请求（应该正常）:
echo ----------------------------------------
curl -v %MINIO_ENDPOINT%/minio/health/live
echo.

echo 2. 确认PUT请求失败:
echo ----------------------------------------
echo test > test-put.txt
echo 尝试PUT请求（预期失败）:
curl -v -X PUT -T test-put.txt %MINIO_ENDPOINT%/%BUCKET%/test-put.txt
echo.

echo 3. 测试POST请求:
echo ----------------------------------------
echo 尝试POST请求（希望成功）:
curl -v -X POST -d "test data for POST" %MINIO_ENDPOINT%/%BUCKET%/test-post.txt
echo.

echo 4. 测试POST multipart上传:
echo ----------------------------------------
echo 创建测试文件...
echo POST multipart test data > test-multipart.txt

echo 尝试multipart POST上传:
curl -v -X POST -F "file=@test-multipart.txt" %MINIO_ENDPOINT%/%BUCKET%/
echo.

echo 5. 测试不同的POST Content-Type:
echo ----------------------------------------
echo 5.1 application/octet-stream:
curl -v -X POST -H "Content-Type: application/octet-stream" -d "binary data test" %MINIO_ENDPOINT%/%BUCKET%/test-binary.dat
echo.

echo 5.2 text/plain:
curl -v -X POST -H "Content-Type: text/plain" -d "plain text test" %MINIO_ENDPOINT%/%BUCKET%/test-text.txt
echo.

echo 6. 测试MinIO特定的POST上传:
echo ----------------------------------------
echo 尝试MinIO风格的POST上传:
curl -v -X POST ^
  -H "Content-Type: multipart/form-data" ^
  -F "key=test-minio-post.txt" ^
  -F "file=@test-multipart.txt" ^
  %MINIO_ENDPOINT%/%BUCKET%/
echo.

echo 7. 清理测试文件:
echo ----------------------------------------
del test-put.txt 2>nul
del test-multipart.txt 2>nul
echo 本地测试文件已清理
echo.

echo ========================================
echo POST方法测试完成
echo ========================================
echo.
echo 分析结果:
echo 1. 如果GET成功，PUT失败 → 确认网络设备限制
echo 2. 如果POST成功 → 可以使用POST绕过限制
echo 3. 如果POST也失败 → 需要检查其他HTTP方法
echo.
echo 下一步:
echo 1. 如果POST成功，修改Java客户端使用POST方法
echo 2. 如果POST失败，考虑使用代理或其他解决方案
echo 3. 联系网络管理员了解具体的限制策略
echo.
pause
