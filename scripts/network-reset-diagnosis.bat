@echo off
echo ========================================
echo 网络连接重置问题专项诊断
echo ========================================
echo.
echo 问题现象: mc.exe, curl, Java客户端都出现Connection reset
echo 这表明问题在网络基础设施层面
echo.

set TARGET_IP=*************
set TARGET_PORT=9000

echo 目标服务器: %TARGET_IP%:%TARGET_PORT%
echo 诊断时间: %date% %time%
echo.

echo 1. 网络基础连通性验证:
echo ----------------------------------------
echo 1.1 ICMP连通性:
ping -n 5 %TARGET_IP%
echo.

echo 1.2 TCP端口连通性:
powershell -Command "Test-NetConnection -ComputerName %TARGET_IP% -Port %TARGET_PORT%"
echo.

echo 1.3 路由跟踪:
tracert %TARGET_IP%
echo.

echo 2. TCP连接行为分析:
echo ----------------------------------------
echo 2.1 连续TCP连接测试:
for /L %%i in (1,1,5) do (
    echo 第%%i次TCP连接测试:
    powershell -Command "$tcp = New-Object System.Net.Sockets.TcpClient; try { $tcp.Connect('%TARGET_IP%', %TARGET_PORT%); Write-Host '  ✅ 连接成功'; Start-Sleep -Seconds 1; $tcp.Close(); Write-Host '  连接已关闭' } catch { Write-Host '  ❌ 连接失败:' $_.Exception.Message }"
    timeout /t 2 /nobreak >nul
)
echo.

echo 2.2 长连接保持测试:
echo 测试连接保持30秒...
powershell -Command "$tcp = New-Object System.Net.Sockets.TcpClient; try { $tcp.Connect('%TARGET_IP%', %TARGET_PORT%); Write-Host '连接建立，保持30秒...'; Start-Sleep -Seconds 30; Write-Host '30秒后关闭连接'; $tcp.Close() } catch { Write-Host '连接失败或中断:' $_.Exception.Message }"
echo.

echo 3. HTTP协议层测试:
echo ----------------------------------------
echo 3.1 HTTP HEAD请求:
curl -I --connect-timeout 10 --max-time 30 http://%TARGET_IP%:%TARGET_PORT%/ 2>&1
echo.

echo 3.2 HTTP GET请求:
curl -v --connect-timeout 10 --max-time 30 http://%TARGET_IP%:%TARGET_PORT%/minio/health/live 2>&1
echo.

echo 3.3 HTTP OPTIONS请求:
curl -v -X OPTIONS --connect-timeout 10 --max-time 30 http://%TARGET_IP%:%TARGET_PORT%/ 2>&1
echo.

echo 4. 数据传输测试:
echo ----------------------------------------
echo 4.1 创建测试文件:
echo test_data_for_upload > test-upload.txt
echo.

echo 4.2 小文件PUT测试:
curl -v -X PUT -T test-upload.txt --connect-timeout 10 --max-time 30 http://%TARGET_IP%:%TARGET_PORT%/images/test-upload.txt 2>&1
echo.

echo 4.3 创建稍大文件测试:
fsutil file createnew test-large.dat 10240 >nul 2>&1
echo 4.4 大文件PUT测试:
curl -v -X PUT -T test-large.dat --connect-timeout 10 --max-time 60 http://%TARGET_IP%:%TARGET_PORT%/images/test-large.dat 2>&1
echo.

echo 5. 网络参数检查:
echo ----------------------------------------
echo 5.1 本地网络配置:
ipconfig | findstr /C:"IPv4" /C:"子网掩码" /C:"默认网关"
echo.

echo 5.2 TCP参数:
netsh int tcp show global
echo.

echo 5.3 当前网络连接:
netstat -an | findstr %TARGET_IP%
echo.

echo 6. 系统资源检查:
echo ----------------------------------------
echo 6.1 网络适配器状态:
wmic path win32_networkadapter where netenabled=true get name,speed
echo.

echo 6.2 网络使用情况:
wmic path Win32_PerfRawData_Tcpip_NetworkInterface get Name,BytesReceivedPerSec,BytesSentPerSec
echo.

echo 7. 防火墙和安全检查:
echo ----------------------------------------
echo 7.1 Windows防火墙状态:
netsh advfirewall show allprofiles state
echo.

echo 7.2 防火墙规则检查:
netsh advfirewall firewall show rule name=all | findstr /C:"9000" /C:"MinIO" /C:"Java"
echo.

echo 8. 时间相关测试:
echo ----------------------------------------
echo 8.1 连接时间测量:
for /L %%i in (1,1,3) do (
    echo 第%%i次连接时间测试:
    powershell -Command "$start = Get-Date; try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('%TARGET_IP%', %TARGET_PORT%); $end = Get-Date; $duration = ($end - $start).TotalMilliseconds; Write-Host '  连接时间:' $duration 'ms'; $tcp.Close() } catch { Write-Host '  连接失败' }"
)
echo.

echo 9. 网络事件日志:
echo ----------------------------------------
echo 9.1 最近的网络错误:
wevtutil qe System /c:10 /rd:true /f:text /q:"*[System[Provider[@Name='Tcpip'] and (EventID=4226 or EventID=4231 or EventID=4232)]]" 2>nul
echo.

echo 9.2 最近的应用程序网络错误:
wevtutil qe Application /c:5 /rd:true /f:text /q:"*[System[TimeCreated[timediff(@SystemTime) <= 3600000]] and EventData[Data[contains(.,'network') or contains(.,'connection') or contains(.,'reset')]]]" 2>nul
echo.

echo 10. 清理测试文件:
echo ----------------------------------------
del test-upload.txt 2>nul
del test-large.dat 2>nul
echo 测试文件已清理
echo.

echo ========================================
echo 诊断完成
echo ========================================
echo.
echo 分析要点:
echo 1. 如果ping正常但TCP连接失败 → 端口被阻止
echo 2. 如果TCP连接成功但HTTP失败 → 应用层问题
echo 3. 如果连接建立后立即断开 → 中间设备干预
echo 4. 如果只有PUT请求失败 → 数据传输被阻止
echo 5. 如果有防火墙日志 → 安全策略阻止
echo.
echo 建议下一步:
echo 1. 联系网络管理员检查交换机/路由器配置
echo 2. 检查MinIO服务器端日志和配置
echo 3. 尝试从其他客户端机器测试
echo 4. 检查是否有网络安全设备在中间
echo.
pause
