@echo off
echo ========================================
echo 启动电测听设备通信服务
echo ========================================
echo.

REM 设置JVM参数
set JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC

REM 激活电测听Profile
set SPRING_PROFILES_ACTIVE=audiometry

REM 设置日志配置
set LOGGING_CONFIG=-Dlogging.config=classpath:logback-spring.xml

echo 启动参数:
echo   Profile: %SPRING_PROFILES_ACTIVE%
echo   JVM参数: %JAVA_OPTS%
echo.

REM 启动应用
echo 正在启动电测听设备通信服务...
java %JAVA_OPTS% %LOGGING_CONFIG% -Dspring.profiles.active=%SPRING_PROFILES_ACTIVE% -jar target/comReader.jar

echo.
echo 服务已停止
pause
