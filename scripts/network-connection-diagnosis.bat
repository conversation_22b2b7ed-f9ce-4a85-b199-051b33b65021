@echo off
echo ========================================
echo 网络连接深度诊断
echo ========================================
echo.

set TARGET_IP=*************
set TARGET_PORT=9000

echo 目标服务器: %TARGET_IP%:%TARGET_PORT%
echo 本地IP信息:
ipconfig | findstr "IPv4"
echo.

echo 1. 基本网络连通性测试:
echo ----------------------------------------
ping -n 3 %TARGET_IP%
echo.

echo 2. 路由跟踪:
echo ----------------------------------------
tracert %TARGET_IP%
echo.

echo 3. 端口连通性测试:
echo ----------------------------------------
echo 测试端口 %TARGET_PORT%...
powershell -Command "Test-NetConnection -ComputerName %TARGET_IP% -Port %TARGET_PORT% -InformationLevel Detailed"
echo.

echo 4. TCP连接状态检查:
echo ----------------------------------------
echo 当前到目标服务器的连接:
netstat -an | findstr %TARGET_IP%
echo.

echo 5. 网络接口状态:
echo ----------------------------------------
ipconfig /all | findstr /C:"以太网适配器" /C:"无线局域网适配器" /C:"IPv4 地址" /C:"子网掩码" /C:"默认网关"
echo.

echo 6. ARP表检查:
echo ----------------------------------------
arp -a | findstr %TARGET_IP%
echo.

echo 7. DNS解析测试:
echo ----------------------------------------
nslookup %TARGET_IP%
echo.

echo 8. 防火墙状态检查:
echo ----------------------------------------
netsh advfirewall show allprofiles state
echo.

echo 9. 网络适配器详细信息:
echo ----------------------------------------
wmic path win32_networkadapter get name,netenabled,speed | findstr True
echo.

echo 10. TCP参数检查:
echo ----------------------------------------
netsh int tcp show global
echo.

echo 11. 持续连接测试 (30秒):
echo ----------------------------------------
echo 开始持续ping测试，观察是否有丢包...
ping -t %TARGET_IP% -l 32 &
set PING_PID=%!
timeout /t 30 /nobreak
taskkill /f /pid %PING_PID% 2>nul
echo.

echo 12. 不同大小数据包测试:
echo ----------------------------------------
echo 测试不同大小的数据包:
ping -n 1 -l 32 %TARGET_IP%
ping -n 1 -l 1024 %TARGET_IP%
ping -n 1 -l 1472 %TARGET_IP%
echo.

echo 13. HTTP连接测试:
echo ----------------------------------------
echo 测试HTTP连接...
curl -v -m 10 http://%TARGET_IP%:%TARGET_PORT%/ 2>&1
echo.

echo 14. 网络质量测试:
echo ----------------------------------------
echo 测试网络延迟和抖动...
for /L %%i in (1,1,10) do (
    ping -n 1 %TARGET_IP% | findstr "时间"
)
echo.

echo 15. 系统网络事件日志:
echo ----------------------------------------
echo 检查最近的网络相关错误...
wevtutil qe System /c:5 /rd:true /f:text /q:"*[System[Provider[@Name='Tcpip'] and TimeCreated[timediff(@SystemTime) <= 3600000]]]" 2>nul
echo.

echo 16. 网络连接重置测试:
echo ----------------------------------------
echo 测试连接建立和断开...
for /L %%i in (1,1,5) do (
    echo 第%%i次连接测试:
    powershell -Command "$tcp = New-Object System.Net.Sockets.TcpClient; try { $tcp.Connect('%TARGET_IP%', %TARGET_PORT%); Write-Host '连接成功'; $tcp.Close() } catch { Write-Host '连接失败:' $_.Exception.Message }"
    timeout /t 2 /nobreak >nul
)
echo.

echo ========================================
echo 网络诊断完成
echo ========================================
echo.
echo 分析建议:
echo 1. 如果ping正常但TCP连接失败 → 端口或服务问题
echo 2. 如果有丢包 → 网络质量问题
echo 3. 如果延迟不稳定 → 网络拥塞或设备问题
echo 4. 如果防火墙阻止 → 安全策略问题
echo.
pause
