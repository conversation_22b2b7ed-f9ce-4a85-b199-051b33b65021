@echo off
echo ========================================
echo 电测听软件启动问题诊断脚本
echo ========================================
echo.

set SOFTWARE_PATH="C:\Program Files (x86)\Interacoustics\Diagnostic Suite\DiagnosticSuiteStandalone.exe"
set PROCESS_NAME=DiagnosticSuiteStandalone

echo 1. 检查软件文件是否存在...
if exist %SOFTWARE_PATH% (
    echo [OK] 软件文件存在: %SOFTWARE_PATH%
) else (
    echo [ERROR] 软件文件不存在: %SOFTWARE_PATH%
    echo 请检查软件安装路径是否正确
    pause
    exit /b 1
)
echo.

echo 2. 检查当前用户信息...
echo 当前用户: %USERNAME%
echo 用户域: %USERDOMAIN%
echo 用户配置文件: %USERPROFILE%
echo.

echo 3. 检查系统环境...
echo 操作系统: %OS%
echo 处理器架构: %PROCESSOR_ARCHITECTURE%
echo 计算机名: %COMPUTERNAME%
echo.

echo 4. 检查进程状态...
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table
if %ERRORLEVEL% EQU 0 (
    echo [INFO] 进程查询完成
) else (
    echo [WARNING] 进程查询失败
)
echo.

echo 5. 检查进程详细信息（如果存在）...
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo csv /v
echo.

echo 6. 检查当前会话信息...
query session
echo.

echo 7. 检查显示器配置...
wmic desktopmonitor get screenheight,screenwidth /format:csv
echo.

echo 8. 测试不同启动方式...
echo.

echo 8.1 测试直接启动...
echo 启动命令: %SOFTWARE_PATH%
start "测试直接启动" /wait /min %SOFTWARE_PATH%
timeout /t 3 /nobreak >nul
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table
echo.

echo 8.2 测试explorer启动...
echo 启动命令: explorer %SOFTWARE_PATH%
explorer %SOFTWARE_PATH%
timeout /t 3 /nobreak >nul
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table
echo.

echo 8.3 测试cmd start启动...
echo 启动命令: start "" %SOFTWARE_PATH%
start "" %SOFTWARE_PATH%
timeout /t 3 /nobreak >nul
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table
echo.

echo 9. 检查启动后的窗口信息...
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo csv /v
echo.

echo 10. 清理测试进程...
echo 正在关闭测试启动的进程...
taskkill /f /im %PROCESS_NAME%.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] 测试进程已关闭
) else (
    echo [INFO] 没有需要关闭的测试进程
)
echo.

echo ========================================
echo 诊断完成！
echo ========================================
echo.
echo 请检查以上输出信息，特别注意：
echo 1. 软件文件是否存在且可访问
echo 2. 进程是否能够启动
echo 3. 进程启动后是否有窗口标题（不是N/A）
echo 4. 不同启动方式的效果差异
echo.
echo 如果进程能启动但没有窗口标题，说明界面没有正确显示
echo 建议使用 explorer 或 cmd start 方式启动
echo.

pause
