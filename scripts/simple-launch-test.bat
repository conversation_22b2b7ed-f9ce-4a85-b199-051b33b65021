@echo off
echo Testing Audiometry Software Launch Methods
echo ==========================================

set SOFTWARE="C:\Program Files (x86)\Interacoustics\Diagnostic Suite\DiagnosticSuiteStandalone.exe"

echo Software Path: %SOFTWARE%
echo.

echo Checking if file exists...
if exist %SOFTWARE% (
    echo [OK] File exists
) else (
    echo [ERROR] File not found
    echo Please check the path and try again
    pause
    exit /b 1
)
echo.

echo Method 1: Direct launch (current method)
echo Command: %SOFTWARE%
echo Press any key to test...
pause >nul
%SOFTWARE%
echo Direct launch executed
echo.

echo Waiting 3 seconds...
timeout /t 3 >nul 2>&1
echo.

echo Method 2: CMD START launch (manual simulation)
echo Command: cmd /c start "" %SOFTWARE%
echo Press any key to test...
pause >nul
cmd /c start "" %SOFTWARE%
echo CMD START launch executed
echo.

echo Waiting 3 seconds...
timeout /t 3 >nul 2>&1
echo.

echo Method 3: Explorer launch
echo Command: explorer %SOFTWARE%
echo Press any key to test...
pause >nul
explorer %SOFTWARE%
echo Explorer launch executed
echo.

echo Waiting 3 seconds...
timeout /t 3 >nul 2>&1
echo.

echo Checking running processes...
tasklist | findstr DiagnosticSuite
echo.

echo Test completed!
echo Please check which method successfully shows the software interface.
echo.
pause
