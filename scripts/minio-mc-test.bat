@echo off
echo ========================================
echo MinIO客户端mc.exe验证测试
echo 基于curl也失败的情况，重点测试网络层面
echo ========================================
echo.

REM 首先进行基础网络测试
echo 0. 基础网络连接测试:
echo ----------------------------------------
echo 测试基本ping连通性:
ping -n 3 192.168.11.98
echo.

echo 测试端口连通性:
powershell -Command "Test-NetConnection -ComputerName 192.168.11.98 -Port 9000 -InformationLevel Detailed"
echo.

echo 测试HTTP基础连接:
curl -v --connect-timeout 10 --max-time 30 http://192.168.11.98:9000/ 2>&1
echo.

echo 测试简单HTTP GET:
curl -v --connect-timeout 5 --max-time 10 -X GET http://192.168.11.98:9000/minio/health/live 2>&1
echo.

REM 检查mc.exe是否存在
where mc.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ mc.exe未找到，请先下载安装MinIO客户端
    echo 下载地址: https://dl.min.io/client/mc/release/windows-amd64/mc.exe
    echo 将mc.exe放到PATH路径中或当前目录
    pause
    exit /b 1
)

echo ✅ 找到mc.exe客户端
mc.exe --version
echo.

REM MinIO服务器配置
set MINIO_ENDPOINT=http://192.168.11.98:9000
set MINIO_ACCESS_KEY=aHCjKJhOJhOJhOJhO
set MINIO_SECRET_KEY=your-secret-key
set MINIO_ALIAS=testminio

echo 配置MinIO服务器连接...
echo 端点: %MINIO_ENDPOINT%
echo 访问密钥: %MINIO_ACCESS_KEY%
echo.

REM 配置mc客户端
echo 1. 配置mc客户端连接:
mc.exe alias set %MINIO_ALIAS% %MINIO_ENDPOINT% %MINIO_ACCESS_KEY% %MINIO_SECRET_KEY%
if %errorlevel% neq 0 (
    echo ❌ mc客户端配置失败
    pause
    exit /b 1
)
echo ✅ mc客户端配置成功
echo.

REM 测试连接
echo 2. 测试服务器连接:
mc.exe admin info %MINIO_ALIAS%
if %errorlevel% neq 0 (
    echo ❌ 服务器连接失败，尝试基本连接测试...
    mc.exe ls %MINIO_ALIAS%
    if %errorlevel% neq 0 (
        echo ❌ 基本连接也失败
        goto :error_analysis
    )
)
echo ✅ 服务器连接成功
echo.

REM 列出存储桶
echo 3. 列出存储桶:
mc.exe ls %MINIO_ALIAS%
echo.

REM 检查images存储桶
echo 4. 检查images存储桶:
mc.exe ls %MINIO_ALIAS%/images/
if %errorlevel% neq 0 (
    echo ⚠️  images存储桶不存在或无权限访问
    echo 尝试创建images存储桶...
    mc.exe mb %MINIO_ALIAS%/images
    if %errorlevel% neq 0 (
        echo ❌ 创建存储桶失败
    else
        echo ✅ 存储桶创建成功
    )
)
echo.

REM 创建测试文件
echo 5. 创建测试文件:
echo MinIO测试文件 - %date% %time% > test-mc.txt
echo 测试文件内容: MinIO测试文件
echo.

REM 网络连接详细测试
echo 6. 网络连接详细测试:
echo 检查网络路径...
tracert 192.168.11.98
echo.
echo 检查TCP连接...
telnet 192.168.11.98 9000
echo.
echo 检查持续连通性...
ping -n 5 192.168.11.98
echo.

REM 上传测试文件 - 分步骤测试
echo 7. 分步骤上传测试:

REM 测试极小文件
echo 7.1 测试极小文件(10字节):
echo test12345 > tiny-test.txt
mc.exe cp tiny-test.txt %MINIO_ALIAS%/images/tiny-test.txt
if %errorlevel% neq 0 (
    echo ❌ 极小文件上传失败
    echo 错误详情:
    mc.exe --debug cp tiny-test.txt %MINIO_ALIAS%/images/tiny-test-debug.txt
) else (
    echo ✅ 极小文件上传成功
    mc.exe rm %MINIO_ALIAS%/images/tiny-test.txt
)
echo.

REM 测试小文件
echo 7.2 测试小文件(1KB):
fsutil file createnew small-test.dat 1024
mc.exe cp small-test.dat %MINIO_ALIAS%/images/small-test.dat
if %errorlevel% neq 0 (
    echo ❌ 小文件上传失败
) else (
    echo ✅ 小文件上传成功
    mc.exe rm %MINIO_ALIAS%/images/small-test.dat
)
del small-test.dat 2>nul
echo.

REM 测试原始文件
echo 7.3 测试原始文件:
mc.exe cp test-mc.txt %MINIO_ALIAS%/images/test-mc.txt
if %errorlevel% neq 0 (
    echo ❌ 文件上传失败
    echo.
    echo 启用详细调试模式重试...
    set MC_DEBUG=1
    mc.exe --debug cp test-mc.txt %MINIO_ALIAS%/images/test-mc-debug.txt
    goto :upload_failed
else
    echo ✅ 文件上传成功
)
echo.

REM 验证上传
echo 7. 验证上传文件:
mc.exe ls %MINIO_ALIAS%/images/test-mc.txt
if %errorlevel% neq 0 (
    echo ❌ 上传文件验证失败
else
    echo ✅ 上传文件验证成功
)
echo.

REM 下载测试
echo 8. 下载测试文件:
mc.exe cp %MINIO_ALIAS%/images/test-mc.txt test-mc-download.txt
if %errorlevel% neq 0 (
    echo ❌ 文件下载失败
else
    echo ✅ 文件下载成功
    echo 下载文件内容:
    type test-mc-download.txt
)
echo.

REM 获取文件信息
echo 9. 获取文件详细信息:
mc.exe stat %MINIO_ALIAS%/images/test-mc.txt
echo.

REM 清理测试文件
echo 10. 清理测试文件:
mc.exe rm %MINIO_ALIAS%/images/test-mc.txt
del test-mc.txt 2>nul
del test-mc-download.txt 2>nul
echo ✅ 测试文件已清理
echo.

echo ========================================
echo ✅ mc.exe测试完成 - 所有测试通过
echo 这说明MinIO服务器工作正常，问题在Java客户端
echo ========================================
goto :end

:upload_failed
echo.
echo ========================================
echo ❌ mc.exe上传测试失败
echo ========================================
echo 这说明问题可能在MinIO服务器端或网络层面
echo.
echo 请检查:
echo 1. MinIO服务器状态
echo 2. 网络连接
echo 3. 访问密钥是否正确
echo 4. 存储桶权限
echo.
goto :error_analysis

:error_analysis
echo.
echo ========================================
echo 错误分析和建议
echo ========================================
echo.
echo 如果mc.exe也失败，说明问题不在Java代码，而在:
echo 1. MinIO服务器配置问题
echo 2. 网络连接问题  
echo 3. 认证信息错误
echo 4. 防火墙阻止
echo.
echo 请执行以下检查:
echo.
echo 1. 检查MinIO服务器日志:
echo    tail -f /var/log/minio/minio.log
echo.
echo 2. 检查网络连接:
echo    ping 192.168.11.98
echo    telnet 192.168.11.98 9000
echo.
echo 3. 检查MinIO控制台:
echo    浏览器访问: http://192.168.11.98:9001
echo.
echo 4. 检查防火墙:
echo    netsh advfirewall show allprofiles
echo.

:end
echo.
echo 测试完成，按任意键退出...
pause >nul
