@echo off
echo ========================================
echo 启动电测听设备通信服务 (Windows优化版)
echo ========================================
echo.

REM 检查Java版本
java -version
echo.

REM Windows + JDK 17 优化的JVM参数
set JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC

REM 网络优化参数 - 针对Windows 10 + JDK 17
set NETWORK_OPTS=-Djava.net.preferIPv4Stack=true
set NETWORK_OPTS=%NETWORK_OPTS% -Dsun.net.useExclusiveBind=false
set NETWORK_OPTS=%NETWORK_OPTS% -Djdk.net.hosts.file=false
set NETWORK_OPTS=%NETWORK_OPTS% -Djava.net.useSystemProxies=false
set NETWORK_OPTS=%NETWORK_OPTS% -Dnetworkaddress.cache.ttl=60
set NETWORK_OPTS=%NETWORK_OPTS% -Dnetworkaddress.cache.negative.ttl=10

REM HTTP客户端优化 - JDK 17
set HTTP_OPTS=-Djdk.httpclient.allowRestrictedHeaders=host,connection
set HTTP_OPTS=%HTTP_OPTS% -Djdk.httpclient.connectionPoolSize=1
set HTTP_OPTS=%HTTP_OPTS% -Djdk.httpclient.keepalive.timeout=5

REM Windows网络栈优化
set WINDOWS_OPTS=-Dsun.nio.ch.bugLevel=
set WINDOWS_OPTS=%WINDOWS_OPTS% -Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.WindowsSelectorProvider

REM 调试参数 (可选，用于问题排查)
REM set DEBUG_OPTS=-Djava.net.debug=all -Djavax.net.debug=all

REM 激活电测听Profile
set SPRING_PROFILES_ACTIVE=audiometry

REM 设置日志配置
set LOGGING_CONFIG=-Dlogging.config=classpath:logback-spring.xml

REM 合并所有JVM参数
set ALL_OPTS=%JAVA_OPTS% %NETWORK_OPTS% %HTTP_OPTS% %WINDOWS_OPTS% %LOGGING_CONFIG%

echo 启动参数:
echo   Profile: %SPRING_PROFILES_ACTIVE%
echo   JVM参数: %JAVA_OPTS%
echo   网络优化: %NETWORK_OPTS%
echo   HTTP优化: %HTTP_OPTS%
echo   Windows优化: %WINDOWS_OPTS%
echo.

REM 检查网络连通性
echo 检查MinIO连通性...
ping -n 1 ************* >nul
if %errorlevel% equ 0 (
    echo ✅ MinIO服务器网络连通
) else (
    echo ❌ MinIO服务器网络不通，请检查网络配置
    pause
    exit /b 1
)

REM 检查端口连通性
echo 检查MinIO端口...
telnet ************* 9000 2>nul
if %errorlevel% equ 0 (
    echo ✅ MinIO端口9000可访问
) else (
    echo ⚠️  MinIO端口9000可能不可访问，但继续启动
)

echo.
echo 正在启动电测听设备通信服务...
echo 如果遇到Connection reset错误，请检查:
echo   1. Windows防火墙设置
echo   2. 网络适配器驱动
echo   3. 企业网络策略
echo.

REM 启动应用
java %ALL_OPTS% -Dspring.profiles.active=%SPRING_PROFILES_ACTIVE% -jar target/comReader.jar

echo.
echo 服务已停止
pause
