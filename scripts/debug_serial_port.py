#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据接收问题排查脚本
用于自动化排查串口数据接收问题
"""

import requests
import json
import time
import sys

class SerialPortDebugger:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.debug_api = f"{base_url}/api/debug"
    
    def check_health(self):
        """检查调试器健康状态"""
        try:
            response = requests.get(f"{self.debug_api}/health")
            if response.status_code == 200:
                data = response.json()
                print("=== 调试器健康检查 ===")
                print(f"状态: {'正常' if data.get('success') else '异常'}")
                print(f"调试器启用: {data.get('debuggerEnabled')}")
                print(f"设备处理器可用: {data.get('deviceHandlerAvailable')}")
                print(f"时间戳: {data.get('timestamp')}")
                return data.get('success', False)
            else:
                print(f"健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"健康检查异常: {e}")
            return False
    
    def get_available_ports(self):
        """获取可用串口列表"""
        try:
            response = requests.get(f"{self.debug_api}/available-ports")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("=== 可用串口列表 ===")
                    ports = data.get('ports', [])
                    print(f"总数: {data.get('count', 0)}")
                    for port in ports:
                        print(f"端口: {port.get('name')}")
                        print(f"  描述: {port.get('description')}")
                        print(f"  位置: {port.get('location')}")
                        print(f"  状态: {'已打开' if port.get('isOpen') else '未打开'}")
                        print()
                    return ports
                else:
                    print(f"获取串口列表失败: {data.get('message')}")
            else:
                print(f"获取串口列表失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"获取串口列表异常: {e}")
        return []
    
    def check_port_status(self, port_name):
        """检查指定串口状态"""
        try:
            response = requests.get(f"{self.debug_api}/port-status/{port_name}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"=== 串口状态: {port_name} ===")
                    port_info = data.get('portInfo', {})
                    print(f"存在: {port_info.get('exists')}")
                    if port_info.get('exists'):
                        print(f"已打开: {port_info.get('isOpen')}")
                        print(f"描述: {port_info.get('description')}")
                        print(f"位置: {port_info.get('location')}")
                        if port_info.get('isOpen'):
                            print(f"波特率: {port_info.get('baudRate')}")
                            print(f"数据位: {port_info.get('dataBits')}")
                            print(f"停止位: {port_info.get('stopBits')}")
                            print(f"校验位: {port_info.get('parity')}")
                    return port_info
                else:
                    print(f"检查串口状态失败: {data.get('message')}")
            else:
                print(f"检查串口状态失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"检查串口状态异常: {e}")
        return {}
    
    def get_device_context(self, port_name):
        """获取设备上下文信息"""
        try:
            response = requests.get(f"{self.debug_api}/context/{port_name}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"=== 设备上下文: {port_name} ===")
                    context = data.get('context', {})
                    print(f"上下文ID: {context.get('contextId')}")
                    print(f"用户ID: {context.get('userId')}")
                    print(f"设备类型: {context.get('deviceType')}")
                    print(f"设备型号: {context.get('deviceModel')}")
                    print(f"状态: {context.get('status')}")
                    print(f"使用专有处理器: {context.get('useSpecializedProcessor')}")
                    print(f"需要缓冲: {context.get('deviceNeedsBuffering')}")
                    print(f"创建时间: {context.get('createTime')}")
                    print(f"最后活跃时间: {context.get('lastActiveTime')}")
                    return context
                else:
                    print(f"获取设备上下文失败: {data.get('message')}")
            else:
                print(f"获取设备上下文失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"获取设备上下文异常: {e}")
        return {}
    
    def start_debugging(self, port_name):
        """开始调试指定端口"""
        try:
            response = requests.post(f"{self.debug_api}/start/{port_name}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"开始调试端口: {port_name}")
                    return True
                else:
                    print(f"开始调试失败: {data.get('message')}")
            else:
                print(f"开始调试失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"开始调试异常: {e}")
        return False
    
    def get_debug_info(self, port_name):
        """获取调试信息"""
        try:
            response = requests.get(f"{self.debug_api}/info/{port_name}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"=== 调试信息: {port_name} ===")
                    debug_info = data.get('debugInfo', {})
                    print(f"端口状态: {debug_info.get('portStatus')}")
                    print(f"端口已打开: {debug_info.get('portOpened')}")
                    print(f"监听器已添加: {debug_info.get('listenerAdded')}")
                    print(f"数据接收次数: {debug_info.get('dataCount')}")
                    print(f"最后数据时间: {debug_info.get('lastDataTime')}")
                    if debug_info.get('lastError'):
                        print(f"最后错误: {debug_info.get('lastError')}")
                    return debug_info
                else:
                    print(f"获取调试信息失败: {data.get('message')}")
            else:
                print(f"获取调试信息失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"获取调试信息异常: {e}")
        return {}
    
    def comprehensive_check(self, port_name):
        """综合检查指定端口"""
        print(f"开始综合检查端口: {port_name}")
        print("=" * 50)
        
        # 1. 健康检查
        if not self.check_health():
            print("调试器健康检查失败，无法继续")
            return
        
        print()
        
        # 2. 检查串口状态
        port_info = self.check_port_status(port_name)
        if not port_info.get('exists'):
            print(f"端口 {port_name} 不存在，请检查端口名称")
            return
        
        print()
        
        # 3. 获取设备上下文
        context = self.get_device_context(port_name)
        
        print()
        
        # 4. 开始调试
        if self.start_debugging(port_name):
            print()
            
            # 5. 获取调试信息
            debug_info = self.get_debug_info(port_name)
            
            print()
            
            # 6. 分析和建议
            self.analyze_and_suggest(port_name, port_info, context, debug_info)
    
    def analyze_and_suggest(self, port_name, port_info, context, debug_info):
        """分析问题并给出建议"""
        print("=== 问题分析和建议 ===")
        
        issues = []
        suggestions = []
        
        # 检查端口状态
        if not port_info.get('isOpen'):
            issues.append("串口未打开")
            suggestions.append("检查串口连接流程是否正确执行")
        
        # 检查设备上下文
        if not context:
            issues.append("未找到设备上下文")
            suggestions.append("检查设备连接请求是否正确发送")
            suggestions.append("检查connectPort事件是否正确处理")
        
        # 检查调试信息
        if debug_info:
            if not debug_info.get('listenerAdded'):
                issues.append("数据监听器未添加")
                suggestions.append("检查addSerialPortDataListener方法是否被调用")
            
            if debug_info.get('dataCount', 0) == 0:
                issues.append("未接收到任何数据")
                suggestions.append("检查设备是否正在发送数据")
                suggestions.append("检查串口参数配置是否正确")
                suggestions.append("尝试使用串口调试工具验证数据发送")
        
        # 输出分析结果
        if issues:
            print("发现的问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("未发现明显问题")
        
        print()
        
        if suggestions:
            print("建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
        else:
            print("暂无特定建议")

def main():
    if len(sys.argv) < 2:
        print("用法: python debug_serial_port.py <端口名称> [服务器地址]")
        print("示例: python debug_serial_port.py COM3")
        print("示例: python debug_serial_port.py COM3 http://localhost:8080")
        return
    
    port_name = sys.argv[1]
    base_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8080"
    
    debugger = SerialPortDebugger(base_url)
    debugger.comprehensive_check(port_name)

if __name__ == "__main__":
    main()
