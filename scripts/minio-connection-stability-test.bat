@echo off
echo ========================================
echo MinIO连接稳定性测试
echo ========================================
echo.

set MINIO_ENDPOINT=http://*************:9000
set MINIO_ALIAS=stabtest

REM 检查mc.exe
where mc.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ mc.exe未找到
    exit /b 1
)

echo 配置MinIO连接...
mc.exe alias set %MINIO_ALIAS% %MINIO_ENDPOINT% aHCjKJhOJhOJhOJhO your-secret-key
echo.

echo 1. 连接稳定性测试 (连续10次):
echo ----------------------------------------
set SUCCESS_COUNT=0
set FAIL_COUNT=0

for /L %%i in (1,1,10) do (
    echo 第%%i次测试:
    mc.exe ls %MINIO_ALIAS% >nul 2>&1
    if !errorlevel! equ 0 (
        echo   ✅ 连接成功
        set /a SUCCESS_COUNT+=1
    ) else (
        echo   ❌ 连接失败
        set /a FAIL_COUNT+=1
    )
    timeout /t 2 /nobreak >nul
)

echo.
echo 连接测试结果: 成功 %SUCCESS_COUNT%/10, 失败 %FAIL_COUNT%/10
echo.

echo 2. 文件上传稳定性测试:
echo ----------------------------------------

REM 创建不同大小的测试文件
echo 创建测试文件...
echo tiny > tiny.txt
echo small_file_content_for_testing > small.txt
fsutil file createnew medium.dat 10240 >nul 2>&1

set UPLOAD_SUCCESS=0
set UPLOAD_FAIL=0

echo 测试tiny文件上传 (5次):
for /L %%i in (1,1,5) do (
    mc.exe cp tiny.txt %MINIO_ALIAS%/images/tiny%%i.txt >nul 2>&1
    if !errorlevel! equ 0 (
        echo   第%%i次: ✅ 成功
        set /a UPLOAD_SUCCESS+=1
        mc.exe rm %MINIO_ALIAS%/images/tiny%%i.txt >nul 2>&1
    ) else (
        echo   第%%i次: ❌ 失败
        set /a UPLOAD_FAIL+=1
    )
    timeout /t 1 /nobreak >nul
)

echo.
echo 测试small文件上传 (5次):
for /L %%i in (1,1,5) do (
    mc.exe cp small.txt %MINIO_ALIAS%/images/small%%i.txt >nul 2>&1
    if !errorlevel! equ 0 (
        echo   第%%i次: ✅ 成功
        set /a UPLOAD_SUCCESS+=1
        mc.exe rm %MINIO_ALIAS%/images/small%%i.txt >nul 2>&1
    ) else (
        echo   第%%i次: ❌ 失败
        set /a UPLOAD_FAIL+=1
    )
    timeout /t 1 /nobreak >nul
)

echo.
echo 测试medium文件上传 (3次):
for /L %%i in (1,1,3) do (
    mc.exe cp medium.dat %MINIO_ALIAS%/images/medium%%i.dat >nul 2>&1
    if !errorlevel! equ 0 (
        echo   第%%i次: ✅ 成功
        set /a UPLOAD_SUCCESS+=1
        mc.exe rm %MINIO_ALIAS%/images/medium%%i.dat >nul 2>&1
    ) else (
        echo   第%%i次: ❌ 失败
        set /a UPLOAD_FAIL+=1
    )
    timeout /t 2 /nobreak >nul
)

echo.
echo 上传测试结果: 成功 %UPLOAD_SUCCESS%/13, 失败 %UPLOAD_FAIL%/13
echo.

echo 3. 连接时间分析:
echo ----------------------------------------
echo 测试连接建立时间...

for /L %%i in (1,1,5) do (
    echo 第%%i次连接时间测试:
    powershell -Command "$start = Get-Date; try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('*************', 9000); $end = Get-Date; $duration = ($end - $start).TotalMilliseconds; Write-Host '  连接时间:' $duration 'ms'; $tcp.Close() } catch { Write-Host '  连接失败:' $_.Exception.Message }"
    timeout /t 1 /nobreak >nul
)

echo.

echo 4. 错误模式分析:
echo ----------------------------------------
echo 启用详细日志进行错误分析...
set MC_DEBUG=1

echo 尝试上传并捕获详细错误:
mc.exe --debug cp tiny.txt %MINIO_ALIAS%/images/debug-test.txt 2>debug-error.log
if %errorlevel% neq 0 (
    echo 错误详情:
    type debug-error.log
) else (
    echo 调试模式下上传成功
    mc.exe rm %MINIO_ALIAS%/images/debug-test.txt >nul 2>&1
)

echo.

echo 5. 网络中断模拟:
echo ----------------------------------------
echo 测试网络恢复后的连接...

REM 先确认连接正常
mc.exe ls %MINIO_ALIAS% >nul 2>&1
if %errorlevel% equ 0 (
    echo 初始连接正常
    
    echo 请手动断开网络连接10秒后重新连接，然后按任意键继续...
    pause >nul
    
    echo 测试网络恢复后的连接:
    for /L %%i in (1,1,5) do (
        mc.exe ls %MINIO_ALIAS% >nul 2>&1
        if !errorlevel! equ 0 (
            echo   第%%i次: ✅ 连接恢复
            goto :network_recovered
        ) else (
            echo   第%%i次: ❌ 连接未恢复
        )
        timeout /t 3 /nobreak >nul
    )
    
    :network_recovered
) else (
    echo 初始连接就有问题，跳过网络中断测试
)

echo.

echo 6. 清理测试文件:
echo ----------------------------------------
del tiny.txt 2>nul
del small.txt 2>nul
del medium.dat 2>nul
del debug-error.log 2>nul
echo 测试文件已清理

echo.
echo ========================================
echo 稳定性测试完成
echo ========================================
echo.
echo 总结:
echo - 连接成功率: %SUCCESS_COUNT%/10
echo - 上传成功率: %UPLOAD_SUCCESS%/13
echo.
echo 如果成功率低于80%%，建议检查:
echo 1. 网络设备配置
echo 2. MinIO服务器负载
echo 3. 防火墙或安全软件
echo 4. 网络质量和稳定性
echo.
pause
