@echo off
chcp 65001 >nul
echo ========================================
echo Test Manual Launch for Audiometry Software
echo ========================================
echo.

set SOFTWARE_PATH="C:\Program Files (x86)\Interacoustics\Diagnostic Suite\DiagnosticSuiteStandalone.exe"
set PROCESS_NAME=DiagnosticSuiteStandalone

echo Software Path: %SOFTWARE_PATH%
echo Process Name: %PROCESS_NAME%
echo.

echo 1. Check if software file exists...
if exist %SOFTWARE_PATH% (
    echo [OK] Software file exists
) else (
    echo [ERROR] Software file not found, please check path
    pause
    exit /b 1
)
echo.

echo 2. Check if process is already running...
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table | find /i "%PROCESS_NAME%" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [WARNING] Software is already running
    echo Do you want to close existing process? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        taskkill /f /im %PROCESS_NAME%.exe >nul 2>&1
        echo Existing process closed
        timeout /t 2 /nobreak >nul 2>&1
    )
) else (
    echo [OK] No running process detected
)
echo.

echo 3. Test manual launch simulation...
echo Command: cmd /c start "" %SOFTWARE_PATH%
cmd /c start "" %SOFTWARE_PATH%
echo [INFO] Launch command executed
echo.

echo 4. Wait for software to start...
timeout /t 5 /nobreak >nul 2>&1

echo 5. Check launch result...
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table 2>&1
echo.
echo Check process details (including window status)...
tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo csv /v 2>&1
echo.

tasklist /fi "imagename eq %PROCESS_NAME%.exe" /fo table | find /i "%PROCESS_NAME%" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] Software started successfully!
    echo.
    echo Please check if the software interface is displayed normally.
    echo If the interface is visible, the manual launch simulation works.
) else (
    echo [ERROR] Software failed to start
    echo.
    echo Possible reasons:
    echo 1. Incorrect software path
    echo 2. Missing software dependencies
    echo 3. Permission issues
    echo 4. System environment issues
)
echo.

echo 6. Close software after test? (Y/N)
set /p cleanup=
if /i "%cleanup%"=="Y" (
    echo Closing test software...
    taskkill /f /im %PROCESS_NAME%.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Software closed
    ) else (
        echo [INFO] No software process to close
    )
)
echo.

echo ========================================
echo Test Complete!
echo ========================================
echo.
echo If the software can start and display interface normally,
echo the manual launch simulation can solve your problem.
echo.
echo Next steps:
echo 1. Recompile and deploy updated code
echo 2. Restart application
echo 3. Test audiometry device connection
echo.

pause
