FROM ghcr.io/graalvm/graalvm-community:21

# Install Maven
RUN microdnf install -y wget tar gzip && \
    wget https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz && \
    tar -xzf apache-maven-3.9.6-bin.tar.gz -C /opt && \
    ln -s /opt/apache-maven-3.9.6/bin/mvn /usr/local/bin/mvn

# Set working directory
WORKDIR /app

# Copy project files
COPY . .

# Build native image
RUN mvn clean compile && mvn -Pnative native:compile

# The executable will be in target/checkupMain
