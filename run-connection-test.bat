@echo off
chcp 65001 >nul
echo ========================================
echo 连接测试脚本
echo ========================================
echo.

echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)

echo ✓ Java环境检查通过
echo.

echo 正在检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo ✓ Maven环境检查通过
echo.

:menu
echo ========================================
echo 请选择要执行的测试:
echo ========================================
echo 1. 测试主数据库连接
echo 2. 测试MinIO服务连接  
echo 3. 综合连接测试
echo 4. 运行所有连接测试
echo 5. 退出
echo ========================================
set /p choice=请输入选项 (1-5): 

if "%choice%"=="1" goto test_database
if "%choice%"=="2" goto test_minio
if "%choice%"=="3" goto test_all_connections
if "%choice%"=="4" goto test_all
if "%choice%"=="5" goto exit
echo 无效选项，请重新选择
goto menu

:test_database
echo.
echo 🔍 正在测试主数据库连接...
echo ========================================
mvn test -Dtest=ConnectionTest#testMainDatabaseConnection -q
if %errorlevel% equ 0 (
    echo ✓ 主数据库连接测试完成
) else (
    echo ❌ 主数据库连接测试失败
)
echo.
pause
goto menu

:test_minio
echo.
echo 🔍 正在测试MinIO服务连接...
echo ========================================
mvn test -Dtest=ConnectionTest#testMinioConnection -q
if %errorlevel% equ 0 (
    echo ✓ MinIO服务连接测试完成
) else (
    echo ❌ MinIO服务连接测试失败
)
echo.
pause
goto menu

:test_all_connections
echo.
echo 🔍 正在执行综合连接测试...
echo ========================================
mvn test -Dtest=ConnectionTest#testAllConnections -q
if %errorlevel% equ 0 (
    echo ✓ 综合连接测试完成
) else (
    echo ❌ 综合连接测试失败
)
echo.
pause
goto menu

:test_all
echo.
echo 🔍 正在运行所有连接测试...
echo ========================================
mvn test -Dtest=ConnectionTest -q
if %errorlevel% equ 0 (
    echo ✓ 所有连接测试完成
) else (
    echo ❌ 部分连接测试失败
)
echo.
pause
goto menu

:exit
echo.
echo 👋 测试完成，感谢使用！
pause
exit /b 0
