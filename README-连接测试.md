# 连接测试使用指南

## 概述

本项目提供了完整的连接测试解决方案，用于验证主数据库（MySQL）和MinIO对象存储服务的连接状态。

## 文件说明

### 测试文件
- `src/test/java/org/bj/ConnectionTest.java` - 主要的连接测试类
- `docs/连接测试说明文档.md` - 详细的技术文档

### 运行脚本
- `run-connection-test.bat` - Windows批处理脚本
- `run-connection-test.sh` - Linux/Mac Shell脚本

## 快速开始

### Windows用户

1. **双击运行脚本**
   ```
   双击 run-connection-test.bat 文件
   ```

2. **或者在命令行中运行**
   ```cmd
   run-connection-test.bat
   ```

### Linux/Mac用户

1. **在终端中运行**
   ```bash
   ./run-connection-test.sh
   ```

2. **如果权限不足，先添加执行权限**
   ```bash
   chmod +x run-connection-test.sh
   ./run-connection-test.sh
   ```

## 测试选项

运行脚本后，您将看到以下菜单：

```
========================================
请选择要执行的测试:
========================================
1. 测试主数据库连接
2. 测试MinIO服务连接  
3. 综合连接测试
4. 运行所有连接测试
5. 退出
========================================
```

### 选项说明

1. **测试主数据库连接** - 仅测试MySQL数据库连接
2. **测试MinIO服务连接** - 仅测试MinIO对象存储服务
3. **综合连接测试** - 测试所有服务并提供综合报告
4. **运行所有连接测试** - 执行完整的测试套件
5. **退出** - 退出测试程序

## 手动运行测试

如果您更喜欢使用Maven命令直接运行测试：

### 运行单个测试方法

```bash
# 测试主数据库连接
mvn test -Dtest=ConnectionTest#testMainDatabaseConnection

# 测试MinIO连接
mvn test -Dtest=ConnectionTest#testMinioConnection

# 综合连接测试
mvn test -Dtest=ConnectionTest#testAllConnections
```

### 运行整个测试类

```bash
mvn test -Dtest=ConnectionTest
```

## 配置要求

### 1. 主数据库配置

确保 `application.yml` 中的数据库配置正确：

```yaml
database:
  main:
    enabled: true
    type: mysql
    host: localhost
    port: 3306
    database: physicalex-lkd
    username: root
    password: root
```

### 2. MinIO配置

确保 `application.yml` 中的MinIO配置正确：

```yaml
minio:
  enabled: true  # 设置为true以启用MinIO测试
  endpoint: http://localhost:9000
  access-key: cIHSfxmIFVjpplRUQ80t
  secret-key: eJbm7pTyMWoylccTeuCJqFrz8A5NOmgR0YLzr6yW
```

## 环境准备

### 1. 启动MySQL数据库

确保MySQL服务正在运行：

**Windows:**
```cmd
net start mysql
```

**Linux/Mac:**
```bash
sudo systemctl start mysql
# 或
sudo service mysql start
```

### 2. 启动MinIO服务

**使用Docker:**
```bash
docker run -p 9000:9000 -p 9001:9001 \
  -e "MINIO_ROOT_USER=cIHSfxmIFVjpplRUQ80t" \
  -e "MINIO_ROOT_PASSWORD=eJbm7pTyMWoylccTeuCJqFrz8A5NOmgR0YLzr6yW" \
  minio/minio server /data --console-address ":9001"
```

**直接运行:**
```bash
# 下载MinIO
wget https://dl.min.io/server/minio/release/linux-amd64/minio
chmod +x minio

# 启动MinIO
export MINIO_ROOT_USER=cIHSfxmIFVjpplRUQ80t
export MINIO_ROOT_PASSWORD=eJbm7pTyMWoylccTeuCJqFrz8A5NOmgR0YLzr6yW
./minio server /data
```

## 测试结果解读

### 成功输出示例

```
=== 开始连接测试 ===
--- 开始测试主数据库连接 ---
数据库配置:
  启用状态: true
  数据库类型: mysql
  主机地址: localhost
  端口: 3306
  数据库名: physicalex-lkd
  用户名: root

数据库连接成功:
  数据库产品: MySQL
  数据库版本: 5.7.44
  驱动名称: MySQL Connector/J
  驱动版本: 8.0.33

✓ 主数据库连接正常
✓ MinIO服务连接正常

🎉 所有服务连接正常！
```

### 失败输出示例

```
❌ 主数据库连接失败: Connection refused
⚠️ MinIO服务不可用，可能是服务未启动或配置错误

❌ 所有服务连接异常，请检查配置和服务状态
```

## 故障排除

### 数据库连接问题

1. **检查MySQL服务状态**
   ```bash
   # Windows
   sc query mysql
   
   # Linux
   systemctl status mysql
   ```

2. **检查端口是否开放**
   ```bash
   telnet localhost 3306
   ```

3. **验证用户权限**
   ```sql
   SHOW GRANTS FOR 'root'@'localhost';
   ```

### MinIO连接问题

1. **检查MinIO服务状态**
   ```bash
   curl http://localhost:9000/minio/health/live
   ```

2. **检查端口是否开放**
   ```bash
   telnet localhost 9000
   ```

3. **验证访问密钥**
   - 确保配置文件中的access-key和secret-key正确
   - 检查MinIO控制台是否可以正常登录

### 常见错误及解决方案

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| Connection refused | 服务未启动 | 启动相应服务 |
| Access denied | 认证失败 | 检查用户名密码 |
| Unknown database | 数据库不存在 | 创建数据库 |
| Timeout | 网络问题 | 检查网络连接 |
| Bucket not found | 存储桶不存在 | MinIO会自动创建 |

## 在IDE中运行

### IntelliJ IDEA

1. 打开项目
2. 导航到 `src/test/java/org/bj/ConnectionTest.java`
3. 右键点击类名或方法名
4. 选择 "Run 'ConnectionTest'" 或 "Debug 'ConnectionTest'"

### Eclipse

1. 打开项目
2. 导航到测试类
3. 右键点击 → Run As → JUnit Test

## 注意事项

1. **测试环境隔离**: 测试使用独立的配置文件，不会影响生产环境
2. **资源清理**: 测试完成后会自动清理临时文件
3. **服务依赖**: 如果某个服务未启用，相应的测试会被跳过
4. **网络要求**: 确保测试环境可以访问配置的服务地址

## 技术支持

如果遇到问题，请：

1. 查看详细的测试日志输出
2. 参考 `docs/连接测试说明文档.md` 获取更多技术细节
3. 检查服务配置和网络连接
4. 确保所有依赖服务正常运行

---

**提示**: 建议在系统部署前运行完整的连接测试，确保所有依赖服务配置正确且连接正常。
