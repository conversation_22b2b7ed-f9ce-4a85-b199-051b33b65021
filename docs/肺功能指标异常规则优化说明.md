# 肺功能指标异常规则优化说明

## 概述

本次优化针对肺功能指标的异常判断规则进行了重大改进，支持基于预期值的对比判断，使异常判断更加准确和符合医学标准。

## 优化内容

### 1. 支持预期值对比的异常判断

#### 优化前
```yaml
abnormal-rules:
  - name: "FVC偏低"
    condition: "#value < 2.5"  # 固定阈值判断
    flag: "1"
    description: "偏低"
    symbol: "↓"
```

#### 优化后
```yaml
abnormal-rules:
  # 基于预期值的判断（优先级更高）
  - name: "FVC严重偏低"
    condition: "#FVC_PRE != null and (#value / #FVC_PRE) < 0.6"
    flag: "1"
    description: "严重偏低"
    symbol: "↓↓"
    priority: 1
  - name: "FVC偏低"
    condition: "#FVC_PRE != null and (#value / #FVC_PRE) < 0.8"
    flag: "1"
    description: "偏低"
    symbol: "↓"
    priority: 2
  # 基于固定阈值的备用判断（当没有预期值时）
  - name: "FVC绝对偏低"
    condition: "#FVC_PRE == null and #value < 2.5"
    flag: "1"
    description: "偏低"
    symbol: "↓"
    priority: 4
```

### 2. 支持指标间对比判断

#### FEV1/FVC比值计算
```yaml
abnormal-rules:
  # 基于实际FEV1和FVC值计算的比值判断
  - name: "FEV1/FVC严重偏低"
    condition: "#FEV1 != null and #FVC != null and (#FEV1 / #FVC * 100) < 60"
    flag: "1"
    description: "严重偏低"
    symbol: "↓↓"
    priority: 1
  - name: "FEV1/FVC偏低"
    condition: "#FEV1 != null and #FVC != null and (#FEV1 / #FVC * 100) < 70"
    flag: "1"
    description: "偏低"
    symbol: "↓"
    priority: 2
```

### 3. 预期值配置支持

#### 预期值字段映射
```yaml
predicted-values:
  enabled: true
  field-mapping:
    FVC_PRE: "FVC预计值"      # FVC预期值字段名
    FEV1_PRE: "FEV1预计值"    # FEV1预期值字段名
    PEF_PRE: "PEF预计值"      # PEF预期值字段名
    VC_PRE: "VC预计值"        # VC预期值字段名
    MVV_PRE: "MVV预计值"      # MVV预期值字段名
```

#### 预期值计算公式（可选）
```yaml
calculation-formulas:
  # 男性FVC预期值计算公式
  FVC_PRE_MALE: "(0.0576 * #height - 0.026 * #age - 4.34)"
  # 女性FVC预期值计算公式  
  FVC_PRE_FEMALE: "(0.0491 * #height - 0.019 * #age - 3.76)"
```

## 技术实现

### 1. LungFunctionExpressionService增强

#### 新增方法
- `calculateAbnormalStatus(indicator, value, allValues, patientInfo)` - 支持患者信息的异常状态计算
- `enhanceValuesWithPredicted(allValues, patientInfo)` - 增强指标值，添加预期值
- `calculatePredictedValues(enhanced, patientInfo, formulas)` - 根据患者信息计算预期值

#### 预期值处理逻辑
1. **从数据中提取预期值**：如果设备提供预期值，直接使用
2. **计算预期值**：如果没有预期值但有患者信息，根据公式计算
3. **变量绑定**：将预期值绑定到SpEL表达式上下文中

### 2. 表达式变量支持

#### 可用变量
- `#value` - 当前指标值
- `#FVC`, `#FEV1`, `#PEF` 等 - 其他指标的实际值
- `#FVC_PRE`, `#FEV1_PRE` 等 - 预期值
- `#age`, `#height`, `#weight` - 患者基本信息
- `#isMale`, `#isFemale` - 性别标识

#### 表达式示例
```yaml
# 基于预期值的百分比判断
condition: "#FVC_PRE != null and (#value / #FVC_PRE) < 0.8"

# 指标间比值判断
condition: "#FEV1 != null and #FVC != null and (#FEV1 / #FVC * 100) < 70"

# 复合条件判断
condition: "#FVC_PRE == null and #value < 2.5"
```

## 配置说明

### 1. 异常规则优先级

规则按`priority`字段排序执行，数字越小优先级越高：
1. 基于预期值的严重异常判断（priority: 1）
2. 基于预期值的一般异常判断（priority: 2-3）
3. 基于固定阈值的备用判断（priority: 4-5）

### 2. 预期值来源

支持两种预期值来源：
1. **设备提供**：通过`field-mapping`配置从设备数据中提取
2. **公式计算**：通过`calculation-formulas`配置根据患者信息计算

### 3. 兼容性保证

- 如果没有预期值，自动降级到固定阈值判断
- 如果表达式计算失败，返回正常状态
- 保持向后兼容，现有配置仍然有效

## 医学意义

### 1. 更准确的异常判断

- **个体化判断**：考虑患者年龄、性别、身高等因素
- **相对值判断**：使用实际值与预期值的比值，而非绝对阈值
- **分级判断**：支持正常、偏低、严重偏低等多级判断

### 2. 符合医学标准

- **FVC判断**：实际值/预期值 < 80% 为偏低，< 60% 为严重偏低
- **FEV1判断**：同FVC标准
- **FEV1/FVC比值**：< 70% 提示阻塞性通气功能障碍

### 3. 临床应用价值

- 提高诊断准确性
- 减少误诊和漏诊
- 为临床决策提供更可靠的依据

## 使用示例

### 1. 启用预期值判断
```yaml
fgy200:
  lung-function:
    predicted-values:
      enabled: true
      field-mapping:
        FVC_PRE: "FVC预计值"
        FEV1_PRE: "FEV1预计值"
```

### 2. 自定义异常规则
```yaml
indicators:
  - code: "FVC"
    abnormal-rules:
      - name: "FVC轻度偏低"
        condition: "#FVC_PRE != null and (#value / #FVC_PRE) >= 0.7 and (#value / #FVC_PRE) < 0.8"
        flag: "1"
        description: "轻度偏低"
        symbol: "↓"
        priority: 2
```

### 3. 复杂表达式示例
```yaml
# 综合判断：考虑年龄因素的FEV1/FVC比值
condition: "#age != null and #FEV1 != null and #FVC != null and (#age > 65 ? (#FEV1/#FVC*100) < 65 : (#FEV1/#FVC*100) < 70)"
```

## 注意事项

1. **表达式语法**：使用Spring Expression Language (SpEL)语法
2. **空值处理**：表达式中需要检查变量是否为null
3. **性能考虑**：复杂表达式可能影响计算性能
4. **测试验证**：新规则需要充分测试验证准确性

## 后续扩展

1. **更多预期值公式**：支持更多年龄段和人群的预期值计算
2. **动态阈值**：根据人群统计数据动态调整异常阈值
3. **机器学习**：引入机器学习模型进行异常判断
4. **多维度判断**：结合多个指标进行综合异常判断
