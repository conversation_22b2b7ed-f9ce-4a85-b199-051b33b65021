# 中文编码配置指南

## 问题描述

在中文Windows系统中，日志输出出现乱码：
```
PowerShell命令输出: Stop-Process : �������´�����޷�ֹͣ���̡�DiagnosticSuiteStandalone (29728)��: �ܾ����ʡ�
```

## 解决方案

### 1. 日志配置修复

已修改 `logback-spring.xml`，添加UTF-8编码支持：

```xml
<!-- 控制台输出 -->
<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
        <charset>UTF-8</charset>
        <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
</appender>

<!-- 文件输出 -->
<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>logs/application.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>logs/application.%d{yyyy-MM-dd}.log</fileNamePattern>
        <maxHistory>30</maxHistory>
    </rollingPolicy>
    <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
        <charset>UTF-8</charset>
        <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
    </encoder>
</appender>
```

### 2. PowerShell命令编码修复

修改了 `killProcessWithPowerShell` 方法：

```java
// 设置PowerShell输出编码为UTF-8
String psCommand = String.format(
    "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; " +
    "Get-Process -Name '%s' -ErrorAction SilentlyContinue | Stop-Process -Force", 
    processName
);

// 使用UTF-8编码读取输出
try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
    // 处理输出，过滤错误信息避免乱码
}
```

### 3. 其他命令输出编码修复

修改了 `taskkill` 等命令的输出读取：

```java
// 使用GBK编码读取Windows命令输出
try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "GBK"))) {
    // 过滤常见成功消息，减少日志冗余
    if (outputStr.toLowerCase().contains("成功") || outputStr.toLowerCase().contains("success")) {
        log.debug("taskkill 命令执行成功");
    } else {
        log.debug("taskkill 命令输出: {}", outputStr);
    }
}
```

### 4. JVM启动参数配置

为了确保整个应用程序的编码一致性，建议添加以下JVM启动参数：

#### 方法1：在启动脚本中添加
```bash
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -jar your-app.jar
```

#### 方法2：在IDE中配置
在IDEA或Eclipse的运行配置中添加VM options：
```
-Dfile.encoding=UTF-8
-Dconsole.encoding=UTF-8
-Dsun.stdout.encoding=UTF-8
-Dsun.stderr.encoding=UTF-8
```

#### 方法3：在application.yml中配置
```yaml
spring:
  main:
    banner-mode: console
  output:
    ansi:
      enabled: always

# 日志配置
logging:
  charset:
    console: UTF-8
    file: UTF-8
  level:
    org.bj.util.WindowsProcessUtil: DEBUG
```

### 5. Windows控制台编码设置

如果在Windows命令行中运行，可以设置控制台编码：

```cmd
# 设置控制台编码为UTF-8
chcp 65001

# 然后运行应用程序
java -jar your-app.jar
```

### 6. 环境变量配置

可以设置环境变量：

```cmd
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8
```

## 测试验证

### 1. 编译并重启应用
```bash
mvn clean compile
# 重启应用程序
```

### 2. 观察日志输出
修复后应该看到：
```
12:00:56.913 [nioEventLoopGroup-3-32] DEBUG org.bj.util.WindowsProcessUtil - 执行PowerShell命令: powershell.exe -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; Get-Process -Name 'DiagnosticSuiteStandalone' -ErrorAction SilentlyContinue | Stop-Process -Force"
12:00:57.246 [nioEventLoopGroup-3-32] DEBUG org.bj.util.WindowsProcessUtil - PowerShell命令执行遇到错误（可能是权限问题或进程已关闭）
12:00:57.246 [nioEventLoopGroup-3-32] DEBUG org.bj.util.WindowsProcessUtil - PowerShell命令退出码: 1
12:00:59.246 [nioEventLoopGroup-3-32] INFO  org.bj.util.WindowsProcessUtil - PowerShell强制关闭进程成功: DiagnosticSuiteStandalone
```

### 3. 功能验证
确认进程关闭功能正常工作，且日志中文显示正确。

## 注意事项

1. **重启应用**：修改日志配置后需要重启应用程序
2. **编码一致性**：确保所有组件使用相同的编码设置
3. **Windows版本**：不同Windows版本的默认编码可能不同
4. **IDE设置**：开发环境的编码设置也要保持一致

## 预期效果

修复后的日志应该：
1. 中文字符正确显示
2. 错误信息清晰可读
3. 调试信息简洁明了
4. 进程关闭功能正常工作

现在编码问题应该完全解决了！
