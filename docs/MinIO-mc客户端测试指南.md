# MinIO mc客户端测试指南

## 1. 下载和安装mc.exe

### Windows环境下载
```bash
# 方法1: 直接下载
# 访问: https://dl.min.io/client/mc/release/windows-amd64/mc.exe
# 下载后重命名为mc.exe并放到PATH路径中

# 方法2: 使用PowerShell下载
Invoke-WebRequest -Uri "https://dl.min.io/client/mc/release/windows-amd64/mc.exe" -OutFile "mc.exe"

# 方法3: 使用curl下载
curl -O https://dl.min.io/client/mc/release/windows-amd64/mc.exe
```

### 验证安装
```bash
mc.exe --version
```

## 2. 配置MinIO服务器连接

### 基本配置
```bash
# 配置服务器别名
mc.exe alias set myminio http://*************:9000 ACCESS_KEY SECRET_KEY

# 示例（请替换为实际的密钥）
mc.exe alias set myminio http://*************:9000 aHCjKJhOJhOJhOJhO your-secret-key
```

### 验证配置
```bash
# 列出配置的服务器
mc.exe alias list

# 测试连接
mc.exe admin info myminio
```

## 3. 基本操作测试

### 列出存储桶
```bash
# 列出所有存储桶
mc.exe ls myminio

# 列出特定存储桶内容
mc.exe ls myminio/images/
```

### 创建存储桶
```bash
# 创建新存储桶
mc.exe mb myminio/test-bucket

# 创建images存储桶（如果不存在）
mc.exe mb myminio/images
```

### 文件上传测试
```bash
# 创建测试文件
echo "Test content" > test.txt

# 上传文件
mc.exe cp test.txt myminio/images/test.txt

# 上传并显示进度
mc.exe cp --progress test.txt myminio/images/test-progress.txt
```

### 文件下载测试
```bash
# 下载文件
mc.exe cp myminio/images/test.txt downloaded-test.txt

# 验证文件内容
type downloaded-test.txt
```

### 文件信息查看
```bash
# 查看文件详细信息
mc.exe stat myminio/images/test.txt

# 列出文件详细信息
mc.exe ls --recursive myminio/images/
```

## 4. 高级测试

### 大文件上传测试
```bash
# 创建大文件（10MB）
fsutil file createnew large-test.dat 10485760

# 上传大文件
mc.exe cp large-test.dat myminio/images/large-test.dat

# 检查上传结果
mc.exe stat myminio/images/large-test.dat
```

### 并发上传测试
```bash
# 创建多个测试文件
for /L %i in (1,1,5) do echo Test file %i > test%i.txt

# 批量上传
for /L %i in (1,1,5) do mc.exe cp test%i.txt myminio/images/test%i.txt
```

### 网络性能测试
```bash
# 测试上传速度
mc.exe cp --progress large-test.dat myminio/images/speed-test.dat

# 测试下载速度  
mc.exe cp --progress myminio/images/speed-test.dat downloaded-speed-test.dat
```

## 5. 诊断和调试

### 启用详细日志
```bash
# 设置调试级别
set MC_DEBUG=1

# 或者使用--debug参数
mc.exe --debug cp test.txt myminio/images/debug-test.txt
```

### 网络连接诊断
```bash
# 检查服务器健康状态
mc.exe admin info myminio

# 检查服务器配置
mc.exe admin config get myminio

# 检查服务器日志
mc.exe admin logs myminio
```

### 权限测试
```bash
# 测试读权限
mc.exe ls myminio/images/

# 测试写权限
mc.exe cp test.txt myminio/images/permission-test.txt

# 测试删除权限
mc.exe rm myminio/images/permission-test.txt
```

## 6. 故障排查

### 常见错误和解决方案

#### 错误1: "Access Denied"
```bash
# 检查访问密钥
mc.exe alias list

# 重新配置
mc.exe alias set myminio http://*************:9000 NEW_ACCESS_KEY NEW_SECRET_KEY
```

#### 错误2: "Connection refused"
```bash
# 检查网络连通性
ping *************
telnet ************* 9000

# 检查MinIO服务状态
mc.exe admin info myminio
```

#### 错误3: "Bucket does not exist"
```bash
# 创建存储桶
mc.exe mb myminio/images

# 验证存储桶
mc.exe ls myminio
```

### 网络问题诊断
```bash
# 使用curl测试HTTP连接
curl -v http://*************:9000/

# 检查DNS解析
nslookup *************

# 检查路由
tracert *************
```

## 7. 与Java客户端对比

### 如果mc.exe成功，Java失败
说明问题在Java客户端配置：
- HTTP客户端配置问题
- JDK版本兼容性问题
- 网络库配置问题

### 如果mc.exe也失败
说明问题在环境层面：
- MinIO服务器问题
- 网络连接问题
- 认证配置问题
- 防火墙阻止

## 8. 自动化测试脚本

### 完整测试脚本
运行提供的`minio-mc-test.bat`脚本进行自动化测试：

```bash
# 执行完整测试
scripts\minio-mc-test.bat
```

### 快速验证脚本
```bash
@echo off
echo 快速MinIO验证...

REM 配置连接
mc.exe alias set quicktest http://*************:9000 ACCESS_KEY SECRET_KEY

REM 快速测试
echo Test > quick.txt
mc.exe cp quick.txt quicktest/images/quick.txt
if %errorlevel% equ 0 (
    echo ✅ MinIO上传成功
) else (
    echo ❌ MinIO上传失败
)

REM 清理
mc.exe rm quicktest/images/quick.txt 2>nul
del quick.txt 2>nul
```

## 9. 结果分析

### mc.exe测试成功的情况
如果mc.exe能够成功上传文件，说明：
- MinIO服务器工作正常
- 网络连接正常
- 认证信息正确
- **问题确实在Java客户端**

### mc.exe测试失败的情况
如果mc.exe也失败，需要检查：
- MinIO服务器状态
- 网络配置
- 防火墙设置
- 认证信息

通过mc.exe的测试结果，我们可以明确问题的根源，然后针对性地解决Java客户端的问题。
