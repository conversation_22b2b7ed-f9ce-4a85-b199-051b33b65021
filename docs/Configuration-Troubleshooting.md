# 配置参数问题排查指南

## 问题描述

配置参数没有生效，可能的原因和解决方案。

## 排查步骤

### 1. 检查配置文件是否正确

确认 `src/main/resources/application-audiometry.yml` 文件内容：

```yaml
audiometry:
  software-management:
    auto-close-after-measurement: false      # 应该是 false
    auto-start-before-measurement: true      # 应该是 true
    close-before-start: true                 # 应该是 true
    restart-strategy:
      enabled: false                         # 应该是 false
      wait-before-restart: 2
      wait-after-restart: 3
      max-restart-attempts: 3
```

### 2. 检查应用程序启动时的Profile

确认应用程序使用了正确的Profile：

```bash
# 检查启动日志中是否包含
The following profiles are active: audiometry
```

如果没有，需要设置Profile：

```bash
# 方法1：启动参数
java -Dspring.profiles.active=audiometry -jar your-app.jar

# 方法2：环境变量
export SPRING_PROFILES_ACTIVE=audiometry

# 方法3：在application.yml中设置
spring:
  profiles:
    active: audiometry
```

### 3. 检查配置类是否正确加载

查看启动日志中是否有配置相关的错误信息。

### 4. 添加调试日志

已在代码中添加了配置状态打印，重启应用后查看日志：

```
INFO  org.bj.device.processors.AudiometryDeviceProcessor - === 软件管理配置状态 ===
INFO  org.bj.device.processors.AudiometryDeviceProcessor - autoCloseAfterMeasurement: false
INFO  org.bj.device.processors.AudiometryDeviceProcessor - autoStartBeforeMeasurement: true
INFO  org.bj.device.processors.AudiometryDeviceProcessor - closeBeforeStart: true
INFO  org.bj.device.processors.AudiometryDeviceProcessor - restartStrategy.enabled: false
```

### 5. 验证配置生效

连接听力设备时，观察日志输出：

#### 如果配置正确，应该看到：
```
INFO  - 使用传统启动方式
INFO  - === 软件关闭配置状态 ===
INFO  - autoCloseAfterMeasurement: false
```

#### 如果配置错误，可能看到：
```
INFO  - 启用软件重启策略，确保软件重新初始化
INFO  - autoCloseAfterMeasurement: true
```

## 常见问题和解决方案

### 问题1：配置文件格式错误

**症状**：应用启动失败或配置使用默认值

**解决方案**：
1. 检查YAML缩进是否正确（使用空格，不要使用Tab）
2. 检查冒号后是否有空格
3. 检查布尔值是否正确（true/false，不要用True/False）

### 问题2：Profile未激活

**症状**：配置使用默认值，不是YAML文件中的值

**解决方案**：
1. 确认启动时指定了 `-Dspring.profiles.active=audiometry`
2. 或在主配置文件中设置默认Profile

### 问题3：配置类未扫描到

**症状**：注入的配置对象为null或使用默认值

**解决方案**：
1. 确认 `AudiometryProperties` 类有 `@Component` 和 `@ConfigurationProperties` 注解
2. 确认类在Spring扫描路径下

### 问题4：配置缓存问题

**症状**：修改配置文件后不生效

**解决方案**：
1. 重启应用程序
2. 清理编译缓存：`mvn clean compile`

### 问题5：属性名映射问题

**症状**：部分配置生效，部分不生效

**解决方案**：
1. 确认YAML中的kebab-case属性名正确
2. 确认Java类中的camelCase属性名正确
3. Spring Boot会自动转换 `auto-close-after-measurement` → `autoCloseAfterMeasurement`

## 调试方法

### 方法1：启用配置调试日志

在 `application.yml` 中添加：

```yaml
logging:
  level:
    org.springframework.boot.context.properties: DEBUG
    org.bj.config.AudiometryProperties: DEBUG
    org.bj.device.processors.AudiometryDeviceProcessor: DEBUG
```

### 方法2：使用Actuator查看配置

添加依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

访问：`http://localhost:8080/actuator/configprops`

### 方法3：代码中打印配置

已在 `AudiometryDeviceProcessor` 中添加了配置打印代码。

## 验证配置是否生效的方法

### 1. 查看启动日志

重启应用程序，查看是否有配置相关的日志输出。

### 2. 连接设备测试

连接听力设备，观察软件启动和关闭行为：

- `restart-strategy.enabled: false` → 应该看到"使用传统启动方式"
- `auto-close-after-measurement: false` → 测量完成后软件不应该自动关闭
- `close-before-start: true` → 启动前应该先关闭已运行的软件

### 3. 手动验证

可以临时修改配置文件中的值，重启应用，观察行为是否改变。

## 快速修复步骤

1. **重启应用程序**（最重要）
2. **检查Profile设置**：确认使用 `audiometry` profile
3. **查看调试日志**：观察配置打印信息
4. **验证行为**：连接设备测试实际行为

## 配置文件模板

如果配置文件有问题，可以使用以下模板：

```yaml
spring:
  profiles:
    active: audiometry

---
spring:
  config:
    activate:
      on-profile: audiometry

audiometry:
  software-management:
    auto-close-after-measurement: false
    auto-start-before-measurement: true
    close-before-start: true
    close-wait-timeout: 10
    start-wait-timeout: 15
    run-as-admin: true
    auto-detect-admin-required: true
    restart-strategy:
      enabled: false
      wait-before-restart: 2
      wait-after-restart: 3
      max-restart-attempts: 3
```

现在请重启应用程序，然后连接听力设备，查看日志中的配置状态输出！
