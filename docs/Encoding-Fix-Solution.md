# 中文Windows系统编码问题修复方案

## 问题分析

从您提供的日志可以看出问题的根本原因：

### 原始日志分析
```
11:51:28.930 [nioEventLoopGroup-3-22] DEBUG org.bj.util.WindowsProcessUtil - tasklist 命令退出码: 0, 输出: 
ӳ������                       PID �Ự��              �Ự#       �ڴ�ʹ�� 
========================= ======== ================ =========== ============
DiagnosticSuiteStandalone     5064 Console                    1    236,812 K

11:51:28.930 [nioEventLoopGroup-3-22] DEBUG org.bj.util.WindowsProcessUtil - 进程 DiagnosticSuiteStandalone 运行状态: false
```

### 问题分析
1. **进程确实存在**：tasklist 找到了 `DiagnosticSuiteStandalone     5064`
2. **编码问题**：表头显示为乱码 `ӳ������` 而不是正确的中文
3. **匹配失败**：由于编码问题，字符串匹配失败，导致误判进程不存在

## 根本原因

**中文Windows系统编码问题**：
- Windows命令行默认使用 **GBK/GB2312** 编码
- Java程序默认使用 **UTF-8** 编码读取
- 编码不匹配导致中文字符显示为乱码，进而影响字符串匹配

## 解决方案

### 1. 修复编码读取

**修改前：**
```java
try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
    // 使用默认编码，在中文Windows上会出现乱码
}
```

**修改后：**
```java
try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "GBK"))) {
    // 明确指定GBK编码，正确读取中文Windows命令输出
}
```

### 2. 使用CSV格式避免表头问题

**修改前：**
```bash
tasklist /fi "imagename eq DiagnosticSuiteStandalone.exe"
```

**修改后：**
```bash
tasklist /fi "imagename eq DiagnosticSuiteStandalone.exe" /fo csv /nh
```

- `/fo csv`：输出CSV格式，避免表头对齐问题
- `/nh`：不显示表头，避免中文乱码干扰

### 3. 多重备用检测方法

实现了三层检测机制：

#### 方法1：改进的tasklist + CSV格式
```java
String command = String.format("tasklist /fi \"imagename eq %s.exe\" /fo csv /nh", processName);
// 使用GBK编码读取
try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "GBK"))) {
    // CSV格式解析
}
```

#### 方法2：WMIC命令检测
```java
String command = String.format("wmic process where \"name='%s.exe'\" get ProcessId /format:csv", processName);
// WMIC输出更稳定，较少受编码影响
```

#### 方法3：简单tasklist + findstr
```java
String command = String.format("tasklist | findstr /i %s.exe", processName);
// 最简单的方法，直接在命令行过滤
```

### 4. 智能检测流程

```java
public boolean isProcessRunning(String processName) {
    // 1. 尝试CSV格式的tasklist
    boolean isRunning = checkWithTasklistCSV(processName);
    
    // 2. 失败时尝试WMIC
    if (!isRunning && exitCode == 0 && hasOutput) {
        isRunning = checkProcessWithWMIC(processName);
    }
    
    return isRunning;
}

public boolean closeProgram(String programPath) {
    // 1. 主检测方法
    boolean processExists = isProcessRunning(processName);
    
    // 2. 备用检测方法
    if (!processExists) {
        processExists = checkProcessWithSimpleTasklist(processName);
    }
    
    // 3. 确认存在后开始关闭流程
    if (processExists) {
        // 执行关闭操作
    }
}
```

## 测试验证

### 1. 运行编码测试
```java
// 编译并运行测试
mvn test-compile
java -cp target/test-classes:target/classes org.bj.util.EncodingFixTest
```

### 2. 预期结果
修复后应该看到：
```
=== 编码修复测试 ===
测试进程: DiagnosticSuiteStandalone

--- 方法1：改进后的isProcessRunning ---
结果: true

--- 方法2：简单tasklist检测 ---
结果: true

--- 方法3：WMIC检测 ---
结果: true

--- 方法4：获取PID ---
找到PID: [5064]
PID数量: 1
```

### 3. 实际应用测试
```java
WindowsProcessUtil util = new WindowsProcessUtil();
String softwarePath = "C:/Program Files (x86)/Interacoustics/Diagnostic Suite/DiagnosticSuiteStandalone.exe";

// 现在应该能正确检测和关闭
boolean closed = util.closeAudiometrySoftware(softwarePath);
```

## 关键改进点

### 1. 编码兼容性
- 所有命令输出都使用GBK编码读取
- 兼容中文Windows系统

### 2. 命令格式优化
- 使用CSV格式避免表头对齐问题
- 使用/nh参数避免中文表头干扰

### 3. 多重保险机制
- 三种不同的检测方法
- 自动降级到备用方法

### 4. 调试信息增强
- 详细的命令执行日志
- 清晰的结果验证信息

## 预期效果

修复后，您应该看到类似这样的正确日志：

```
INFO  org.bj.util.WindowsProcessUtil - === 开始终极关闭程序流程 ===
INFO  org.bj.util.WindowsProcessUtil - 目标程序: C:/Program Files (x86)/Interacoustics/Diagnostic Suite/DiagnosticSuiteStandalone.exe
INFO  org.bj.util.WindowsProcessUtil - 提取的进程名: DiagnosticSuiteStandalone
DEBUG org.bj.util.WindowsProcessUtil - 检查进程运行状态，执行命令: tasklist /fi "imagename eq DiagnosticSuiteStandalone.exe" /fo csv /nh
DEBUG org.bj.util.WindowsProcessUtil - tasklist 命令退出码: 0, 输出: "DiagnosticSuiteStandalone.exe","5064","Console","1","236,812 K"
DEBUG org.bj.util.WindowsProcessUtil - 找到运行中的进程: "DiagnosticSuiteStandalone.exe","5064","Console","1","236,812 K"
DEBUG org.bj.util.WindowsProcessUtil - 进程 DiagnosticSuiteStandalone 运行状态: true
INFO  org.bj.util.WindowsProcessUtil - 确认进程 DiagnosticSuiteStandalone 正在运行，开始关闭流程
```

现在编码问题已经修复，进程检测应该能正常工作了！
