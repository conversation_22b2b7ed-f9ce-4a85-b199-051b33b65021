# 电测听设备API说明

## 概述

本文档描述了电测听设备处理器提供的API接口和事件机制。

## 设备处理器接口

### 基本信息

- **设备型号**: `丹麦国际听力电测听`
- **版本**: `1.0.0`
- **描述**: `电测听设备处理器 - 支持OtoAccess ADI Server接口`

### 支持的功能特性

```java
// 查询设备支持的功能
boolean supportsFeature(String feature)
```

支持的功能列表：
- `HTTP_COMMUNICATION`: HTTP通信
- `HOOK_MECHANISM`: Hook钩子机制
- `PATIENT_SETUP`: 患者设置
- `RESULT_MONITORING`: 结果监控
- `XML_DATA_PARSING`: XML数据解析
- `MULTI_FREQUENCY_TESTING`: 多频率测试
- `BILATERAL_TESTING`: 双耳测试
- `QUERY_RESULTS`: 结果查询
- `SOFTWARE_CONTROL`: 软件启动和关闭控制
- `AUTO_SOFTWARE_MANAGEMENT`: 自动软件管理

## 设备连接API

### 连接设备

**方法**: `handleConnection(DeviceContext context)`

**流程**:
1. 检查服务器连接
2. 注册Hook钩子
3. 设置诊所信息
4. 设置检查员信息
5. 设置患者信息
6. 启动设备软件
7. 注册监控任务

**返回**: `boolean` - 连接是否成功

### 设备状态查询

**方法**: `getDeviceStatus(DeviceContext context)`

**返回**: `String` - 设备状态
- `CONNECTED`: 已连接
- `DISCONNECTED`: 未连接
- `ERROR`: 错误状态

## 命令发送API

### 发送命令

**方法**: `sendCommand(DeviceContext context, String command)`

**支持的命令**:
- `START_MEASUREMENT`: 开始测量
- `QUERY_RESULTS`: 查询结果
- `RESET`: 重置设备
- `START_SOFTWARE`: 启动软件
- `STOP_SOFTWARE`: 关闭软件

**返回**: `boolean` - 命令执行是否成功

### 命令示例

```java
// 开始测量
boolean success = processor.sendCommand(context, "START_MEASUREMENT");

// 查询结果
boolean success = processor.sendCommand(context, "QUERY_RESULTS");

// 重置设备
boolean success = processor.sendCommand(context, "RESET");

// 启动软件
boolean success = processor.sendCommand(context, "START_SOFTWARE");

// 关闭软件
boolean success = processor.sendCommand(context, "STOP_SOFTWARE");
```

## 数据处理API

### 处理数据

**方法**: `handleData(DeviceContext context, String rawData)`

**特殊数据**:
- `QUERY_RESULTS`: 触发结果查询

**返回**: `boolean` - 数据处理是否成功

## 监控API

### 执行监控

**方法**: `executeMonitoring(DeviceContext context, Object customData)`

**功能**: 定期查询最新测试结果

**返回**: `boolean` - 监控执行是否成功

### 监控配置

**方法**: `getMonitoringConfig()`

**返回**: `MonitoringConfig`
- 监控间隔: 30秒
- 超时时间: 300秒
- 最大错误次数: 5次

## WebSocket事件

### 设备状态事件

#### 连接成功事件
```json
{
  "event": "deviceConnected",
  "data": {
    "deviceId": "丹麦国际听力电测听_session123",
    "status": "CONNECTED",
    "message": "电测听设备连接成功，请在设备上进行测试",
    "timestamp": 1704678000000
  }
}
```

#### 连接失败事件
```json
{
  "event": "deviceConnectionError",
  "data": {
    "deviceId": "丹麦国际听力电测听_session123",
    "error": "无法连接到电测听服务器",
    "timestamp": 1704678000000
  }
}
```

#### 统一设备状态事件
```json
{
  "event": "deviceStatus",
  "data": {
    "deviceId": "丹麦国际听力电测听_session123",
    "deviceModel": "丹麦国际听力电测听",
    "status": "CONNECTED",
    "statusCode": "DEVICE_CONNECTED",
    "message": "设备连接成功",
    "timestamp": 1704678000000,
    "sessionId": "session123"
  }
}
```

### 测量事件

#### 测量开始事件
```json
{
  "event": "measurementStarted",
  "data": {
    "deviceId": "丹麦国际听力电测听_session123",
    "status": "MEASUREMENT_STARTED",
    "message": "请在设备上开始听力测试",
    "timestamp": 1704678000000
  }
}
```

#### 测量结果事件
```json
{
  "event": "testResult",
  "data": {
    "data": {
      "LEFT": [
        {
          "frequency": 500,
          "threshold": 25.0,
          "type": "AC"
        }
      ],
      "RIGHT": [
        {
          "frequency": 500,
          "threshold": 30.0,
          "type": "AC"
        }
      ]
    },
    "sessionId": "session123",
    "deviceId": "丹麦国际听力电测听_session123",
    "deviceModel": "丹麦国际听力电测听",
    "timestamp": 1704678000000,
    "duration": 120000,
    "responseType": "MEASUREMENT_RESULT",
    "dataStatus": "PROCESSED",
    "hasError": false,
    "isComplete": true,
    "patientInfo": {
      "name": "张三",
      "examNo": "202501060001"
    },
    "examItemInfo": {
      "itemName": "电测听",
      "itemId": "AUDIOMETRY_001"
    },
    "extendedProperties": {
      "formattedResults": "听力测试结果...",
      "resultType": "AUDIOMETRY_TEST",
      "dataStorageType": "db",
      "dataSaved": true
    }
  }
}
```

### 查询状态事件

#### 查询完成事件
```json
{
  "event": "deviceStatus",
  "data": {
    "deviceId": "丹麦国际听力电测听_session123",
    "status": "QUERY_COMPLETED",
    "statusCode": "QUERY_COMPLETED",
    "message": "查询成功，找到测试结果",
    "hasResults": true,
    "timestamp": 1704678000000
  }
}
```

## 数据格式

### TonePoint数据结构
```java
public class TonePoint {
    private int frequency;      // 频率 (Hz)
    private double threshold;   // 阈值 (dB HL)
    private String type;        // 类型 (AC/BC)
    private String ear;         // 耳朵 (LEFT/RIGHT)
}
```

### 听力测试数据格式
```java
Map<String, Object> hearingTestData = {
    "acLeft": 25.0,     // 左耳气导阈值
    "acRight": 30.0,    // 右耳气导阈值
    "bcLeft": 20.0,     // 左耳骨导阈值
    "bcRight": 25.0,    // 右耳骨导阈值
    "ptaLeft": 28.0,    // 左耳平均听阈
    "ptaRight": 32.0    // 右耳平均听阈
};
```

## 配置参数

### 设备连接配置
```yaml
audiometry:
  device-connection:
    server-host: "localhost"
    server-port: 8080
    hook-port: 9092
    connection-timeout: 30
    retry-count: 3
    auto-reconnect: true
    api-key: "your-api-key-here"
```

### 监控配置
```yaml
audiometry:
  monitoring:
    interval: 30        # 监控间隔（秒）
    timeout: 300        # 超时时间（秒）
    max-error-count: 5  # 最大错误次数
```

### 数据存储配置
```yaml
audiometry:
  data-storage:
    storage-type: "db"           # db 或 transfer
    validation-enabled: true     # 是否启用数据验证
```

### 软件管理配置
```yaml
audiometry:
  software-management:
    auto-close-after-measurement: true    # 测量完成后自动关闭软件
    auto-start-before-measurement: true   # 新患者开始前自动启动软件
    close-wait-timeout: 10               # 软件关闭等待时间（秒）
    start-wait-timeout: 15               # 软件启动等待时间（秒）
```

## 错误处理

### 常见错误码

- `SERVER_CONNECTION_FAILED`: 服务器连接失败
- `DEVICE_NOT_FOUND`: 设备未找到
- `PATIENT_SETUP_FAILED`: 患者设置失败
- `SOFTWARE_START_FAILED`: 软件启动失败
- `DATA_PARSING_ERROR`: 数据解析错误
- `DATABASE_SAVE_ERROR`: 数据库保存错误

### 错误事件格式
```json
{
  "event": "deviceConnectionError",
  "data": {
    "deviceId": "丹麦国际听力电测听_session123",
    "error": "错误描述",
    "errorCode": "ERROR_CODE",
    "timestamp": 1704678000000
  }
}
```

## 使用示例

### 前端JavaScript示例

```javascript
// 连接Socket.IO
const socket = io('http://localhost:9093');

// 监听设备连接事件
socket.on('deviceConnected', (data) => {
    console.log('设备连接成功:', data);
});

// 监听测试结果事件
socket.on('testResult', (data) => {
    console.log('收到测试结果:', data);
    
    if (data.extendedProperties.dataStorageType === 'db') {
        console.log('数据已保存到数据库:', data.extendedProperties.dataSaved);
    }
});

// 发送连接请求
socket.emit('connectPort', {
    deviceProperties: {
        deviceModel: '丹麦国际听力电测听',
        comDeviceType: 'audiometry'
    },
    patientInfo: {
        name: '张三',
        examNo: '202501060001'
    },
    examItemInfo: {
        itemName: '电测听',
        itemId: 'AUDIOMETRY_001'
    }
});

// 发送查询命令
socket.emit('sendCommand', {
    command: 'QUERY_RESULTS'
});

// 启动软件
socket.emit('sendCommand', {
    command: 'START_SOFTWARE'
});

// 关闭软件
socket.emit('sendCommand', {
    command: 'STOP_SOFTWARE'
});

// 重置设备（会自动关闭软件）
socket.emit('sendCommand', {
    command: 'RESET'
});
```

### Java后端示例

```java
@Autowired
private AudiometryDeviceProcessor audiometryProcessor;

// 检查设备支持的功能
boolean supportsQuery = audiometryProcessor.supportsFeature("QUERY_RESULTS");

// 发送查询命令
boolean success = audiometryProcessor.sendCommand(context, "QUERY_RESULTS");

// 获取设备状态
String status = audiometryProcessor.getDeviceStatus(context);
```

## 注意事项

1. **网络连接**: 确保电测听服务器网络连接正常
2. **软件路径**: 确保配置的软件路径正确且有执行权限
3. **数据格式**: 确保XML数据格式符合解析器要求
4. **并发控制**: 数据库保存使用患者级别锁，避免并发冲突
5. **异步处理**: 报告图片处理是异步的，不会阻塞主流程
6. **错误重试**: 支持自动重试机制，提高系统稳定性
