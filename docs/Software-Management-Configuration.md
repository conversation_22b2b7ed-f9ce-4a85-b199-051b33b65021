# 听力设备软件管理配置指南

## 配置概述

新增了完整的软件管理配置，支持灵活的软件启动和关闭策略，确保听力设备软件的稳定运行。

## 配置文件位置

配置位于 `src/main/resources/application-audiometry.yml` 文件中：

```yaml
audiometry:
  software-management:
    # 基本配置
    auto-close-after-measurement: true      # 测量完成后自动关闭软件
    auto-start-before-measurement: true     # 新患者开始前自动启动软件
    close-before-start: true                # 启动前先关闭已运行的软件
    close-wait-timeout: 10                  # 软件关闭等待时间（秒）
    start-wait-timeout: 15                  # 软件启动等待时间（秒）
    run-as-admin: true                      # 是否以管理员权限启动软件
    auto-detect-admin-required: true        # 是否自动检测权限需求
    
    # 软件重启策略配置
    restart-strategy:
      enabled: true                         # 是否启用软件重启策略
      wait-before-restart: 2                # 重启前的等待时间（秒）
      wait-after-restart: 3                 # 重启后的等待时间（秒）
      max-restart-attempts: 3               # 最大重启尝试次数
```

## 配置项详细说明

### 1. 基本软件管理配置

#### `auto-close-after-measurement`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否在测量完成后自动关闭软件
- **建议**: 设置为 true，确保软件状态清洁

#### `auto-start-before-measurement`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否在新患者开始前自动启动软件
- **建议**: 设置为 true，确保软件就绪

#### `close-before-start`
- **类型**: boolean
- **默认值**: true
- **说明**: 启动前是否先关闭已运行的软件实例
- **建议**: 设置为 true，避免多实例冲突

#### `close-wait-timeout`
- **类型**: int
- **默认值**: 10
- **说明**: 软件关闭等待时间（秒）
- **建议**: 根据软件关闭速度调整，通常 10-15 秒

#### `start-wait-timeout`
- **类型**: int
- **默认值**: 15
- **说明**: 软件启动等待时间（秒）
- **建议**: 根据软件启动速度调整，通常 15-30 秒

#### `run-as-admin`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否以管理员权限启动软件
- **建议**: 如果软件安装在 C 盘，建议设置为 true

#### `auto-detect-admin-required`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否自动检测权限需求
- **建议**: 设置为 true，让系统自动判断

### 2. 软件重启策略配置

#### `restart-strategy.enabled`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否启用软件重启策略（每次测量前重启软件）
- **建议**: 设置为 true，确保软件状态一致性

#### `restart-strategy.wait-before-restart`
- **类型**: int
- **默认值**: 2
- **说明**: 重启前的等待时间（秒）
- **建议**: 2-5 秒，确保软件完全关闭

#### `restart-strategy.wait-after-restart`
- **类型**: int
- **默认值**: 3
- **说明**: 重启后的等待时间（秒）
- **建议**: 3-10 秒，确保软件完全启动

#### `restart-strategy.max-restart-attempts`
- **类型**: int
- **默认值**: 3
- **说明**: 最大重启尝试次数
- **建议**: 3-5 次，避免无限重试

## 使用场景配置

### 场景1：稳定环境（推荐配置）
```yaml
software-management:
  auto-close-after-measurement: true
  auto-start-before-measurement: true
  close-before-start: true
  restart-strategy:
    enabled: true
    wait-before-restart: 2
    wait-after-restart: 3
    max-restart-attempts: 3
```

### 场景2：快速测试环境
```yaml
software-management:
  auto-close-after-measurement: false      # 保持软件运行
  auto-start-before-measurement: true
  close-before-start: false               # 不重启软件
  restart-strategy:
    enabled: false                        # 禁用重启策略
```

### 场景3：问题排查环境
```yaml
software-management:
  auto-close-after-measurement: false
  auto-start-before-measurement: false     # 手动控制软件
  close-before-start: false
  restart-strategy:
    enabled: false
```

### 场景4：高可靠性环境
```yaml
software-management:
  auto-close-after-measurement: true
  auto-start-before-measurement: true
  close-before-start: true
  restart-strategy:
    enabled: true
    wait-before-restart: 5                # 更长等待时间
    wait-after-restart: 10
    max-restart-attempts: 5               # 更多重试次数
```

## 工作流程

### 启用重启策略时的流程
1. **检测现有软件**：检查是否有软件实例在运行
2. **关闭现有软件**：使用终极关闭方法关闭所有实例
3. **等待关闭完成**：等待 `wait-before-restart` 秒
4. **启动新软件**：使用配置的启动方法启动软件
5. **等待启动完成**：等待 `wait-after-restart` 秒
6. **验证启动结果**：检查进程是否真的在运行
7. **重试机制**：如果失败，重试最多 `max-restart-attempts` 次

### 传统启动流程
1. **可选关闭**：如果 `close-before-start` 为 true，先关闭现有软件
2. **HTTP启动**：尝试通过 HTTP 接口启动
3. **本地启动**：如果 HTTP 失败，使用本地路径启动
4. **权限处理**：根据配置使用普通或管理员权限

## 日志监控

启用 DEBUG 级别日志来监控软件管理过程：

```yaml
logging:
  level:
    org.bj.device.processors.AudiometryDeviceProcessor: DEBUG
    org.bj.util.WindowsProcessUtil: DEBUG
```

## 故障排除

### 1. 软件启动失败
- 检查软件路径是否正确
- 确认权限配置是否合适
- 增加启动等待时间

### 2. 软件关闭失败
- 检查进程是否有保护机制
- 尝试以管理员权限运行应用
- 增加关闭等待时间

### 3. 重启策略失效
- 检查 `restart-strategy.enabled` 是否为 true
- 增加等待时间配置
- 检查最大重试次数设置

## 性能优化建议

1. **合理设置等待时间**：根据实际软件启动/关闭速度调整
2. **适当的重试次数**：避免过多重试影响用户体验
3. **选择合适的策略**：根据使用场景选择配置
4. **监控日志**：定期检查软件管理日志，优化配置

现在软件管理功能更加灵活和可靠了！
