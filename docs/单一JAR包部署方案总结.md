# 单一JAR包 + 外部配置部署方案总结

## 🎯 方案目标

解决原有方案中"需要为不同工作站分别打包"的问题，实现：
- **一次构建，多处部署**
- **配置文件驱动的环境适配**
- **简化的部署和维护流程**

## ✅ 方案优势

### 1. 统一构建
- 只需构建一个通用 JAR 包 (`comReader.jar`)
- 所有工作站使用相同的代码版本
- 避免版本不一致和维护复杂性

### 2. 灵活配置
- 通过外部配置文件适配不同工作站
- 启动参数控制 Spring Profile 激活
- 配置覆盖机制：外部配置 > 内置配置

### 3. 自动化部署
- 提供一键部署脚本
- 自动生成工作站特定的启动脚本
- 标准化的目录结构

## 📁 文件结构

### 构建产物
```
target/
└── comReader.jar          # 通用JAR包
```

### 配置文件模板
```
config/
├── application-fgy200-workstation.yml      # 肺功能工作站配置
├── application-audiometry-workstation.yml  # 电测听工作站配置
└── application-mixed-workstation.yml       # 混合工作站配置
```

### 部署脚本
```
deploy/
├── deploy-fgy200-workstation.bat      # 肺功能工作站部署
└── deploy-audiometry-workstation.bat  # 电测听工作站部署
```

### 工作站部署结构
```
{workstation-name}/
├── comReader.jar       # 通用JAR包
├── application.yml     # 工作站特定配置
├── start.bat          # 启动脚本
├── stop.bat           # 停止脚本
└── logs/              # 日志目录
    └── workstation.log
```

## 🚀 使用流程

### 1. 构建阶段（开发人员）
```bash
# 构建通用JAR包
build.bat
# 或
mvn clean package -DskipTests
```

### 2. 部署阶段（运维人员）
```bash
# 肺功能工作站
deploy\deploy-fgy200-workstation.bat

# 电测听工作站
deploy\deploy-audiometry-workstation.bat
```

### 3. 运行阶段（工作站）
```bash
# 进入工作站目录
cd fgy200-workstation

# 启动服务
start.bat

# 停止服务
stop.bat
```

## 🔧 技术实现

### 1. Spring Boot 配置优先级
```
外部配置文件 > JAR包内配置文件 > 默认配置
```

### 2. Profile 激活机制
```bash
java -Dspring.profiles.active=fgy200 -jar comReader.jar
```

### 3. 配置文件命名约定
- 外部配置文件：`application.yml`（放在JAR包同目录）
- 内置配置文件：`application-{profile}.yml`（JAR包内）

## 📋 支持的工作站类型

| 工作站类型 | Spring Profile | 端口 | 支持设备 | 配置模板 |
|------------|----------------|------|----------|----------|
| 肺功能工作站 | fgy200 | 9092 | FGY200、ST150 | application-fgy200-workstation.yml |
| 电测听工作站 | audiometry | 9093 | 电测听设备 | application-audiometry-workstation.yml |
| 混合工作站 | production | 9092 | 所有设备 | application-mixed-workstation.yml |

## 🔍 验证结果

### 构建验证
- ✅ 成功构建通用JAR包：`target/comReader.jar`
- ✅ JAR包大小：约 61MB
- ✅ 包含所有必要依赖

### 配置验证
- ✅ 外部配置文件正确覆盖内置配置
- ✅ Spring Profile 正确激活：`The following 1 profile is active: "fgy200"`
- ✅ 设备处理器按Profile正确加载

### 部署验证
- ✅ 部署脚本正确创建工作站目录结构
- ✅ 启动脚本正确设置JVM参数
- ✅ 日志配置按工作站类型正确设置

## 📝 部署清单

### 开发人员交付物
1. `comReader.jar` - 通用JAR包
2. `config/` 目录 - 配置文件模板
3. `deploy/` 目录 - 部署脚本
4. 部署文档

### 工作站部署要求
1. Java 17+ 运行环境
2. 对应的设备软件（如需要）
3. 网络连接（访问数据库和MinIO）
4. 足够的磁盘空间（日志和临时文件）

## 🎉 方案效果

### 解决的问题
- ❌ **原问题**：需要为不同工作站分别打包
- ✅ **新方案**：一个JAR包适配所有工作站

### 带来的好处
1. **简化构建**：从多次构建变为一次构建
2. **统一版本**：避免版本不一致问题
3. **灵活部署**：通过配置文件适配环境
4. **易于维护**：标准化的部署结构
5. **快速扩展**：新增工作站类型只需添加配置模板

## 🔮 未来扩展

### 添加新工作站类型
1. 创建新的配置模板：`config/application-{type}-workstation.yml`
2. 创建对应的部署脚本：`deploy/deploy-{type}-workstation.bat`
3. 更新文档

### 配置管理优化
1. 可考虑使用配置中心（如Spring Cloud Config）
2. 支持环境变量覆盖配置
3. 配置文件加密和安全管理

## 📚 相关文档

- [Maven-Profile配置说明.md](./Maven-Profile配置说明.md) - 详细的配置说明
- [FGY200JdbcTemplate替换说明.md](./FGY200JdbcTemplate替换说明.md) - 数据库连接优化
- [环境感知部署方案说明.md](./环境感知部署方案说明.md) - Profile隔离方案

---

**总结**：新方案成功实现了"单一JAR包 + 外部配置"的部署模式，完美解决了原有的分别打包问题，大大简化了构建和部署流程。
