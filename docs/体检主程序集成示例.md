# 体检主程序集成示例

## 数据查询服务示例

```java
@Service
public class AudiometryRawDataService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 查询待生成PDF的记录
     */
    public List<AudiometryRawData> getPendingPdfRecords() {
        String sql = """
            SELECT * FROM audiometry_raw_data 
            WHERE status = 'PENDING_PDF' 
            ORDER BY create_time ASC
            LIMIT 50
            """;
        
        return jdbcTemplate.query(sql, new AudiometryRawDataRowMapper());
    }
    
    /**
     * 根据体检号查询最新原始数据
     */
    public AudiometryRawData getLatestByExamNo(String examNo) {
        String sql = """
            SELECT * FROM audiometry_raw_data 
            WHERE exam_no = ? 
            ORDER BY create_time DESC 
            LIMIT 1
            """;
        
        List<AudiometryRawData> results = jdbcTemplate.query(sql, 
            new AudiometryRawDataRowMapper(), examNo);
        
        return results.isEmpty() ? null : results.get(0);
    }
    
    /**
     * 更新PDF生成状态
     */
    public void updatePdfStatus(String id, String status, String pdfUrl, String errorMessage) {
        String sql = """
            UPDATE audiometry_raw_data 
            SET status = ?, pdf_url = ?, pdf_generated_time = ?, 
                error_message = ?, retry_count = retry_count + 1,
                update_time = NOW()
            WHERE id = ?
            """;
        
        jdbcTemplate.update(sql, status, pdfUrl, 
            "PDF_GENERATED".equals(status) ? new Date() : null,
            errorMessage, id);
    }
}
```

## 数据实体类示例

```java
@Data
public class AudiometryRawData {
    private String id;
    private String examNo;
    private String customerRegId;
    private String customerRegItemGroupId;
    private String patientName;
    private String patientIdCard;
    private String deviceModel;
    private String rawDataJson;
    private LocalDate testDate;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createdBy;
    private String status;
    private String pdfUrl;
    private LocalDateTime pdfGeneratedTime;
    private String errorMessage;
    private Integer retryCount;
    
    /**
     * 解析原始数据JSON
     */
    public Map<String, List<TonePoint>> parseRawData() {
        // JSON解析逻辑
        ObjectMapper mapper = new ObjectMapper();
        try {
            TypeReference<Map<String, List<TonePoint>>> typeRef = 
                new TypeReference<Map<String, List<TonePoint>>>() {};
            return mapper.readValue(rawDataJson, typeRef);
        } catch (Exception e) {
            throw new RuntimeException("解析原始数据失败", e);
        }
    }
}

@Data
public class TonePoint {
    private Integer frequency;
    private Integer intensityMT;
    private Integer intensityMTMasked;
    private String statusMT;
    private Integer intensityUT;
    private String statusUT;
    private String comment;
    private String transducer;
    
    /**
     * 获取有效的听力阈值
     */
    public Integer getEffectiveThreshold() {
        if (intensityUT != null && intensityUT != -2147483648) {
            return intensityUT;
        } else if (intensityMT != null && intensityMT != -2147483648) {
            return intensityMT;
        }
        return null;
    }
}
```

## PDF生成服务示例

```java
@Service
public class AudiometryPdfGeneratorService {
    
    @Autowired
    private AudiometryRawDataService rawDataService;
    
    @Autowired
    private FileUploadService fileUploadService;
    
    /**
     * 生成PDF报告
     */
    public boolean generatePdfReport(AudiometryRawData rawData) {
        try {
            // 1. 解析原始数据
            Map<String, List<TonePoint>> toneData = rawData.parseRawData();
            
            // 2. 生成PDF
            byte[] pdfBytes = createPdfReport(rawData, toneData);
            
            // 3. 上传文件
            String fileName = String.format("audiometry_%s_%d.pdf", 
                rawData.getExamNo(), System.currentTimeMillis());
            String pdfUrl = fileUploadService.uploadPdf(pdfBytes, fileName);
            
            // 4. 更新状态
            rawDataService.updatePdfStatus(rawData.getId(), "PDF_GENERATED", pdfUrl, null);
            
            return true;
            
        } catch (Exception e) {
            // 更新失败状态
            rawDataService.updatePdfStatus(rawData.getId(), "PDF_FAILED", null, e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建PDF报告
     */
    private byte[] createPdfReport(AudiometryRawData rawData, Map<String, List<TonePoint>> toneData) {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // 1. 绘制页眉
                drawHeader(contentStream, rawData);
                
                // 2. 绘制患者信息
                drawPatientInfo(contentStream, rawData);
                
                // 3. 绘制听力图表
                drawAudiogram(contentStream, toneData);
                
                // 4. 绘制数据表格
                drawDataTable(contentStream, toneData);
                
                // 5. 绘制诊断结论
                drawConclusion(contentStream, toneData);
                
                // 6. 绘制页脚
                drawFooter(contentStream, rawData);
            }
            
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.save(baos);
            return baos.toByteArray();
            
        } catch (Exception e) {
            throw new RuntimeException("创建PDF失败", e);
        }
    }
    
    /**
     * 绘制听力图表
     */
    private void drawAudiogram(PDPageContentStream contentStream, Map<String, List<TonePoint>> toneData) 
            throws IOException {
        
        // 图表区域定义
        float chartX = 50;
        float chartY = 400;
        float chartWidth = 500;
        float chartHeight = 300;
        
        // 绘制坐标轴
        drawAxes(contentStream, chartX, chartY, chartWidth, chartHeight);
        
        // 绘制频率标签（X轴）
        int[] frequencies = {125, 250, 500, 750, 1000, 1500, 2000, 3000, 4000, 6000, 8000};
        drawFrequencyLabels(contentStream, chartX, chartY, chartWidth, frequencies);
        
        // 绘制听力阈值标签（Y轴）
        drawThresholdLabels(contentStream, chartX, chartY, chartHeight);
        
        // 绘制右耳数据（红色O标记）
        List<TonePoint> rightEarData = toneData.get("Right_AC");
        if (rightEarData != null) {
            drawEarData(contentStream, rightEarData, chartX, chartY, chartWidth, chartHeight, 
                       Color.RED, "O", frequencies);
        }
        
        // 绘制左耳数据（蓝色X标记）
        List<TonePoint> leftEarData = toneData.get("Left_AC");
        if (leftEarData != null) {
            drawEarData(contentStream, leftEarData, chartX, chartY, chartWidth, chartHeight, 
                       Color.BLUE, "X", frequencies);
        }
    }
    
    /**
     * 计算听力损失程度
     */
    private String calculateHearingLossLevel(List<TonePoint> tonePoints) {
        if (tonePoints == null || tonePoints.isEmpty()) {
            return "无数据";
        }
        
        // 计算PTA（500Hz, 1000Hz, 2000Hz的平均值）
        double pta = calculatePTA(tonePoints);
        
        if (pta <= 25) {
            return "正常听力";
        } else if (pta <= 40) {
            return "轻度听力损失";
        } else if (pta <= 55) {
            return "中度听力损失";
        } else if (pta <= 70) {
            return "中重度听力损失";
        } else if (pta <= 90) {
            return "重度听力损失";
        } else {
            return "极重度听力损失";
        }
    }
    
    private double calculatePTA(List<TonePoint> tonePoints) {
        int[] targetFrequencies = {500, 1000, 2000};
        double sum = 0.0;
        int count = 0;
        
        for (int frequency : targetFrequencies) {
            for (TonePoint point : tonePoints) {
                if (point.getFrequency() != null && point.getFrequency() == frequency) {
                    Integer threshold = point.getEffectiveThreshold();
                    if (threshold != null) {
                        sum += threshold;
                        count++;
                        break;
                    }
                }
            }
        }
        
        return count > 0 ? sum / count : 0.0;
    }
}
```

## 定时任务示例

```java
@Component
public class AudiometryPdfScheduledTask {
    
    @Autowired
    private AudiometryRawDataService rawDataService;
    
    @Autowired
    private AudiometryPdfGeneratorService pdfGeneratorService;
    
    /**
     * 每30秒扫描一次待生成PDF的记录
     */
    @Scheduled(fixedDelay = 30000)
    public void processPendingPdfTasks() {
        try {
            List<AudiometryRawData> pendingRecords = rawDataService.getPendingPdfRecords();
            
            if (pendingRecords.isEmpty()) {
                return;
            }
            
            log.info("开始处理电测听PDF生成任务，待处理记录数: {}", pendingRecords.size());
            
            for (AudiometryRawData record : pendingRecords) {
                try {
                    // 检查重试次数
                    if (record.getRetryCount() >= 3) {
                        rawDataService.updatePdfStatus(record.getId(), "PDF_FAILED", 
                            null, "超过最大重试次数");
                        continue;
                    }
                    
                    // 生成PDF
                    boolean success = pdfGeneratorService.generatePdfReport(record);
                    
                    if (success) {
                        log.info("电测听PDF生成成功: 体检号={}", record.getExamNo());
                    } else {
                        log.warn("电测听PDF生成失败: 体检号={}", record.getExamNo());
                    }
                    
                } catch (Exception e) {
                    log.error("处理电测听PDF生成任务异常: 体检号={}", record.getExamNo(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("电测听PDF定时任务执行异常", e);
        }
    }
}
