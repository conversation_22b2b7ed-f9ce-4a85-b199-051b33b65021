# 单一JAR包 + 外部配置部署方案

## 概述

本项目采用**单一JAR包 + 外部配置文件**的部署方案，解决了不同工作站需要分别打包的问题。通过外部配置文件和启动参数，一个JAR包可以适配所有工作站环境。

## 方案优势

### 1. 统一构建
- ✅ **一次构建，多处部署**：只需构建一个通用JAR包
- ✅ **减少维护成本**：无需为不同环境分别打包
- ✅ **避免版本混乱**：所有工作站使用相同的代码版本

### 2. 灵活配置
- ✅ **外部配置文件**：工作站特定配置通过外部文件提供
- ✅ **启动参数控制**：通过 `-Dspring.profiles.active` 指定环境
- ✅ **配置覆盖机制**：外部配置自动覆盖内置配置

### 3. 简化部署
- ✅ **自动化部署脚本**：提供一键部署脚本
- ✅ **标准化目录结构**：统一的工作站部署结构
- ✅ **便捷启停脚本**：自动生成启动和停止脚本

## 支持的工作站类型

| 工作站类型 | Spring Profile | Socket.IO 端口 | 支持设备 | 配置文件 |
|------------|----------------|----------------|----------|----------|
| 肺功能工作站 | fgy200 | 9092 | 仅肺功能设备 | application-fgy200-workstation.yml |
| 电测听工作站 | audiometry | 9093 | 仅电测听设备 | application-audiometry-workstation.yml |
| 混合工作站 | production | 9092 | 所有设备 | application-mixed-workstation.yml |

## 部署架构

### 1. 构建阶段
```
源代码 → Maven构建 → 通用JAR包 (comReader.jar)
```

### 2. 部署阶段
```
通用JAR包 + 外部配置文件 → 工作站特定环境
```

### 3. 目录结构
```
工作站目录/
├── comReader.jar           # 通用JAR包
├── application.yml         # 工作站特定配置
├── start.bat              # 启动脚本
├── stop.bat               # 停止脚本
└── logs/                  # 日志目录
    └── workstation.log    # 工作站日志
```

## 配置文件说明

### 1. 内置配置文件
- `application.yml` - 基础配置（JAR包内）
- `application-fgy200.yml` - 肺功能环境配置（JAR包内）
- `application-audiometry.yml` - 电测听环境配置（JAR包内）
- `application-production.yml` - 生产环境配置（JAR包内）

### 2. 外部配置文件模板
- `config/application-fgy200-workstation.yml` - 肺功能工作站配置模板
- `config/application-audiometry-workstation.yml` - 电测听工作站配置模板
- `config/application-mixed-workstation.yml` - 混合工作站配置模板

## 使用方法

### 1. 构建通用JAR包

```bash
# 使用构建脚本（推荐）
build.bat

# 或使用Maven命令
mvn clean package -DskipTests
```

构建完成后，在 `target/` 目录下生成 `comReader.jar` 文件。

### 2. 自动化部署

使用提供的部署脚本进行一键部署：

```bash
# 部署肺功能工作站
deploy\deploy-fgy200-workstation.bat

# 部署电测听工作站
deploy\deploy-audiometry-workstation.bat
```

部署脚本会自动：
- 创建工作站目录
- 复制JAR文件和配置文件
- 生成启动和停止脚本
- 创建日志目录

### 3. 手动部署

如果需要手动部署：

```bash
# 1. 创建工作站目录
mkdir fgy200-workstation

# 2. 复制JAR文件
copy target\comReader.jar fgy200-workstation\

# 3. 复制并重命名配置文件
copy config\application-fgy200-workstation.yml fgy200-workstation\application.yml

# 4. 启动服务
cd fgy200-workstation
java -Dspring.profiles.active=fgy200 -jar comReader.jar
```

### 4. 启动不同环境

```bash
# 肺功能工作站
java -Dspring.profiles.active=fgy200 -jar comReader.jar

# 电测听工作站
java -Dspring.profiles.active=audiometry -jar comReader.jar

# 混合环境工作站
java -Dspring.profiles.active=production -jar comReader.jar
```

## 配置验证

### 1. 检查活跃的 Profile

```bash
mvn help:active-profiles -Pfgy200
```

### 2. 验证资源过滤

构建后检查 `target/classes/application.yml` 文件，确认占位符已被正确替换。

### 3. 运行时验证

启动应用时，日志会显示：
```
The following 1 profile is active: "fgy200"
```

## 配置示例

### FGY200 Profile 构建结果

```yaml
spring:
  profiles:
    active: fgy200

socketio:
  port: 9092

app:
  description: "设备通信服务 - 肺功能专用环境"
  environment: "fgy200"

device:
  type: "lung-function"
  enabled:
    fgy200: true
    audiometry: false
```

### Audiometry Profile 构建结果

```yaml
spring:
  profiles:
    active: audiometry

socketio:
  port: 9093

app:
  description: "设备通信服务 - 电测听专用环境"
  environment: "audiometry"

device:
  type: "audiometry"
  enabled:
    fgy200: false
    audiometry: true
```

## 优势

### 1. 环境隔离
- 每个环境有独立的配置
- 避免配置冲突和错误

### 2. 自动化构建
- 一键构建不同环境版本
- 减少手动配置错误

### 3. 部署简化
- 不同环境使用不同的 JAR 文件
- 配置已内置，无需外部配置文件

### 4. 开发效率
- 开发环境有详细日志配置
- 生产环境有优化的性能配置

## 注意事项

### 1. 端口配置
不同环境使用不同端口，避免冲突：
- FGY200: 9092
- Audiometry: 9093
- Development: 9094

### 2. 资源过滤
确保 `pom.xml` 中的资源配置启用了过滤：
```xml
<filtering>true</filtering>
```

### 3. Profile 激活
- 生产环境 Profile 默认激活
- 其他环境需要显式指定

### 4. 配置一致性
- Maven 属性与 Spring 配置保持一致
- 避免硬编码配置值

## 扩展

### 添加新环境

1. 在 `pom.xml` 中添加新的 Profile
2. 创建对应的 `application-{profile}.yml` 文件
3. 创建对应的构建脚本
4. 更新文档

### 添加新属性

1. 在 Profile 的 `<properties>` 中定义
2. 在 `application.yml` 中使用占位符
3. 在环境特定配置文件中提供默认值
