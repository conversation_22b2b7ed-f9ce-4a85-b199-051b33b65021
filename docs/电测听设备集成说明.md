# 电测听设备集成说明

## 概述

本文档描述了电测听设备的完整集成方案，包括配置文件、数据保存服务、报告图片处理等功能的实现。

## 功能特性

### 1. 设备软件管理
- 支持通过HTTP接口启动设备软件
- 支持通过本地路径启动设备软件（使用WindowsProcessUtil）
- 双重启动机制确保设备软件能够正常启动
- **自动软件管理**：测量完成后自动关闭软件，下次测量前自动启动
- 支持手动控制软件启动和关闭
- 软件状态跟踪，避免重复启动

### 2. 数据存储模式
- **transfer模式**：直接传输数据到前端，不保存到数据库
- **db模式**：保存数据到数据库，并发送通知到前端

### 3. 数据保存功能
- 保存测量结果到 `customer_reg_item_result` 表
- 更新项目组状态到 `customer_reg_item_group` 表
- 支持异常规则评估和标志设置
- 支持医生信息配置

### 4. 报告图片处理
- 异步处理报告图片上传
- 支持多种图片格式（png, jpg等）
- 自动扫描预定义文件名
- 上传到MinIO对象存储
- 定时任务处理和重试机制

## 配置文件

### 主配置文件修改

在 `application.yml` 中添加电测听配置：

```yaml
spring:
  profiles:
    include: fgy200, audiometry
```

### 电测听专用配置

配置文件：`application-audiometry.yml`

主要配置项：
- **程序路径**：电测听软件安装路径
- **设备连接**：服务器地址、端口、API Key等
- **监控配置**：监控间隔、超时时间等
- **报告配置**：图片保存目录、格式、文件名模式等
- **医生配置**：检查、报告、审核医生信息
- **指标配置**：听力测试指标和异常规则

## 核心类说明

### 1. AudiometryProperties
配置属性类，负责绑定配置文件中的所有配置项。

**主要属性：**
- `programPath`: 程序安装路径
- `softwareExecutable`: 可执行文件名
- `deviceConnection`: 设备连接配置
- `monitoring`: 监控配置
- `report`: 报告配置
- `doctorConfig`: 医生配置
- `hearingTest`: 听力测试配置

### 2. AudiometryService
数据保存服务类，负责将电测听结果保存到主数据库。

**主要功能：**
- 保存测量结果到 `customer_reg_item_result` 表
- 更新项目组状态到 `customer_reg_item_group` 表
- 支持患者级别的并发控制
- 异步处理报告图片

**关键方法：**
- `saveAudiometryResult()`: 保存电测听结果
- `saveAudiometryItemResults()`: 保存测量结果详情
- `updateCustomerRegItemGroupStatus()`: 更新项目组状态

### 3. AudiometryReportImageService
报告图片处理服务，负责异步处理报告图片的上传和管理。

**主要功能：**
- 定时扫描报告图片文件
- 上传图片到MinIO对象存储
- 更新数据库中的图片URL
- 支持重试和超时机制

**关键方法：**
- `addReportImageTask()`: 添加图片处理任务
- `processReportImageTasks()`: 定时处理任务
- `scanAndUploadReportImages()`: 扫描并上传图片

### 4. HearingTestExpressionService
听力测试表达式服务，负责评估听力测试指标的异常规则。

**主要功能：**
- 使用Spring Expression Language评估异常规则
- 支持复杂的条件表达式
- 返回异常标志、描述和符号

### 5. AudiometryDeviceProcessor（修改）
电测听设备处理器的主要修改：

**新增功能：**
- 集成WindowsProcessUtil启动设备软件
- 支持数据库存储模式
- 数据格式转换（TonePoint -> 听力测试数据）
- 集成AudiometryService保存数据
- **自动软件管理**：测量完成后自动关闭，下次测量前自动启动
- 软件状态跟踪和控制

**关键修改：**
- `startDeviceSoftware()`: 双重启动机制，状态跟踪
- `stopDeviceSoftware()`: 软件关闭功能
- `sendParsedResults()`: 支持数据库存储
- `convertToHearingTestData()`: 数据格式转换
- `calculatePTA()`: 计算纯音平均听阈
- `processTestResults()`: 测量完成后自动关闭软件

## 数据流程

### 1. 设备连接流程
1. 检查服务器连接
2. 注册Hook钩子
3. 设置诊所信息
4. 设置检查员信息
5. 设置患者信息
6. 启动设备软件（HTTP接口 + 本地启动）
7. 注册监控任务

### 2. 数据处理流程
1. 查询最新测试结果
2. 解析Base64编码的XML数据
3. 转换为听力测试数据格式
4. 根据存储模式处理：
   - **transfer模式**：直接发送到前端
   - **db模式**：保存到数据库 + 发送通知

### 3. 数据保存流程
1. 使用患者锁防止并发冲突
2. 删除已有结果
3. 查询指标配置信息
4. 评估异常规则
5. 批量保存到 `customer_reg_item_result` 表
6. 更新 `customer_reg_item_group` 表状态
7. 添加报告图片处理任务

### 4. 报告图片处理流程
1. 定时扫描待处理任务
2. 检查预定义文件名是否存在
3. 上传图片到MinIO
4. 更新数据库中的图片URL
5. 支持重试和超时处理

## 配置示例

### 基本配置
```yaml
audiometry:
  program-path: "C:/Program Files/Interacoustics/OtoAccess"
  software-executable: "OtoAccess.exe"
  his-code: "audiometry"
  
  data-storage:
    storage-type: "db"  # 或 "transfer"
    validation-enabled: true
```

### 报告配置
```yaml
audiometry:
  report:
    image-directory: "C:/AudiometryReports"
    image-format: "png"
    file-name-pattern: "{examNo}.{序号}"
    process-interval: 30
    max-retry-count: 5
```

### 医生配置
```yaml
audiometry:
  doctor-config:
    check-doctor:
      code: "AUDIOMETRY_CHECK_DOC"
      name: "电测听检查医生"
    report-doctor:
      code: "AUDIOMETRY_REPORT_DOC"
      name: "电测听报告医生"
```

## 异常规则配置

支持基于Spring Expression Language的复杂异常规则：

```yaml
audiometry:
  hearing-test:
    indicators:
      - code: "PTA_LEFT"
        name: "左耳平均听阈"
        abnormal-rules:
          - name: "轻度听力损失"
            condition: "#value >= 26 and #value <= 40"
            flag: "1"
            description: "轻度听力损失"
            symbol: "↓"
```

## 注意事项

### 1. 依赖关系
- 需要WindowsProcessUtil工具类
- 需要FileUploadUtil（MinIO上传）
- 需要PatientLockManager（并发控制）
- 需要Spring Expression Language支持

### 2. 数据库表结构
确保以下表存在且结构正确：
- `customer_reg_item_result`: 测量结果表
- `customer_reg_item_group`: 项目组表
- `item_info`: 指标信息表

### 3. 文件权限
确保应用有权限：
- 读取电测听软件安装目录
- 读取报告图片目录
- 启动外部程序

### 4. 网络配置
确保网络连接正常：
- 电测听服务器连接
- MinIO对象存储连接
- 数据库连接

## 测试建议

### 1. 单元测试
- 配置属性绑定测试
- 数据转换逻辑测试
- 异常规则评估测试

### 2. 集成测试
- 设备连接流程测试
- 数据保存流程测试
- 报告图片处理测试

### 3. 性能测试
- 并发数据保存测试
- 大量图片处理测试
- 长时间运行稳定性测试

## 故障排查

### 1. 设备软件启动失败
- 检查程序路径配置
- 检查文件权限
- 查看WindowsProcessUtil日志

### 2. 数据保存失败
- 检查数据库连接
- 检查表结构和权限
- 查看并发锁状态

### 3. 图片上传失败
- 检查MinIO配置
- 检查文件路径和权限
- 查看网络连接状态

## 版本历史

- **v1.0.0** (2025-07-09): 初始版本，支持基本的电测听设备集成功能
