# 简化环境感知部署方案说明

## 问题背景

中间件需要部署在不同的工作站上：
- **电测听工作站**：只有电测听设备软件，没有肺功能软件
- **肺功能工作站**：只有肺功能设备软件，没有电测听软件
- **混合工作站**：同时具备多种设备软件

### 原始错误

在电测听工作站上启动时会出现以下错误：
```
org.springframework.beans.factory.UnsatisfiedDependencyException:
Error creating bean with name 'FGY200DeviceProcessor':
Unsatisfied dependency expressed through field 'FGY200Service':
Error creating bean with name 'FGY200Service':
Unsatisfied dependency expressed through field 'fgy200JdbcTemplate':
No qualifying bean of type 'org.springframework.jdbc.core.JdbcTemplate' available
```

**错误原因**：
1. FGY200DeviceProcessor 依赖 FGY200Service
2. FGY200Service 依赖 fgy200JdbcTemplate
3. fgy200JdbcTemplate 依赖 FGY200DatabaseConfig
4. FGY200DatabaseConfig 需要 fgy200.database.enabled=true 和数据库文件存在
5. 电测听工作站没有肺功能数据库文件，导致整个依赖链失败

## 解决方案

### 核心思路：使用Spring Profile完全隔离

**简化原则**：
1. 使用Spring Profile直接控制Bean加载，无需复杂的条件判断
2. 每个环境有独立的配置文件，完全隔离
3. 移除所有可选依赖注入，让Spring自动处理

### 1. Profile驱动的Bean加载

使用`@Profile`注解控制Bean的加载：

**肺功能相关组件**（使用`@Profile("!audiometry")`）：
- `FGY200DeviceProcessor`：肺功能设备处理器
- `FGY200DatabaseConfig`：肺功能数据库配置
- `FGY200Service`：肺功能数据服务
- `FGY200ReportImageService`：肺功能报告图片服务
- `LungFunctionExpressionService`：肺功能表达式解析服务
- `FGY200Properties`：肺功能配置属性
- `FGY200ScheduledTaskConfig`：肺功能定时任务配置

**电测听相关组件**（使用`@Profile("!lung-function")`）：
- `AudiometryDeviceProcessor`：电测听设备处理器
- `AudiometryService`：电测听数据服务
- `AudiometryReportImageService`：电测听报告图片服务
- `HearingTestExpressionService`：听力测试表达式服务
- `AudiometryProperties`：电测听配置属性
- `AudiometryScheduledTaskConfig`：电测听定时任务配置

### 2. 配置文件完全分离

每个环境有独立的配置文件，不包含其他环境的配置：
- `application-audiometry.yml`：只包含电测听配置
- `application-lung-function.yml`：只包含肺功能配置

### 3. 配置文件示例

#### 电测听环境配置 (`application-audiometry.yml`)
```yaml
# 电测听工作站专用配置
socketio:
  hostname: 0.0.0.0
  port: 9092

# 电测听设备配置
audiometry:
  programPath: "C:/Program Files/Interacoustics/OtoAccess"
  softwareExecutable: "OtoAccess.exe"
  # ... 其他电测听配置

# 注意：不包含任何FGY200相关配置
```

#### 肺功能环境配置 (`application-lung-function.yml`)
```yaml
# 肺功能工作站专用配置
socketio:
  hostname: 0.0.0.0
  port: 9092

# FGY200肺功能设备配置
fgy200:
  programPath: "D:"
  databaseFile: "fei.mdb"
  database:
    enabled: true
    path: "D:/fei.mdb"
  # ... 其他肺功能配置

# 注意：不包含任何电测听相关配置
```

## 部署方式

### 方式一：使用专用启动脚本

#### 电测听工作站
```bash
# 使用电测听专用启动脚本
start-audiometry.bat
```

#### 肺功能工作站
```bash
# 使用肺功能专用启动脚本
start-lung-function.bat
```

#### 混合环境工作站
```bash
# 使用混合环境启动脚本
start-mixed.bat
```

### 方式二：手动指定Profile

```bash
# 电测听环境
java -Dspring.profiles.active=audiometry -jar target/comReader.jar

# 肺功能环境
java -Dspring.profiles.active=lung-function -jar target/comReader.jar

# 混合环境
java -Dspring.profiles.active=production -jar target/comReader.jar
```

## 配置文件优先级

Spring Boot配置文件加载顺序：
1. `application.yml` (基础配置)
2. `application-{profile}.yml` (环境特定配置)

环境特定配置会覆盖基础配置中的相同项。

## 健康检查机制

### 启动时检查
- 只对启用的设备进行健康检查
- 未启用的设备处理器不会被加载，因此不会进行检查

### 运行时监控
- 全局设备监控服务只监控已注册的设备处理器
- 未注册的设备不会被监控

## 日志输出

启动时会输出当前环境信息：
```
开始注册设备处理器...
当前部署环境: audiometry
启用的设备类型: [audiometry]
已注册丹麦国际听力电测听设备处理器: 丹麦国际听力电测听设备处理器
FGY200设备处理器未启用或未找到，跳过注册
=== 设备处理器注册摘要 ===
部署环境: audiometry
已注册处理器数量: 1
支持的设备类型: 丹麦国际听力电测听
```

## 优势

1. **彻底隔离**：使用Profile完全隔离不同环境的Bean，无任何交叉依赖
2. **配置简洁**：移除复杂的条件判断，配置文件更清晰
3. **启动稳定**：消除因缺少设备软件导致的启动失败
4. **维护简单**：每个环境独立配置，互不影响
5. **Spring原生**：使用Spring Boot标准的Profile机制，符合最佳实践

## 注意事项

1. **配置路径**：确保各环境配置文件中的软件路径正确
2. **权限要求**：电测听软件可能需要管理员权限
3. **端口冲突**：确保不同环境使用的端口不冲突
4. **日志分离**：不同环境使用不同的日志文件名

## 故障排除

### 启动失败
1. 检查配置文件语法是否正确
2. 确认软件路径是否存在
3. 查看启动日志中的错误信息

### 设备无法连接
1. 确认设备软件是否正常运行
2. 检查配置文件中的连接参数
3. 验证网络端口是否可用

### 配置不生效
1. 确认使用了正确的Profile
2. 检查配置文件的优先级
3. 查看启动日志确认加载的配置
