# MinIO健康检查问题排查指南

## 问题现象

健康检查返回400 Bad Request，这通常表示MinIO服务配置或状态有问题。

## 正常的MinIO健康检查响应

### 标准健康检查端点

| 端点 | 用途 | 正常响应 |
|------|------|----------|
| `/minio/health/live` | 存活检查 | 200 OK |
| `/minio/health/ready` | 就绪检查 | 200 OK |
| `/minio/health/cluster` | 集群健康 | 200 OK |
| `/` | 根路径 | 403 Forbidden（正常，需要认证） |

### 异常响应码含义

| 响应码 | 含义 | 可能原因 |
|--------|------|----------|
| 400 | Bad Request | 请求格式错误、配置问题 |
| 403 | Forbidden | 需要认证（根路径正常） |
| 404 | Not Found | 端点不存在、版本不支持 |
| 500 | Internal Error | 服务内部错误 |
| 503 | Service Unavailable | 服务不可用、启动中 |

## 排查步骤

### 1. 检查MinIO服务状态

```bash
# 检查MinIO进程
ps aux | grep minio

# 检查端口监听
netstat -tlnp | grep 9000

# 检查服务日志
tail -f /var/log/minio/minio.log
```

### 2. 手动测试健康检查端点

```bash
# 测试存活检查
curl -v http://*************:9000/minio/health/live

# 测试就绪检查
curl -v http://*************:9000/minio/health/ready

# 测试根路径（应该返回403）
curl -v http://*************:9000/

# 测试API端点
curl -v http://*************:9000/minio/admin/v3/info
```

### 3. 检查MinIO配置

```bash
# 检查MinIO配置文件
cat /etc/default/minio

# 检查环境变量
env | grep MINIO

# 检查存储路径权限
ls -la /data/minio
```

### 4. 网络连接测试

```bash
# 测试端口连通性
telnet ************* 9000

# 测试DNS解析
nslookup *************

# 检查防火墙规则
iptables -L | grep 9000
```

## 常见问题和解决方案

### 问题1: 健康检查返回400

**可能原因:**
- MinIO版本过旧，不支持健康检查端点
- 配置文件格式错误
- 存储路径权限问题
- 服务启动不完整

**解决方案:**
```bash
# 1. 检查MinIO版本
minio --version

# 2. 重启MinIO服务
systemctl restart minio

# 3. 检查配置文件
sudo nano /etc/default/minio

# 4. 修复存储路径权限
sudo chown -R minio:minio /data/minio
sudo chmod -R 755 /data/minio
```

### 问题2: Connection reset during upload

**可能原因:**
- 网络不稳定
- MinIO服务器负载过高
- 客户端超时设置过短
- 防火墙中断连接

**解决方案:**
```yaml
# 优化客户端配置
minio:
  connect-timeout: 120
  write-timeout: 600
  read-timeout: 300
  max-retry-count: 5
  retry-interval: 3000
```

### 问题3: 服务启动失败

**检查启动日志:**
```bash
# 查看系统日志
journalctl -u minio -f

# 查看MinIO日志
tail -f /var/log/minio/minio.log
```

**常见启动问题:**
- 端口被占用
- 存储路径不存在
- 权限不足
- 配置文件语法错误

## MinIO服务配置示例

### 标准配置文件 (/etc/default/minio)

```bash
# MinIO服务配置
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=your-secure-password
MINIO_VOLUMES="/data/minio"
MINIO_OPTS="--console-address :9001"

# 网络优化配置
MINIO_API_REQUESTS_MAX=1000
MINIO_API_REQUESTS_DEADLINE=10m
MINIO_API_CLUSTER_DEADLINE=10m

# 健康检查配置
MINIO_HEALTH_CHECK_INTERVAL=30s
```

### Docker部署配置

```yaml
version: '3.8'
services:
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=admin
      - MINIO_ROOT_PASSWORD=password
      - MINIO_API_REQUESTS_MAX=1000
      - MINIO_API_REQUESTS_DEADLINE=10m
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
```

## 监控和维护

### 1. 健康检查脚本

```bash
#!/bin/bash
# minio-health-check.sh

MINIO_URL="http://*************:9000"
ENDPOINTS=("/minio/health/live" "/minio/health/ready")

for endpoint in "${ENDPOINTS[@]}"; do
    response=$(curl -s -o /dev/null -w "%{http_code}" "$MINIO_URL$endpoint")
    if [ "$response" = "200" ]; then
        echo "✅ $endpoint: OK"
    else
        echo "❌ $endpoint: $response"
    fi
done
```

### 2. 性能监控

```bash
# 监控MinIO性能指标
curl -s http://*************:9000/minio/admin/v3/info \
  -H "Authorization: Bearer YOUR_TOKEN"

# 监控存储使用情况
df -h /data/minio

# 监控网络连接
ss -tuln | grep 9000
```

### 3. 日志分析

```bash
# 分析错误日志
grep -i "error\|fail\|connection reset" /var/log/minio/minio.log

# 统计请求类型
awk '{print $7}' /var/log/minio/access.log | sort | uniq -c

# 监控响应时间
tail -f /var/log/minio/access.log | grep -E "PUT|GET"
```

## 应急处理

### 服务异常时的处理步骤

1. **立即检查**
   ```bash
   systemctl status minio
   curl -I http://*************:9000/
   ```

2. **重启服务**
   ```bash
   systemctl restart minio
   sleep 10
   curl http://*************:9000/minio/health/live
   ```

3. **检查资源**
   ```bash
   df -h
   free -m
   top -p $(pgrep minio)
   ```

4. **恢复验证**
   ```bash
   # 测试基本功能
   mc ls myminio/
   echo "test" | mc pipe myminio/test.txt
   mc cat myminio/test.txt
   ```

通过这些排查步骤，可以快速定位MinIO健康检查400错误的根本原因并进行修复。
