# MinIO最佳实践使用指南

## 重构后的功能特性

### 🚀 核心改进

1. **智能上传策略**
   - 标准文件（<64MB）：标准上传，性能最优
   - 大文件（≥64MB）：优化缓冲区上传，提高传输效率

2. **优化上传处理**
   - 大文件使用更大缓冲区（64KB）
   - 自动选择最佳上传参数
   - 内存友好的流式处理

3. **完整性校验**
   - 自动计算MD5哈希值
   - 上传前后数据完整性验证
   - 元数据记录文件信息

4. **预签名URL支持**
   - 客户端直接上传
   - 安全的临时访问权限
   - 自定义有效期

5. **流式上传**
   - 支持未知大小的数据流
   - 内存友好的流式处理
   - 适合实时数据上传

## 使用示例

### 1. 基本文件上传

```java
@Autowired
private MinioService minioService;

// 简单上传
File file = new File("report.pdf");
Map<String, Object> result = minioService.uploadFile(file, "reports/2025/report.pdf");

if (Boolean.TRUE.equals(result.get("success"))) {
    String downloadUrl = (String) result.get("downloadUrl");
    String etag = (String) result.get("etag");
    String uploadMethod = (String) result.get("uploadMethod"); // "standard" 或 "large-file"
    System.out.println("上传成功: " + downloadUrl);
}
```

### 2. 带自定义元数据的上传

```java
// 添加自定义元数据
Map<String, String> metadata = new HashMap<>();
metadata.put("department", "cardiology");
metadata.put("patient-id", "P123456");
metadata.put("exam-type", "ecg");

Map<String, Object> result = minioService.uploadFile(file, "medical/ecg_report.pdf", metadata);
```

### 3. 字节数组上传

```java
// PDF报告字节数组上传
byte[] pdfData = generatePdfReport();
Map<String, String> metadata = new HashMap<>();
metadata.put("report-type", "audiometry");
metadata.put("generated-by", "system");

Map<String, Object> result = minioService.uploadBytes(
    pdfData, 
    "audiometry_report.pdf", 
    "reports/audiometry/" + examNo + ".pdf",
    metadata
);
```

### 4. 流式上传

```java
// 流式上传（适合大数据流）
try (InputStream dataStream = getDataStream()) {
    Map<String, String> metadata = new HashMap<>();
    metadata.put("source", "device");
    metadata.put("timestamp", String.valueOf(System.currentTimeMillis()));
    
    Map<String, Object> result = minioService.uploadStream(
        dataStream,
        "device_data.bin",
        "device-data/" + deviceId + "/" + timestamp + ".bin",
        "application/octet-stream",
        metadata
    );
}
```

### 5. 预签名URL使用

```java
// 获取下载URL（7天有效期）
String downloadUrl = minioService.getDownloadUrl("images", "photo.jpg");

// 获取自定义有效期的下载URL
String downloadUrl = minioService.getDownloadUrl("images", "photo.jpg", 1, TimeUnit.HOURS);

// 获取预签名上传URL（客户端直接上传）
String uploadUrl = minioService.getPresignedUploadUrl("uploads", "client-file.jpg", 30, TimeUnit.MINUTES);
```

## 性能优化特性

### 1. 自动分片策略

```
文件大小判断：
├── < 64MB → 标准上传（最快）
└── ≥ 64MB → 大文件优化上传
    ├── 缓冲区：64KB
    ├── 分片大小：16MB
    └── 流式处理：内存友好
```

### 2. 内存优化

- **缓冲流处理**：减少内存占用
- **分片并发**：避免大文件占用过多内存
- **流式上传**：支持无限大小的数据流

### 3. 网络优化

- **连接池复用**：减少连接建立开销
- **自动重试**：网络异常自动恢复
- **超时配置**：适应不同网络环境

## 错误处理和监控

### 1. 上传结果检查

```java
Map<String, Object> result = minioService.uploadFile(file, objectName);

if (Boolean.TRUE.equals(result.get("success"))) {
    // 上传成功
    String etag = (String) result.get("etag");
    String md5Hash = (String) result.get("md5Hash");
    String uploadMethod = (String) result.get("uploadMethod");
    
    // 验证完整性
    if (md5Hash != null) {
        // 可以与本地计算的MD5对比
    }
} else {
    // 上传失败
    String errorMessage = (String) result.get("message");
    Integer retryCount = (Integer) result.get("retryCount");
    logger.error("上传失败: {}, 重试次数: {}", errorMessage, retryCount);
}
```

### 2. 分片上传监控

```java
// 日志输出示例
2025-07-12 10:30:15 INFO  - 文件大小128MB，使用分片上传: large_file.zip
2025-07-12 10:30:15 INFO  - 开始分片上传: 文件大小=128MB, 分片大小=16MB, 分片数量=8
2025-07-12 10:30:16 DEBUG - 分片1上传成功: size=16384KB
2025-07-12 10:30:16 DEBUG - 分片2上传成功: size=16384KB
...
2025-07-12 10:30:20 INFO  - 分片上传完成: bucket=files, object=large_file.zip, 分片数=8
```

## 配置优化建议

### 1. 应用配置

```yaml
minio:
  enabled: true
  endpoint: http://localhost:9000
  access-key: your-access-key
  secret-key: your-secret-key
  default-bucket: files
  # 工作站环境优化配置
  connect-timeout: 60
  write-timeout: 300
  read-timeout: 120
  max-retry-count: 3
  retry-interval: 1000
```

### 2. JVM参数优化

```bash
# 针对大文件上传的JVM优化
-Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
```

## 最佳实践建议

### 1. 文件命名规范

```java
// 推荐的对象命名格式
String objectName = String.format("%s/%s/%s_%d.%s", 
    category,           // 分类：reports, images, data
    date,              // 日期：2025/01/12
    examNo,            // 体检号
    timestamp,         // 时间戳
    extension          // 扩展名
);
// 示例：reports/2025/01/12/E202501120001_1736668800000.pdf
```

### 2. 元数据使用

```java
// 标准元数据字段
Map<String, String> metadata = new HashMap<>();
metadata.put("original-filename", file.getName());     // 原始文件名
metadata.put("content-category", "medical-report");    // 内容分类
metadata.put("patient-id", patientId);                // 患者ID
metadata.put("exam-no", examNo);                       // 体检号
metadata.put("department", "cardiology");             // 科室
metadata.put("created-by", userId);                   // 创建人
metadata.put("retention-period", "7-years");          // 保留期限
```

### 3. 错误处理策略

```java
public String uploadWithFallback(File file, String objectName) {
    // 主要上传方式
    Map<String, Object> result = minioService.uploadFile(file, objectName);
    
    if (Boolean.TRUE.equals(result.get("success"))) {
        return (String) result.get("downloadUrl");
    }
    
    // 降级处理：本地存储
    try {
        String localPath = saveToLocalStorage(file);
        logger.warn("MinIO上传失败，已保存到本地: {}", localPath);
        return localPath;
    } catch (Exception e) {
        logger.error("本地存储也失败", e);
        throw new RuntimeException("文件存储完全失败", e);
    }
}
```

### 4. 监控指标

```java
// 关键监控指标
- 上传成功率
- 平均上传时间
- 分片上传比例
- 重试次数统计
- 存储空间使用率
- 网络传输速度
```

## 故障排查

### 1. 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 分片上传失败 | 网络不稳定 | 增加重试次数，检查网络 |
| 内存溢出 | 文件过大 | 调整JVM参数，使用流式上传 |
| 上传超时 | 超时设置过短 | 增加write-timeout配置 |
| MD5校验失败 | 数据传输错误 | 启用重试机制，检查网络质量 |

### 2. 性能调优

```java
// 根据文件类型调整策略
if (isImageFile(fileName)) {
    // 图片文件：优先单次上传
    return uploadSmallFile(file, bucketName, objectName, contentType, metadata);
} else if (isVideoFile(fileName)) {
    // 视频文件：强制分片上传
    return uploadLargeFile(file, bucketName, objectName, contentType, metadata);
}
```

通过这些最佳实践，可以显著提升MinIO上传的稳定性、性能和可维护性。
