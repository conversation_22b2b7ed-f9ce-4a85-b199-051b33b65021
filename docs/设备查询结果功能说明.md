# 设备查询结果功能说明

## 概述

本文档说明了体检设备查询结果功能的实现，包括统一的查询接口、设备处理器的查询逻辑以及前端事件处理。

## 功能特性

### 1. 统一查询接口
- 提供统一的`handleQueryResults`方法处理所有设备的查询请求
- 支持临时上下文创建，无需实际设备连接
- 自动校验设备是否支持查询功能
- 统一的状态反馈和错误处理

### 2. 设备处理器查询支持
- **FGY200DeviceProcessor**: 支持从Access数据库查询肺功能检测结果
- **AudiometryDeviceProcessor**: 支持通过HTTP接口查询电测听结果
- 所有处理器都实现了`QUERY_RESULTS`指令支持

### 3. 状态事件反馈
- 查询开始状态：`QUERY_STARTED`
- 查询进行状态：`QUERY`
- 查询完成状态：`QUERY_COMPLETED`
- 查询结果响应：`QUERY_RESULTS_RESPONSE`

## 实现架构

### 核心组件

#### 1. DeviceHandler.handleQueryResults()
```java
public void handleQueryResults(SocketIOClient client, ConnectRequest request, String userId)
```

**功能流程：**
1. 创建查询上下文（临时上下文，状态为QUERYING）
2. 查找对应的设备处理器
3. 校验设备是否支持查询功能（`supportsFeature("QUERY_RESULTS")`）
4. 发送查询开始状态
5. 执行查询指令（`sendCommand(context, "QUERY_RESULTS")`）
6. 发送查询状态反馈

#### 2. 设备处理器查询实现

##### FGY200DeviceProcessor.queryLatestResults()
- 从肺功能Access数据库查询最新检测结果
- 支持按患者ID和检查日期查询
- 自动保存结果到体检主数据库
- 发送`testResult`事件和查询状态

##### AudiometryDeviceProcessor.queryLatestResults()
- 通过HTTP接口获取最新测试会话
- 解析Base64编码的XML测试数据
- 发送解析后的纯音测试结果
- 发送查询状态反馈

#### 3. DeviceStatusSender查询状态方法

##### 新增方法：
- `sendQueryStarted()`: 发送查询开始状态
- `sendQueryCompleted()`: 发送查询完成状态
- `sendQueryResultsResponse()`: 发送查询结果响应事件

## 前端集成

### 事件监听

#### 1. 发送查询请求
```javascript
// 发送查询测量结果请求
socket.emit('queryResults', {
    deviceProperties: {
        deviceModel: 'FGY-200',  // 或 'Audiometry'
        deviceId: 'device_001'
    },
    patientInfo: {
        examNo: 'P001',
        name: '张三'
    },
    examItemInfo: {
        itemCode: 'LUNG_FUNCTION'  // 或 'AUDIOMETRY'
    }
});
```

#### 2. 监听查询状态事件
```javascript
// 监听设备状态事件（包含查询状态）
socket.on('deviceStatus', (data) => {
    if (data.operation === 'QUERY_STARTED') {
        console.log('查询开始:', data.statusMessage);
    } else if (data.operation === 'QUERY_COMPLETED') {
        console.log('查询完成:', data.statusMessage, '成功:', data.operationSuccess);
    } else if (data.operation === 'QUERY') {
        console.log('查询状态:', data.statusMessage, '成功:', data.operationSuccess);
    }
});

// 监听查询结果响应事件
socket.on('queryResultsResponse', (data) => {
    console.log('查询结果响应:', data);
    if (data.success && data.data) {
        // 处理查询到的结果数据
        handleQueryResults(data.data);
    }
});

// 监听测试结果事件（实际的测量数据）
socket.on('testResult', (data) => {
    console.log('收到测试结果:', data);
    // 处理测试结果数据
    handleTestResult(data);
});
```

## 配置要求

### 1. 设备处理器配置
设备处理器必须在`supportsFeature()`方法中声明支持查询功能：

```java
@Override
public boolean supportsFeature(String feature) {
    return switch (feature) {
        case "QUERY_RESULTS" -> true;  // 声明支持查询功能
        // ... 其他功能
        default -> false;
    };
}
```

### 2. 查询指令支持
设备处理器必须在`sendCommand()`方法中支持`QUERY_RESULTS`指令：

```java
@Override
public boolean sendCommand(DeviceContext context, String command) {
    return switch (command.toUpperCase()) {
        case "QUERY_RESULTS" -> queryLatestResults(context);
        // ... 其他指令
        default -> false;
    };
}
```

## 错误处理

### 常见错误情况
1. **设备型号为空**: 抛出`IllegalArgumentException`
2. **未找到设备处理器**: 抛出`IllegalStateException`
3. **设备不支持查询**: 抛出`UnsupportedOperationException`
4. **查询执行失败**: 发送失败状态，记录错误日志

### 错误状态反馈
- 所有错误都会通过`deviceStatus`事件发送给前端
- 错误信息包含具体的错误描述和错误代码
- 查询失败时会发送`QUERY`操作的失败状态

## 使用示例

### 查询FGY-200肺功能结果
```javascript
socket.emit('queryResults', {
    deviceProperties: {
        deviceModel: 'FGY-200',
        deviceId: 'fgy200_001'
    },
    patientInfo: {
        examNo: 'P20250709001',
        name: '张三'
    },
    examItemInfo: {
        itemCode: 'LUNG_FUNCTION',
        itemName: '肺功能检查'
    }
});
```

### 查询电测听结果
```javascript
socket.emit('queryResults', {
    deviceProperties: {
        deviceModel: 'Audiometry',
        deviceId: 'audio_001'
    },
    patientInfo: {
        examNo: 'P20250709002',
        name: '李四'
    },
    examItemInfo: {
        itemCode: 'AUDIOMETRY',
        itemName: '电测听检查'
    }
});
```

## 注意事项

1. **查询上下文**: 查询操作使用临时上下文，不需要实际的设备连接
2. **状态管理**: 查询过程中上下文状态为`QUERYING`
3. **结果格式**: 查询结果通过`testResult`事件发送，格式与实时测量结果一致
4. **并发处理**: 支持多个用户同时查询不同设备的结果
5. **超时处理**: 查询操作有内置的超时机制，避免长时间阻塞

## 扩展支持

### 添加新设备查询支持
1. 在设备处理器中实现`queryLatestResults()`方法
2. 在`supportsFeature()`中声明支持`QUERY_RESULTS`
3. 在`sendCommand()`中添加`QUERY_RESULTS`指令处理
4. 确保查询结果通过`testResult`事件发送
5. 添加适当的状态反馈调用

### 自定义查询参数
可以通过`ConnectRequest`中的`patientInfo`和`examItemInfo`传递查询参数，设备处理器可以根据这些参数进行精确查询。
