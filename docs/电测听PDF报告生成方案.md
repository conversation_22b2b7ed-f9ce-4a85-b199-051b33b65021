# 电测听PDF报告生成方案

## 方案概述

电测听PDF报告生成采用分离式架构：
- **中间件职责**：负责设备通信、数据采集、原始数据存储
- **主程序职责**：负责PDF报告生成、文件管理、报告展示

## 数据存储架构

### 1. 聚合数据存储
存储在 `customer_reg_item_result` 表中，包含：
- 左右耳平均听阈（PTA）
- 气导阈值
- 异常判断结果
- 参考范围

### 2. 原始数据存储
存储在 `audiometry_raw_data` 表中，包含：
- 完整的TonePoint数据（JSON格式）
- 患者基本信息
- 测试时间和设备信息
- PDF生成状态

## 数据库表结构

### audiometry_raw_data表字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | VARCHAR(36) | 主键ID |
| exam_no | VARCHAR(50) | 体检号 |
| customer_reg_id | VARCHAR(36) | 客户登记ID |
| customer_reg_item_group_id | VARCHAR(36) | 体检项目组ID |
| patient_name | VARCHAR(100) | 患者姓名 |
| patient_id_card | VARCHAR(20) | 患者身份证号 |
| device_model | VARCHAR(100) | 设备型号 |
| raw_data_json | TEXT | 原始TonePoint数据JSON |
| test_date | DATE | 测试日期 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |
| created_by | VARCHAR(50) | 创建人 |
| status | VARCHAR(20) | 状态 |
| pdf_url | VARCHAR(500) | PDF报告URL |
| pdf_generated_time | DATETIME | PDF生成时间 |
| error_message | TEXT | 错误信息 |
| retry_count | INT | 重试次数 |

### 状态说明
- `PENDING_PDF`: 待生成PDF
- `PDF_GENERATED`: 已生成PDF
- `PDF_FAILED`: 生成失败

## 原始数据JSON格式

```json
{
  "Right_AC": [
    {
      "frequency": 250,
      "intensityMT": 15,
      "intensityMTMasked": 15,
      "statusMT": "Heard",
      "intensityUT": null,
      "statusUT": "Undefined",
      "comment": "",
      "transducer": "TDH39"
    },
    {
      "frequency": 500,
      "intensityMT": 25,
      "intensityMTMasked": 15,
      "statusMT": "Heard",
      "intensityUT": null,
      "statusUT": "Undefined",
      "comment": "",
      "transducer": "TDH39"
    }
  ],
  "Left_AC": [
    {
      "frequency": 250,
      "intensityMT": 20,
      "intensityMTMasked": 30,
      "statusMT": "Heard",
      "intensityUT": null,
      "statusUT": "Undefined",
      "comment": "",
      "transducer": "TDH39"
    }
  ]
}
```

## 体检主程序集成方案

### 1. 数据查询接口

体检主程序需要实现以下查询方法：

```sql
-- 查询待生成PDF的记录
SELECT * FROM audiometry_raw_data 
WHERE status = 'PENDING_PDF' 
ORDER BY create_time ASC;

-- 根据体检号查询原始数据
SELECT * FROM audiometry_raw_data 
WHERE exam_no = ? 
ORDER BY create_time DESC 
LIMIT 1;

-- 根据项目组ID查询原始数据
SELECT * FROM audiometry_raw_data 
WHERE customer_reg_item_group_id = ?;
```

### 2. PDF生成流程

1. **定时任务扫描** - 每30秒扫描一次待处理记录
2. **数据解析** - 解析JSON格式的原始TonePoint数据
3. **PDF生成** - 使用Apache PDFBox生成听力图表和报告
4. **文件上传** - 上传到MinIO或文件服务器
5. **状态更新** - 更新数据库状态和PDF URL

### 3. PDF报告内容

#### 报告结构
- **页眉**: 医院信息、报告标题
- **患者信息**: 姓名、性别、年龄、体检号、测试日期
- **听力图表**: 标准audiogram图表
  - X轴：频率（125Hz - 8000Hz）
  - Y轴：听力阈值（-10dB - 120dB）
  - 左耳：X标记，蓝色
  - 右耳：O标记，红色
- **数据表格**: 详细的频率-阈值数据
- **诊断结论**: 基于听力损失程度的自动判断
- **页脚**: 医生签名、报告日期

#### 听力损失程度判断
- 正常听力：≤25dB HL
- 轻度听力损失：26-40dB HL
- 中度听力损失：41-55dB HL
- 中重度听力损失：56-70dB HL
- 重度听力损失：71-90dB HL
- 极重度听力损失：>90dB HL

### 4. 错误处理

- **重试机制**: 失败后最多重试3次
- **错误记录**: 记录详细错误信息到error_message字段
- **状态管理**: 失败后标记为PDF_FAILED状态
- **监控告警**: 连续失败时发送告警通知

### 5. 配置参数

```yaml
audiometry:
  pdf-report:
    # PDF生成配置
    enabled: true
    scan-interval: 30  # 扫描间隔（秒）
    max-retry-count: 3  # 最大重试次数
    timeout: 300  # 生成超时时间（秒）
    
    # 报告样式配置
    template:
      page-size: "A4"
      font-family: "SimSun"
      font-size: 12
      chart-width: 400
      chart-height: 300
      
    # 文件存储配置
    storage:
      directory: "/reports/audiometry"
      file-name-pattern: "audiometry_{examNo}_{timestamp}.pdf"
```

## 实施步骤

### 第一阶段：数据库准备
1. 执行SQL脚本创建audiometry_raw_data表
2. 部署中间件更新，开始保存原始数据
3. 验证数据存储功能

### 第二阶段：主程序开发
1. 实现数据查询接口
2. 开发PDF生成器
3. 实现定时任务
4. 测试PDF生成功能

### 第三阶段：集成测试
1. 端到端测试
2. 性能测试
3. 错误处理测试
4. 生产环境部署

## 技术依赖

- **Apache PDFBox**: PDF生成和图表绘制
- **Jackson**: JSON数据解析
- **Spring Boot**: 定时任务和依赖注入
- **MySQL**: 数据存储
- **MinIO**: 文件存储（可选）

## 监控指标

- PDF生成成功率
- 平均生成时间
- 队列积压数量
- 错误率统计
- 存储空间使用情况
