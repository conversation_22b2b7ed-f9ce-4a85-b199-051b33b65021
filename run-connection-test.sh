#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "========================================"
echo "连接测试脚本"
echo "========================================"
echo

# 检查Java环境
echo "正在检查Java环境..."
if ! command -v java &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Java环境，请确保已安装Java 8或更高版本${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Java环境检查通过${NC}"
echo

# 检查Maven环境
echo "正在检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Maven环境，请确保已安装Maven${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Maven环境检查通过${NC}"
echo

# 主菜单函数
show_menu() {
    echo "========================================"
    echo "请选择要执行的测试:"
    echo "========================================"
    echo "1. 测试主数据库连接"
    echo "2. 测试MinIO服务连接"
    echo "3. 综合连接测试"
    echo "4. 运行所有连接测试"
    echo "5. 退出"
    echo "========================================"
}

# 测试主数据库连接
test_database() {
    echo
    echo -e "${BLUE}🔍 正在测试主数据库连接...${NC}"
    echo "========================================"
    
    if mvn test -Dtest=ConnectionTest#testMainDatabaseConnection -q; then
        echo -e "${GREEN}✓ 主数据库连接测试完成${NC}"
    else
        echo -e "${RED}❌ 主数据库连接测试失败${NC}"
    fi
    echo
    read -p "按回车键继续..."
}

# 测试MinIO服务连接
test_minio() {
    echo
    echo -e "${BLUE}🔍 正在测试MinIO服务连接...${NC}"
    echo "========================================"
    
    if mvn test -Dtest=ConnectionTest#testMinioConnection -q; then
        echo -e "${GREEN}✓ MinIO服务连接测试完成${NC}"
    else
        echo -e "${RED}❌ MinIO服务连接测试失败${NC}"
    fi
    echo
    read -p "按回车键继续..."
}

# 综合连接测试
test_all_connections() {
    echo
    echo -e "${BLUE}🔍 正在执行综合连接测试...${NC}"
    echo "========================================"
    
    if mvn test -Dtest=ConnectionTest#testAllConnections -q; then
        echo -e "${GREEN}✓ 综合连接测试完成${NC}"
    else
        echo -e "${RED}❌ 综合连接测试失败${NC}"
    fi
    echo
    read -p "按回车键继续..."
}

# 运行所有测试
test_all() {
    echo
    echo -e "${BLUE}🔍 正在运行所有连接测试...${NC}"
    echo "========================================"
    
    if mvn test -Dtest=ConnectionTest -q; then
        echo -e "${GREEN}✓ 所有连接测试完成${NC}"
    else
        echo -e "${RED}❌ 部分连接测试失败${NC}"
    fi
    echo
    read -p "按回车键继续..."
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-5): " choice
    
    case $choice in
        1)
            test_database
            ;;
        2)
            test_minio
            ;;
        3)
            test_all_connections
            ;;
        4)
            test_all
            ;;
        5)
            echo
            echo -e "${GREEN}👋 测试完成，感谢使用！${NC}"
            exit 0
            ;;
        *)
            echo -e "${YELLOW}无效选项，请重新选择${NC}"
            echo
            ;;
    esac
done
