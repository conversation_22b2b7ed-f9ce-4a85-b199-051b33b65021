@echo off
chcp 65001 > nul
echo ========================================
echo 肺功能工作站部署脚本
echo ========================================
echo.

set WORKSTATION_DIR=%~dp0fgy200-workstation
set JAR_FILE=comReader.jar
set CONFIG_FILE=application.yml

echo 1. 创建工作站目录...
if not exist "%WORKSTATION_DIR%" (
    mkdir "%WORKSTATION_DIR%"
    echo ✅ 创建目录: %WORKSTATION_DIR%
) else (
    echo ℹ️  目录已存在: %WORKSTATION_DIR%
)

echo.
echo 2. 复制 JAR 文件...
if exist "..\target\%JAR_FILE%" (
    copy "..\target\%JAR_FILE%" "%WORKSTATION_DIR%\"
    echo ✅ 复制 JAR 文件成功
) else (
    echo ❌ JAR 文件不存在，请先构建项目
    echo    运行命令: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo.
echo 3. 复制配置文件...
if exist "..\config\application-fgy200-workstation.yml" (
    copy "..\config\application-fgy200-workstation.yml" "%WORKSTATION_DIR%\%CONFIG_FILE%"
    echo ✅ 复制配置文件成功
) else (
    echo ❌ 配置文件不存在
    pause
    exit /b 1
)

echo.
echo 4. 创建启动脚本...
(
echo @echo off
echo chcp 65001 ^> nul
echo echo ========================================
echo echo 启动肺功能工作站服务
echo echo ========================================
echo echo.
echo echo 当前配置：肺功能专用环境
echo echo - Spring Profile: fgy200
echo echo - Socket.IO 端口: 9092
echo echo - 支持设备: 仅肺功能设备
echo echo.
echo echo 正在启动服务...
echo java -Dspring.profiles.active=fgy200 -Dfile.encoding=UTF-8 -jar %JAR_FILE%
echo.
echo echo 服务已停止
echo pause
) > "%WORKSTATION_DIR%\start.bat"
echo ✅ 创建启动脚本成功

echo.
echo 5. 创建停止脚本...
(
echo @echo off
echo chcp 65001 ^> nul
echo echo 正在停止肺功能工作站服务...
echo taskkill /f /im java.exe 2^>nul
echo echo 服务已停止
echo pause
) > "%WORKSTATION_DIR%\stop.bat"
echo ✅ 创建停止脚本成功

echo.
echo 6. 创建日志目录...
if not exist "%WORKSTATION_DIR%\logs" (
    mkdir "%WORKSTATION_DIR%\logs"
    echo ✅ 创建日志目录成功
)

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 部署位置: %WORKSTATION_DIR%
echo.
echo 文件列表:
dir /b "%WORKSTATION_DIR%"
echo.
echo 使用说明:
echo 1. 进入目录: cd "%WORKSTATION_DIR%"
echo 2. 启动服务: start.bat
echo 3. 停止服务: stop.bat
echo 4. 查看日志: logs\fgy200-workstation.log
echo.
echo 注意事项:
echo - 确保端口 9092 未被占用
echo - 确保 FGY200 数据库文件路径正确
echo - 首次启动可能需要配置数据库连接
echo.

pause
